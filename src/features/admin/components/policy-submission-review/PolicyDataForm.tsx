"use client";

import { useState } from "react";
import { useSidebar } from "@/components/ui/sidebar";
import { useForm } from "react-hook-form";
// import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  PolicyType,
  PaymentPeriod,
  AssetsType,
  FuelType,
  UsageType,
  KmRange,
  GarageType,
  PartyRole,
  GuaranteeType,
} from "@prisma/client";
import { ArrowLeft, ArrowRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { PolicyData } from "@/features/admin/hooks/usePolicySubmissionReview";
import { InsuredPartiesAccordion } from "./InsuredPartiesAccordion";
import { CoveragesManagement } from "./CoveragesManagement";
import { DateTimePicker } from "./DateTimePicker";
import { AddPartyModal } from "./AddPartyModal";
import { useFieldArray } from "react-hook-form";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

// Export the schema so the parent component can initialize the form
export const policySchema = z
  .object({
    policyNumber: z.string().min(1, "Número de póliza es requerido"),
    insurerName: z.string().min(1, "Aseguradora es requerida"),
    policyType: z.nativeEnum(PolicyType).nullable(),
    productName: z.string().nullable(),
    startDate: z.coerce.date().nullable(),
    endDate: z.coerce.date().nullable(),
    paymentPeriod: z.nativeEnum(PaymentPeriod).nullable(),
    premium: z.number().nullable(),
    insuredParties: z.array(
      z.object({
        personId: z.string().optional(),
        fullName: z.string().min(1, "Nombre es requerido"),
        dni: z.string().min(1, "DNI/NIE/NIF es requerido"),
        roles: z.array(z.nativeEnum(PartyRole)),
      })
    ),
    brand: z.string().min(1, "Marca es requerida"),
    model: z.string().min(1, "Modelo es requerido"),
    year: z.number().nullable(),
    version: z.string().nullable(),
    chassisNumber: z.string().min(1, "Número de bastidor es requerido"),
    licensePlate: z.string().min(1, "Matrícula es requerida"),
    firstRegistrationDate: z.coerce.date().nullable(),
    type: z.nativeEnum(AssetsType).nullable(),
    fuelType: z.nativeEnum(FuelType).nullable(),
    usageType: z.nativeEnum(UsageType).nullable(),
    kmPerYear: z.nativeEnum(KmRange).nullable(),
    garageType: z.nativeEnum(GarageType).nullable(),
    seats: z.number().nullable(),
    powerCv: z.number().nullable(),
    isLeased: z.boolean(),
    coverages: z.array(
      z.object({
        id: z.string().optional(),
        type: z.nativeEnum(GuaranteeType),
        customName: z.string().optional(),
        limit: z.number().optional(),
        deductible: z.number().optional(),
        description: z.string().optional(),
      })
    ),
  })
  .refine(
    (data) => {
      if (data.startDate && data.endDate) {
        return data.endDate > data.startDate;
      }
      return true;
    },
    {
      message: "Fecha de vencimiento debe ser posterior a la fecha de inicio",
      path: ["endDate"],
    }
  );

interface PolicyDataFormProps {
  onDataUpdate: (data: PolicyData) => void;
  onBack: () => void;
  onContinue: () => void;
  form: ReturnType<typeof useForm<z.infer<typeof policySchema>>>;
}

export function PolicyDataForm({
  onDataUpdate,
  onBack,
  onContinue,
  form,
}: PolicyDataFormProps) {
  const { state } = useSidebar();
  const [activeTab, setActiveTab] = useState("policy");

  const { fields, append, remove, update } = useFieldArray({
    control: form.control,
    name: "insuredParties",
  });

  const [editingIndex, setEditingIndex] = useState<number | null>(null);

  const handleEdit = (index: number) => setEditingIndex(index);

  const handleUpdate = (data: any) => {
    if (editingIndex !== null) {
      update(editingIndex, data);
      setEditingIndex(null);
    }
  };

  const onSubmit: any = (data: PolicyData) => {
    onDataUpdate(data);
    onContinue();
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 pb-24">
        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className="space-y-6"
        >
          <div className="sticky top-[216px] z-20 bg-background pb-4">
            <TabsList className="grid w-full grid-cols-2 lg:grid-cols-4 text-muted-foreground">
              <TabsTrigger value="policy">Póliza</TabsTrigger>
              <TabsTrigger value="insured">Partes Aseguradas</TabsTrigger>
              <TabsTrigger value="asset">Vehículo</TabsTrigger>
              <TabsTrigger value="coverages">Coberturas</TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="policy" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Datos de la Póliza</CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="policyNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Número de Póliza</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="00123456789"
                          {...field}
                          value={field.value ?? ""}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="insurerName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Aseguradora</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Mafre"
                          {...field}
                          value={field.value ?? ""}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="productName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nombre del Producto</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Terceros Flex"
                          {...field}
                          value={field.value ?? ""}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="policyType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tipo de Póliza</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value ?? ""}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecciona un tipo de póliza" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="THIRD_PARTY">
                            Terceros Básico
                          </SelectItem>
                          <SelectItem value="THIRD_PARTY_EXTENDED">
                            Terceros Ampliado
                          </SelectItem>
                          <SelectItem value="COMPREHENSIVE">
                            Todo Riesgo
                          </SelectItem>
                          <SelectItem value="COMPREHENSIVE_WITH_DEDUCTIBLE">
                            Todo Riesgo Franquicia
                          </SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="startDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Fecha de Inicio</FormLabel>
                      <FormControl>
                        <DateTimePicker
                          value={field.value}
                          onChange={field.onChange}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="endDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Fecha de Vencimiento</FormLabel>
                      <FormControl>
                        <DateTimePicker
                          value={field.value}
                          onChange={field.onChange}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="paymentPeriod"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Forma de Pago</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value ?? ""}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecciona una forma de pago" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="ANNUAL">Anual</SelectItem>
                          <SelectItem value="SEMIANNUAL">Semestral</SelectItem>
                          <SelectItem value="QUARTERLY">Trimestral</SelectItem>
                          <SelectItem value="MONTHLY">Mensual</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="premium"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Prima Anual (€)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          placeholder="350.50"
                          {...field}
                          value={field.value ?? ""}
                          onChange={(e) =>
                            field.onChange(
                              e.target.value === ""
                                ? null
                                : parseFloat(e.target.value.replace(",", "."))
                            )
                          }
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="insured" className="space-y-6">
            <Card>
              <CardHeader className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                <CardTitle>Partes Aseguradas</CardTitle>
                <AddPartyModal
                  onAdd={append}
                  onEdit={handleUpdate}
                  editingParty={
                    editingIndex !== null ? fields[editingIndex] : null
                  }
                  editingIndex={editingIndex}
                  setEditingIndex={setEditingIndex}
                />
              </CardHeader>
              <CardContent>
                <InsuredPartiesAccordion
                  fields={fields}
                  onEdit={handleEdit}
                  onRemove={remove}
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="asset" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Información del Vehículo</CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="brand"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Marca</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Volkswagen"
                          {...field}
                          value={field.value ?? ""}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="model"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Modelo</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Golf"
                          {...field}
                          value={field.value ?? ""}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="year"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Año</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="2022"
                          {...field}
                          value={field.value ?? ""}
                          onChange={(e) =>
                            field.onChange(
                              e.target.value === ""
                                ? null
                                : parseInt(e.target.value, 10)
                            )
                          }
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="seats"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Plazas</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="5"
                          {...field}
                          value={field.value ?? ""}
                          onChange={(e) =>
                            field.onChange(
                              e.target.value === ""
                                ? null
                                : parseInt(e.target.value, 10)
                            )
                          }
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="powerCv"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Potencia (CV)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="150"
                          {...field}
                          value={field.value ?? ""}
                          onChange={(e) =>
                            field.onChange(
                              e.target.value === ""
                                ? null
                                : parseFloat(e.target.value)
                            )
                          }
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="chassisNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Bastidor / VIN</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="WVWZZZAUZNP123456"
                          {...field}
                          value={field.value ?? ""}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="licensePlate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Matrícula</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="1234 ABC"
                          {...field}
                          value={field.value ?? ""}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="firstRegistrationDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Fecha 1ª matriculación</FormLabel>
                      <FormControl>
                        <DateTimePicker
                          value={field.value}
                          onChange={field.onChange}
                          showTime={false}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tipo de vehículo</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value ?? ""}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecciona un tipo" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="CAR">Coche</SelectItem>
                          <SelectItem value="MOTORCYCLE">Moto</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="fuelType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Combustible</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value ?? ""}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecciona un combustible" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="GASOLINE">Gasolina</SelectItem>
                          <SelectItem value="DIESEL">Diesel</SelectItem>
                          <SelectItem value="ELECTRIC">Eléctrico</SelectItem>
                          <SelectItem value="HYBRID">Híbrido</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="usageType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Uso</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value ?? ""}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecciona un tipo de uso" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="PRIVATE_OCCASIONAL">
                            Particular Ocasional
                          </SelectItem>
                          <SelectItem value="PRIVATE_REGULAR">
                            Particular Regular
                          </SelectItem>
                          <SelectItem value="PROFESSIONAL_OCCASIONAL">
                            Profesional Ocasional
                          </SelectItem>
                          <SelectItem value="PROFESSIONAL_REGULAR">
                            Profesional Regular
                          </SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="kmPerYear"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Km/año</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value ?? ""}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecciona un rango de km" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="UP_TO_2000">
                            Hasta 2.000
                          </SelectItem>
                          <SelectItem value="FROM_2000_TO_4000">
                            2.000 a 4.000
                          </SelectItem>
                          <SelectItem value="FROM_4000_TO_6000">
                            4.000 a 6.000
                          </SelectItem>
                          <SelectItem value="FROM_6000_TO_8000">
                            6.000 a 8.000
                          </SelectItem>
                          <SelectItem value="FROM_8000_TO_10000">
                            8.000 a 10.000
                          </SelectItem>
                          <SelectItem value="FROM_10000_TO_12000">
                            10.000 a 12.000
                          </SelectItem>
                          <SelectItem value="FROM_12000_TO_14000">
                            12.000 a 14.000
                          </SelectItem>
                          <SelectItem value="FROM_14000_TO_16000">
                            14.000 a 16.000
                          </SelectItem>
                          <SelectItem value="FROM_16000_TO_18000">
                            16.000 a 18.000
                          </SelectItem>
                          <SelectItem value="FROM_18000_TO_20000">
                            18.000 a 20.000
                          </SelectItem>
                          <SelectItem value="FROM_20000_TO_22000">
                            20.000 a 22.000
                          </SelectItem>
                          <SelectItem value="FROM_22000_TO_24000">
                            22.000 a 24.000
                          </SelectItem>
                          <SelectItem value="FROM_24000_TO_26000">
                            24.000 a 26.000
                          </SelectItem>
                          <SelectItem value="FROM_26000_TO_28000">
                            26.000 a 28.000
                          </SelectItem>
                          <SelectItem value="FROM_28000_TO_30000">
                            28.000 a 30.000
                          </SelectItem>
                          <SelectItem value="FROM_30000_TO_32000">
                            30.000 a 32.000
                          </SelectItem>
                          <SelectItem value="FROM_32000_TO_34000">
                            32.000 a 34.000
                          </SelectItem>
                          <SelectItem value="FROM_34000_TO_36000">
                            34.000 a 36.000
                          </SelectItem>
                          <SelectItem value="FROM_36000_TO_38000">
                            36.000 a 38.000
                          </SelectItem>
                          <SelectItem value="FROM_38000_TO_40000">
                            38.000 a 40.000
                          </SelectItem>
                          <SelectItem value="FROM_40000_TO_45000">
                            40.000 a 45.000
                          </SelectItem>
                          <SelectItem value="FROM_45000_TO_50000">
                            45.000 a 50.000
                          </SelectItem>
                          <SelectItem value="OVER_50000">
                            Más de 50.000
                          </SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="garageType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Garaje</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value ?? ""}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecciona un tipo de garaje" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="STREET">Calle</SelectItem>
                          <SelectItem value="SHARED_UNGUARDED">
                            Garaje compartido sin vigilancia
                          </SelectItem>
                          <SelectItem value="SHARED_GUARDED">
                            Garaje compartido con vigilancia
                          </SelectItem>
                          <SelectItem value="PRIVATE">
                            Garaje privado
                          </SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="isLeased"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm col-span-2">
                      <FormLabel>¿Es un vehículo de leasing?</FormLabel>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="coverages" className="space-y-6">
            <CoveragesManagement
              coverages={form.watch("coverages")}
              onCoveragesChange={(updatedCoverages) =>
                form.setValue("coverages", updatedCoverages, {
                  shouldValidate: true,
                })
              }
            />
          </TabsContent>
        </Tabs>

        <div
          className={`fixed bottom-0 left-0 right-0 z-10 border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 transition-all duration-200 ${state === "collapsed" ? "md:left-[calc(var(--sidebar-width-icon)_+_1rem)]" : "md:left-[var(--sidebar-width)]"}`}
        >
          <div className="container flex h-16 items-center justify-between">
            {activeTab === "policy" && (
              <>
                <Button
                  type="button"
                  variant="outline"
                  onClick={onBack}
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Atrás
                </Button>
                <Button
                  type="button"
                  variant="default"
                  onClick={() => setActiveTab("insured")}
                >
                  Siguiente
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </>
            )}
            {activeTab === "insured" && (
              <>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setActiveTab("policy")}
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Atrás
                </Button>
                <Button
                  type="button"
                  variant="default"
                  onClick={() => setActiveTab("asset")}
                >
                  Siguiente
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </>
            )}
            {activeTab === "asset" && (
              <>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setActiveTab("insured")}
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Atrás
                </Button>
                <Button
                  type="button"
                  variant="default"
                  onClick={() => setActiveTab("coverages")}
                >
                  Siguiente
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </>
            )}
            {activeTab === "coverages" && (
              <>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setActiveTab("asset")}
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Atrás
                </Button>
                <Button type="submit" variant="default">
                  Continuar
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </>
            )}
          </div>
        </div>
      </form>
    </Form>
  );
}
