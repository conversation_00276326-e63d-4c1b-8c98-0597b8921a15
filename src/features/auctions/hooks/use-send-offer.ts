import { useMutation, useQueryClient } from "@tanstack/react-query";

interface SendOfferData {
  policyId: string;
  annualPremium: number;
  fileUrl?: string;
}

interface SendOfferResponse {
  success: boolean;
}

async function sendOffer(data: SendOfferData): Promise<SendOfferResponse> {
  const response = await fetch("/api/auctions/send-offer", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const error = await response.text();
    throw new Error(error || "Failed to send offer");
  }

  return response.json();
}

export function useSendOffer() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: sendOffer,
    onSuccess: () => {
      // Invalidate auction queries to refresh the Kanban board
      queryClient.invalidateQueries({ queryKey: ["auctions"] });
      queryClient.invalidateQueries({ queryKey: ["broker-auctions"] });
    },
  });
}