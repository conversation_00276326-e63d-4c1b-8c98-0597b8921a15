/**
 * Utility functions for formatting auction time remaining
 */

/**
 * Calculates and formats the time remaining until an auction ends
 * @param endDate - The auction end date (ISO string or Date)
 * @returns Formatted time string (e.g., "2d 5h", "3h 45m", "30m", "Finalizada")
 */
export function formatTimeRemaining(endDate: string | Date): string {
  const now = new Date();
  const end = typeof endDate === 'string' ? new Date(endDate) : endDate;
  
  // Check if the date is valid
  if (isNaN(end.getTime())) {
    return "Finalizada";
  }
  
  const diffMs = end.getTime() - now.getTime();
  
  // If auction has ended
  if (diffMs <= 0) {
    return "Finalizada";
  }
  
  // Convert to different time units
  const diffMinutes = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  
  // Format based on time remaining
  if (diffDays > 0) {
    const remainingHours = diffHours % 24;
    if (remainingHours > 0) {
      return `${diffDays}d ${remainingHours}h`;
    }
    return `${diffDays}d`;
  } else if (diffHours > 0) {
    const remainingMinutes = diffMinutes % 60;
    if (remainingMinutes > 0) {
      return `${diffHours}h ${remainingMinutes}m`;
    }
    return `${diffHours}h`;
  } else {
    return `${diffMinutes}m`;
  }
}

/**
 * Gets the urgency level based on time remaining
 * @param endDate - The auction end date (ISO string or Date)
 * @returns Urgency level: "high", "medium", "low", or "finished"
 */
export function getTimeUrgency(endDate: string | Date): "high" | "medium" | "low" | "finished" {
  const now = new Date();
  const end = typeof endDate === 'string' ? new Date(endDate) : endDate;
  
  if (isNaN(end.getTime())) {
    return "finished";
  }
  
  const diffMs = end.getTime() - now.getTime();
  
  if (diffMs <= 0) {
    return "finished";
  }
  
  const diffHours = diffMs / (1000 * 60 * 60);
  
  if (diffHours <= 2) {
    return "high";
  } else if (diffHours <= 12) {
    return "medium";
  } else {
    return "low";
  }
}

/**
 * Gets the appropriate CSS classes for time remaining badge based on urgency
 * @param endDate - The auction end date (ISO string or Date)
 * @returns CSS classes for styling the time badge
 */
export function getTimeRemainingBadgeClasses(endDate: string | Date): string {
  const urgency = getTimeUrgency(endDate);
  
  switch (urgency) {
    case "high":
      return "bg-red-100 text-red-800 border-red-200";
    case "medium":
      return "bg-yellow-100 text-yellow-800 border-yellow-200";
    case "low":
      return "bg-green-100 text-green-800 border-green-200";
    case "finished":
      return "bg-gray-100 text-gray-800 border-gray-200";
    default:
      return "bg-gray-100 text-gray-800 border-gray-200";
  }
}
