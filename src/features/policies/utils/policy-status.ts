import { PolicyStatus } from "@prisma/client";
import { formatDate } from "@/lib/utils";

/**
 * Get subtitle text based on policy status
 */
export const getSubtitleByStatus = (status: PolicyStatus, endDate?: Date | string) => {
  switch (status) {
    case PolicyStatus.ACTIVE:
      return "Póliza vigente";
    case PolicyStatus.DRAFT:
      return "Completa los datos pendientes";
    case PolicyStatus.RENEW_SOON:
      return endDate ? `Renovar antes del ${formatDate(endDate)}` : "Renovar pronto";
    case PolicyStatus.EXPIRED:
      return "Cobertura caducada";
    case PolicyStatus.REJECTED:
      return "Documento rechazado";
    default:
      return "Póliza vigente";
  }
};

/**
 * Get filter label with count
 */
export const getFilterLabel = (
  status: PolicyStatus | "all" | "attention" | "renew_soon" | "expired",
  policies: any[]
) => {
  switch (status) {
    case "all":
      return `Todas las pólizas (${policies.length})`;
    case "ACTIVE":
      return `Activas (${policies.filter(p => p.status === PolicyStatus.ACTIVE).length})`;
    case "DRAFT":
      return `Borradores (${policies.filter(p => p.status === PolicyStatus.DRAFT).length})`;
    case "RENEW_SOON":
      return `Renovar Pronto (${policies.filter(p => p.status === PolicyStatus.RENEW_SOON).length})`;
    case "EXPIRED":
      return `Expiradas (${policies.filter(p => p.status === PolicyStatus.EXPIRED).length})`;
    case "REJECTED":
      return `Rechazadas (${policies.filter(p => p.status === PolicyStatus.REJECTED).length})`;
    case "attention":
      const needsAttention = policies.filter(p => 
        p.status === PolicyStatus.RENEW_SOON || p.status === PolicyStatus.EXPIRED
      ).length;
      return `Requieren Atención (${needsAttention})`;
    default:
      return `Todas las pólizas (${policies.length})`;
  }
};

/**
 * Get policy status display name
 */
export const getStatusDisplayName = (status: PolicyStatus) => {
  switch (status) {
    case PolicyStatus.ACTIVE:
      return "Activa";
    case PolicyStatus.DRAFT:
      return "Borrador";
    case PolicyStatus.RENEW_SOON:
      return "Renovar pronto";
    case PolicyStatus.EXPIRED:
      return "Expirada";
    case PolicyStatus.REJECTED:
      return "Rechazada";
    default:
      return status;
  }
};

/**
 * Check if policy status indicates expiration
 */
export const isExpiredStatus = (status: PolicyStatus) => {
  return status === PolicyStatus.EXPIRED;
};

/**
 * Check if policy status indicates renewal needed
 */
export const isRenewSoonStatus = (status: PolicyStatus) => {
  return status === PolicyStatus.RENEW_SOON;
};

/**
 * Check if policy status indicates draft
 */
export const isDraftStatus = (status: PolicyStatus) => {
  return status === PolicyStatus.DRAFT;
};

/**
 * Check if policy status indicates rejection
 */
export const isRejectedStatus = (status: PolicyStatus) => {
  return status === PolicyStatus.REJECTED;
};