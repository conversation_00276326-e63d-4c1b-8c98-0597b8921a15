# Changelog - August 11, 2025 (v42)

## 🔧 Database & Security Fixes

### Fixed Supabase User Profile Sync Trigger
- **Issue**: The `sync_user_profile` trigger was silently failing to create user profiles during signup due to missing RLS policies
- **Root Cause**: Missing INSERT policies for profile tables (`account_holder_profile`, `broker_profile`, `admin_profile`) prevented the trigger from creating records
- **Solution**: Comprehensive fix involving multiple components

#### Added Missing RLS Policies
- Created `supabase/policies/AccountHolderProfile/01_enable_rls.sql` - Enabled RLS for account holder profiles
- Created `supabase/policies/AccountHolderProfile/02_policies.sql` - Added SELECT, INSERT, UPDATE policies for account holder profiles
- Created `supabase/policies/BrokerProfile/01_enable_rls.sql` - Enabled RLS for broker profiles  
- Created `supabase/policies/BrokerProfile/02_policies.sql` - Added SELECT, INSERT, UPDATE policies for broker profiles
- Created `supabase/policies/AdminProfile/01_enable_rls.sql` - Enabled RLS for admin profiles
- Created `supabase/policies/AdminProfile/02_policies.sql` - Added SELECT, INSERT, UPDATE policies for admin profiles

#### Enhanced User Table Policies
- Updated `supabase/policies/User/02_user_access_policy.sql` - Added INSERT policy to allow users to create their own records during signup

#### Improved Trigger Function Robustness
- Enhanced `supabase/functions/02_sync_user_profile.sql` with:
  - **Role Validation**: Added validation for user role from `raw_user_meta_data` with fallback to 'ACCOUNT_HOLDER'
  - **Upsert Logic**: Changed from `INSERT ... ON CONFLICT DO NOTHING` to `ON CONFLICT DO UPDATE SET` for better handling of existing records
  - **Security**: Added explicit `search_path = public` setting for `SECURITY DEFINER` function
  - **Error Handling**: Enhanced logging with `SQLSTATE` codes for better debugging
  - **Consistency**: Ensured validated `user_role` variable is used throughout the function

#### Added KPI card "Póliza firmada" on the Account Holder auctions index (Mis Subastas) to display the number of auctions in the SIGNED_POLICY state.
- Removed the "Totales" KPI card per product request and adjusted the KPI grid layout from 4 to 3 columns for better balance.

#### Updated the account holder auctions counting logic in the client hook to improve accuracy:
- total now reads from `pagination.totalCount` returned by the API instead of counting only the items in the current page.
- car/moto counts now derive from the structured `assetType` field ("CAR" | "MOTORCYCLE") instead of parsing display strings.
- signed policy count derives from `status === "SIGNED_POLICY"`.
- Removed legacy references related to deprecated states (e.g., CONFIRM_BROKER) that were no longer part of the domain model.

#### Fixed discrepancy where the "Moto" counter showed 0 despite existing motorcycle auctions; counts now leverage `assetType`.
- Fixed total counter mismatch (showing 6 instead of 7) by using server-reported `totalCount`.
- Corrected a UI class name typo in the empty state card background (`bg-white`).

#### Cleaned up the Account Holder PRD (Auction FAQ) to remove stray diff markers and references to deprecated states, ensuring the states list matches current product behavior.
- Seeding: In some environments, `prisma/seed.ts` attempts to insert `SIGNED_POLICY` into `auction_state`, which may cause `PrismaClientUnknownRequestError` if the database enum is out of sync. Action: align the database enum with `schema.prisma` via migrations or update the seed script to use valid enum values for the target database.

## Bug Fixes - Auction Filtering & Counting

### Fixed Auction Count Discrepancies
- **Issue**: The sum of KPI totals for "open", "closed", and "signed policy" auctions was higher than the total number of auctions in the database.
- **Root Cause**: The calculation for the `closed` count was `total - open`, which incorrectly included auctions with a `SIGNED_POLICY` status, leading to double-counting.
- **Solution**: The logic in `useAuctionCounts` was corrected to `closed = total - open - signedPolicy`, ensuring that the counts are mutually exclusive and accurate.

- **Issue**: Auction counts were showing 6 instead of 7 total auctions
- **Root Cause**: `useAuctionCounts` hook was fetching with limit 10,000 but only counting from returned data instead of using server's `totalCount`
- **Solution**: Modified counting logic to use separate API calls for accurate counts:
  - Total count: Uses server-reported `totalCount` from main API call
  - Asset type counts: Uses separate filtered API calls for CAR and MOTORCYCLE
  - Status counts: Fetches all data when needed for accurate open/closed/signed policy counts

### Fixed Asset Type Filter Buttons
- **Issue**: "TODAS", "COCHE", and "MOTORCYCLE" filter buttons were not working correctly
- **Root Cause**: Multiple issues in API and frontend:
  1. Database query conflicts when multiple filters were applied
  2. Missing page reset when asset type filter changed
- **Solutions**:
  1. **API Fix**: Restructured where clause construction to use proper AND conditions array instead of conflicting object properties
  2. **Frontend Fix**: Added `useEffect` to reset `currentPage` to 1 when `filterAssetsType` changes

### Technical Details
- **Files Modified**:
  - `src/features/account-holder/hooks/useAuctions.ts`: Fixed counting logic
  - `src/app/api/account-holder/auctions/list/route.ts`: Fixed query construction
  - `src/features/account-holder/components/auction-list.tsx`: Added page reset effect
- **Database Query**: Changed from conflicting object properties to proper AND conditions array for multiple filters
- **Performance**: Optimized counting by using minimal data fetches (limit=1) for count-only operations

## Known Issues

- Database seeding issue with `SIGNED_POLICY` status in `prisma/seed.ts` - needs investigation