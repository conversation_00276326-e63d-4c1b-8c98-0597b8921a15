"use client";

import React, { useState } from "react";
import { ZCard } from "@/components/shared/z-card";
import { PrimaryButton } from "@/components/shared/primary-button";
import { PolicyDetailsDrawer } from "@/components/shared/PolicyDetailsDrawer";
import { cn } from "@/lib/utils";
import { PolicyStatus, AssetType } from "@prisma/client";
import { GenericPolicyData } from "@/features/policies/utils/policy-transformer";
import { Eye, RotateCcw } from "lucide-react";
import Link from "next/link";
import { formatDate, formatCurrency, getAssetTypeIcon } from "@/lib/utils";
import { formatAssetType } from "@/lib/format-asset-type";
import { formatInsurerCompany } from "@/lib/format-insurer";
import {
  isExpiredStatus,
  isRenewSoonStatus,
  isDraftStatus,
  isRejectedStatus
} from "@/features/policies/utils/policy-status";
import { PolicyStatusBadge } from "@/features/policies/components/policy-status-badge";
import {
  translateFuelType,
  translateUsageType,
  translateGarageType,
  translateKmRange
} from "@/features/policies/utils/translations";

interface PolicyCardProps {
  policy: GenericPolicyData;
  id: string;
  policyNumber: string;
  assetInfo: string;
  insurerName: string;
  productName: string;
  premium: number;
  status: PolicyStatus;
  endDate: Date | string | null;
  insuredName: string;
  coverageCount: number;
  assetType: AssetType;
  className?: string;
  baseUrl?: string; // Allow different base URLs for different roles
}

export function PolicyCard({
  policy,
  id,
  policyNumber,
  assetInfo,
  insurerName,
  productName,
  premium,
  status,
  endDate,
  insuredName,
  coverageCount,
  assetType,
  className,
  baseUrl = "/account-holder", // Default to account-holder, can be overridden
  ...props
}: PolicyCardProps & Omit<React.HTMLAttributes<HTMLDivElement>, "id">) {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  // Transform policy data for the drawer
  const transformedPolicyData = {
    id: policy.id,
    policyNumber: policy.policyNumber || "No disponible",
    status: policy.status as any,
    insurer: policy.insurerCompany ? formatInsurerCompany(policy.insurerCompany) : "No disponible",
    product: policy.productName || "No disponible",
    policyType: policy.asset?.assetType ? formatAssetType(policy.asset.assetType) : "No disponible",
    validity: policy.endDate ? `Hasta ${formatDate(policy.endDate)}` : 'Fecha no disponible',
    annualPremium: formatCurrency(policy.premium || 0),
    holderName: insuredName || "No disponible",
    birthDate: "No disponible", // This data is not available in the policy object
    gender: "No disponible", // This data is not available in the policy object
    phone: "No disponible", // This data is not available in the policy object
    email: "No disponible", // This data is not available in the policy object
    // Vehicle data mapping from database structure with proper type conversion and translation
    vehiclePlate: policy.asset?.vehicleDetails?.licensePlate || "No disponible",
    vehicleFirstRegistrationDate: policy.asset?.vehicleDetails?.firstRegistrationDate
      ? (() => {
          try {
            const date = policy.asset.vehicleDetails.firstRegistrationDate instanceof Date
              ? policy.asset.vehicleDetails.firstRegistrationDate
              : new Date(policy.asset.vehicleDetails.firstRegistrationDate);
            return isNaN(date.getTime()) ? "No disponible" : date.toLocaleDateString('es-ES');
          } catch {
            return "No disponible";
          }
        })()
      : "No disponible",
    vehicleBrand: policy.asset?.vehicleDetails?.brand || "No disponible",
    vehicleModel: policy.asset?.vehicleDetails?.model || "No disponible",
    vehicleVersion: policy.asset?.vehicleDetails?.version || "No disponible",
    vehicleManufacturingYear: policy.asset?.vehicleDetails?.year?.toString() || "No disponible",
    vehicleType: policy.asset?.assetType ? formatAssetType(policy.asset.assetType) : "No disponible",
    vehicleFuelType: policy.asset?.vehicleDetails?.fuelType
      ? translateFuelType(policy.asset.vehicleDetails.fuelType)
      : "No disponible",
    vehicleVin: policy.asset?.vehicleDetails?.chassisNumber || "No disponible",
    vehiclePower: policy.asset?.vehicleDetails?.powerCv?.toString() || "No disponible",
    vehicleSeats: policy.asset?.vehicleDetails?.seats?.toString() || "No disponible",
    vehicleUsageType: policy.asset?.vehicleDetails?.usageType
      ? translateUsageType(policy.asset.vehicleDetails.usageType)
      : "No disponible",
    vehicleGarageType: policy.asset?.vehicleDetails?.garageType
      ? translateGarageType(policy.asset.vehicleDetails.garageType)
      : "No disponible",
    vehicleKmPerYear: policy.asset?.vehicleDetails?.kmPerYear
      ? translateKmRange(policy.asset.vehicleDetails.kmPerYear)
      : "No disponible",
    vehicleIsLeased: policy.asset?.vehicleDetails?.isLeased === true ? "Sí" : "No",
    coverages: policy.coverages ? policy.coverages.map(c => ({ title: c.customName || c.type, limit: c.limit, description: c.description || 'No disponible', guaranteeType: c.type, severity: 'Medium' })) : [],
    document: policy.document,
  };
  const isExpired = isExpiredStatus(status);
  const isRenewSoon = isRenewSoonStatus(status);
  const isDraft = isDraftStatus(status);
  const isRejected = isRejectedStatus(status);

  return (
    <ZCard
      variant="policy"
      className={cn(
        "transition-all hover:shadow-lg p-3 h-full w-full min-w-[280px]",
        className
      )}
      {...props}
    >
      <div className="space-y-3 flex flex-col h-full">
        {/* Header with Asset Icon, Title and Status Badge */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3 min-w-0 flex-1">
            <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-br from-primary/10 to-primary/20 border border-primary/20 flex items-center justify-center text-sm shadow-sm">
              {getAssetTypeIcon(assetType)}
            </div>
            <div className="min-w-0 flex-1">
              <div className="flex items-center justify-between">
                <h3 className="font-bold text-sm text-foreground truncate">
                  {isDraft ? "Verificación Pendiente" : isRejected ? "Rechazado" : (
                    <span className="font-mono">{policyNumber}</span>
                  )}
                </h3>
                <PolicyStatusBadge status={status} />
              </div>
              {!isDraft && !isRejected && (
                <div className="text-xs text-muted-foreground truncate mt-0.5" title={assetInfo}>
                  {assetInfo}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Main Content - Conditional Layout Based on Status */}
        {isDraft || isRejected ? (
          /* DRAFT or REJECTED Status - Special Layout */
          <div className="flex-grow">
            <p className="text-xs text-muted-foreground leading-relaxed">
              {isDraft 
                ? "Tu póliza está pasando por nuestro proceso de verificación. Tan pronto como la validación esté completa tendrás acceso completo a sus datos."
                : "El documento subido no es una póliza válida o no se puede leer claramente. Por favor, sube una copia correcta y legible para continuar."
              }
            </p>
          </div>
        ) : (
          /* Normal Status - Responsive Layout */
          <div className="flex-grow space-y-2">
            {/* Compact Info Grid */}
            <div className="grid grid-cols-1 gap-2 text-xs">
              {/* Insurer and Premium in one row */}
              <div className="flex justify-between items-center">
                <div className="min-w-0 flex-1">
                  <div className="font-semibold text-foreground truncate" title={insurerName}>
                    {insurerName}
                  </div>
                  <div className="text-muted-foreground truncate text-[10px]" title={productName}>
                    {productName}
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-semibold text-foreground">
                    {premium && premium > 0 ? `${formatCurrency(premium)}/año` : "N/A"}
                  </div>
                  <div className="text-muted-foreground text-[10px]">
                    {coverageCount > 0 ? `${coverageCount} coberturas` : "Sin coberturas"}
                  </div>
                </div>
              </div>
              
              {/* Tomador and Expiry in one row */}
              <div className="flex justify-between items-center pt-1 border-t border-border/50">
                <div className="min-w-0 flex-1">
                  <div className="text-[10px] font-semibold text-muted-foreground uppercase tracking-wide">
                    Tomador
                  </div>
                  <div className="font-medium text-foreground truncate text-[11px]" title={insuredName}>
                    {insuredName}
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-[10px] font-semibold text-muted-foreground uppercase tracking-wide">
                    Vence
                  </div>
                  {endDate ? (
                    <div className="text-muted-foreground text-[11px]">
                      {formatDate(endDate)}
                    </div>
                  ) : (
                    <div className="text-muted-foreground text-[11px]">
                      N/A
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Footer with Actions */}
        <div className="flex justify-end items-center pt-2 border-t border-border/30">
          {/* Action Button */}
          <div>
            {(isDraft || isRejected) && (
              <PrimaryButton 
                size="sm" 
                className="h-7 text-xs opacity-50 cursor-not-allowed gap-1.5 px-3"
                disabled
              >
                <Eye className="h-3.5 w-3.5" />
                Ver detalles
              </PrimaryButton>
            )}
            
            {(status === PolicyStatus.ACTIVE) && (
              <>
                <PrimaryButton 
                  size="sm" 
                  className="h-7 text-xs gap-1.5 px-3"
                  onClick={() => setIsDrawerOpen(true)}
                >
                  <Eye className="h-3.5 w-3.5" />
                  Ver detalles
                </PrimaryButton>
                <PolicyDetailsDrawer
                   isOpen={isDrawerOpen}
                   onClose={() => setIsDrawerOpen(false)}
                   mode="account-holder"
                   policyData={transformedPolicyData}
                 />
              </>
            )}
            
            {isRenewSoon && (
              <Link href={`${baseUrl}/policies/${id}/renew`}>
                <PrimaryButton size="sm" className="h-7 text-xs gap-1.5 px-3">
                  <RotateCcw className="h-3.5 w-3.5" />
                  Renovar
                </PrimaryButton>
              </Link>
            )}
            
            {isExpired && (
              <>
                <PrimaryButton 
                  size="sm" 
                  className="h-7 text-xs gap-1.5 px-3"
                  onClick={() => setIsDrawerOpen(true)}
                >
                  <Eye className="h-3.5 w-3.5" />
                  Ver detalles
                </PrimaryButton>
                <PolicyDetailsDrawer
                   isOpen={isDrawerOpen}
                   onClose={() => setIsDrawerOpen(false)}
                   mode="account-holder"
                   policyData={transformedPolicyData}
                 />
              </>
            )}
          </div>
        </div>
      </div>
    </ZCard>
  );
}