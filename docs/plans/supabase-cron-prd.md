# PRD – Supabase Cron Jobs Implementation

## 1. Background  
The application is a vehicle insurance auction system built with **Next.js 15**, **Supabase (Postgres, Auth)**, and **TypeScript**, deployed on OCI with Coolify.  
Auctions last **48 hours** and move through states: **Open → Closed.** User engagement requires timely **state changes** and **email notifications** when auctions start, end, or receive new bids.  
Currently, these transitions are handled manually. Automating them is crucial to ensure reliability and user trust.

---

## 2. Motivation  
- **Reliability** → Automate auction expiration so no auction remains open beyond its allowed duration.  
- **Engagement** → Notify users and brokers when important auction events occur.  
- **MVP Speed** → Use Supabase-native tools (`pg_cron`, `pg_net`) to avoid external complexity. For development purposes, please use the Supabase MCP.
- **Cost Efficiency** → Stay within Free/Pro plan quotas, avoiding unnecessary services.  

---

## 3. Actions  

We will implement **two Supabase cron jobs** using the `pg_cron` extension.  

### Cron Job 1 – Auction Expiration  
- **Goal**: Close expired auctions automatically.  
- **Schedule**: Every 5 minutes.  
- **Logic**:  
  ```sql
  UPDATE "Auction"
  SET status = 'CLOSED'
  WHERE status = 'OPEN' AND expires_at <= now();
  ```
- **Result**: Ensures auctions move to *Closed* once expired.

### Cron Job 2 – Notification Trigger  
- **Goal**: Notify users/brokers when auctions close or receive bids.  
- **Schedule**: Every 5 minutes.  
- **Logic**:  
  - Find auctions that changed to *CLOSED* in the last 5 minutes.  
  - Use `pg_net` to call a Supabase Edge Function.  
  - Edge Function should handle email sending (via Brevo SMTP).  
- **Result**: Keeps stakeholders updated automatically.

### Supporting Steps  
- Enable `pg_cron` and `pg_net` in Supabase.  
- Create `sendAuctionNotification.ts` Edge Function in TypeScript.  
- Set up logging/monitoring for jobs in Supabase Dashboard.  

---

## 4. Deliverables  
1. **SQL Migration Scripts**  
   - Enabling extensions (`pg_cron`, `pg_net`).  
   - Queries for the two cron jobs.  
2. **Edge Function**  
   - `sendAuctionNotification.ts` to handle email dispatch.  
3. **Documentation**  
   - Cron job setup, schedules, and quota considerations (Free vs Pro).  
4. **Testing & QA**  
   - Auction expiration test cases.  
   - Notification delivery verification.
   - Ask for help to the Human Team to test the emails.  

---

✅ This setup gives you **automated expiration** and **notification flows** directly in Supabase, with minimal infra
