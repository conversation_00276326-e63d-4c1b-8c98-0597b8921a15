# 2025-07-22 - Auction Card Readability Improvements and Bug Fixes

## Readability Improvements

Increased text size for `div` elements containing policy numbers and status/time indicators across all auction card components to improve readability:

- **won-auction-card.tsx**: Changed policy number and "FINALIZADA" status text size from `text-[10px]` to `text-xs`.
- **participating-auction-card.tsx**: Changed policy number and time remaining text size from `text-[10px]` to `text-xs`.
- **available-auction-card.tsx**: Changed time remaining text size from `text-[10px]` to `text-xs`.
- **confirmed-auction-card.tsx**: Changed policy number and "FIRMADA" status text size from `text-[10px]` to `text-xs`.
- **lost-auction-card.tsx**: Changed policy number and "FINALIZADA" status text size from `text-[10px]` to `text-xs`.

## Bug Fixes

- **participating-auction-card.tsx**: Removed unused `formatPremiumWithSuffix` import to resolve diagnostic error.
- **available-auction-card.tsx** and **participating-auction-card.tsx**: Fixed button clickability for `div` elements when `isNestedInInteractiveElement` is true, by adding `hover:bg-green-600`, `transition-colors duration-200`, `cursor-pointer`, and `onClick` handler for consistent interactive behavior.
- **kanban.tsx**: Added `mb-2` class to `kanbanBoardCardClassNames` to provide padding between cards.