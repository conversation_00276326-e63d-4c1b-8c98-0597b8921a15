-- Function to create a user profile when a new user signs up
CREATE OR REPLACE FUNCTION public.sync_user_profile()
RETURNS TRIGGER AS $$
DECLARE
  user_role text;
BEGIN
  -- Extract and validate role from metadata
  user_role := NEW.raw_user_meta_data->>'role';
  
  -- Log the trigger execution for debugging
  RAISE LOG 'sync_user_profile triggered for user: %, role: %', NEW.email, COALESCE(user_role, 'NULL');
  
  -- Validate role is present and valid
  IF user_role IS NULL OR user_role NOT IN ('ACCOUNT_HOLDER', 'BROKER', 'ADMIN') THEN
    RAISE LOG 'Invalid or missing role for user %: %. Setting default to ACCOUNT_HOLDER', NEW.email, COALESCE(user_role, 'NULL');
    user_role := 'ACCOUNT_HOLDER';
  END IF;

  -- Insert a new record into the public.user table
  BEGIN
    INSERT INTO public.user (id, email, phone, first_name, last_name, display_name, role, updated_at)
    VALUES (
      NEW.id,
      NEW.email,
      NEW.raw_user_meta_data->>'phone',
      NEW.raw_user_meta_data->>'first_name',
      NEW.raw_user_meta_data->>'last_name',
      NEW.raw_user_meta_data->>'display_name',
      user_role::public.role,
      NOW()
    )
    ON CONFLICT (id) DO UPDATE SET
      email = EXCLUDED.email,
      phone = EXCLUDED.phone,
      first_name = EXCLUDED.first_name,
      last_name = EXCLUDED.last_name,
      display_name = EXCLUDED.display_name,
      role = EXCLUDED.role,
      updated_at = NOW();
    
    RAISE LOG 'Successfully inserted/updated user in public.user table: %', NEW.email;
  EXCEPTION
    WHEN OTHERS THEN
      RAISE LOG 'Error inserting/updating user: %, SQLSTATE: %', SQLERRM, SQLSTATE;
      RAISE;
  END;

  -- Create associated profiles only if they don't already exist
  -- This handles both manual user creation and automatic signup

  -- Create an associated account_holder_profile only for ACCOUNT_HOLDER role (using validated user_role)
  IF user_role = 'ACCOUNT_HOLDER' THEN
    BEGIN
      INSERT INTO public.account_holder_profile (user_id)
      VALUES (NEW.id)
      ON CONFLICT (user_id) DO NOTHING; -- Ignore if already exists
      RAISE LOG 'Successfully ensured account holder profile for user: %', NEW.email;
    EXCEPTION
      WHEN OTHERS THEN
        RAISE LOG 'Error inserting account holder profile: %, SQLSTATE: %', SQLERRM, SQLSTATE;
        -- Don't re-raise the error to avoid blocking user creation
    END;
  END IF;

  -- Create an associated broker_profile for BROKER role
  IF user_role = 'BROKER' THEN
    BEGIN
      INSERT INTO public.broker_profile (user_id, registration_class, registration_key, registration_date, legal_name, identifier, is_authorized_by_other, is_complementary, is_group_agent)
      VALUES (
        NEW.id,
        'Pending', -- Default registration class
        'TEMP_' || SUBSTRING(NEW.id::text, 1, 8), -- Shorter temporary registration key
        NOW(), -- Default registration date
        'Pending Legal Name', -- Default legal name
        'TEMP_' || SUBSTRING(NEW.id::text, 1, 8), -- Shorter temporary identifier
        false, -- Default values
        false,
        false
      )
      ON CONFLICT (user_id) DO NOTHING; -- Ignore if already exists
      RAISE LOG 'Successfully ensured broker profile for user: %', NEW.email;
    EXCEPTION
      WHEN OTHERS THEN
        RAISE LOG 'Error inserting broker profile: %, SQLSTATE: %', SQLERRM, SQLSTATE;
        -- Don't re-raise the error to avoid blocking user creation
    END;
  END IF;

  -- Create an associated admin_profile for ADMIN role
  IF user_role = 'ADMIN' THEN
    BEGIN
      INSERT INTO public.admin_profile (user_id)
      VALUES (NEW.id)
      ON CONFLICT (user_id) DO NOTHING; -- Ignore if already exists
      RAISE LOG 'Successfully ensured admin profile for user: %', NEW.email;
    EXCEPTION
      WHEN OTHERS THEN
        RAISE LOG 'Error inserting admin profile: %, SQLSTATE: %', SQLERRM, SQLSTATE;
        -- Don't re-raise the error to avoid blocking user creation
    END;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = public;

-- Trigger to call the function when a new user is created
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.sync_user_profile();