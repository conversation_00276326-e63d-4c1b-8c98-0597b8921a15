# 10.4 Language and Naming Conventions

**Code Layer (English):**
- Variable names: `camelCase`
- Function names: `camelCase`
- Class names: `PascalCase`
- Component names: `PascalCase`
- File names: `kebab-case` or `camelCase`
- Database models: `PascalCase`

**UI Layer (Spanish):**
- All user-visible text in professional Spanish
- Consistent terminology across platform
- Error messages and validation feedback
- Button labels and form placeholders
