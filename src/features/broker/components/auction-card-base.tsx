"use client";

import React from "react";
import { ZCard } from "@/components/shared/z-card";
import { Badge } from "@/components/ui/badge";
import { cn, formatCurrency } from "@/lib/utils";
import { formatTimeRemaining } from "@/features/auctions/utils/time-formatting";
import { Clock, Users } from "lucide-react";
import { getCoverageTypeDisplay, maskClientName } from "../utils/auction-card-utils";
import { type AssetType } from "@prisma/client";

// Local function for auction cards - returns scales of justice emoji for all asset types
function getAssetTypeIcon(assetType: AssetType) {
  return "⚖️";
}

export interface AuctionCardBaseData {
  id: string;
  clientName: string;
  coverageType: string;
  timeRemaining: string;
  currentPremium: number;
  participantCount: number;
  assetType?: string;
  assetDisplayName?: string;
  location?: string;
  urgencyLevel?: "low" | "medium" | "high";
}

interface AuctionCardBaseProps {
  auction: AuctionCardBaseData;
  className?: string;
  children?: React.ReactNode;
}

export function AuctionCardBase({ auction, className, children }: AuctionCardBaseProps) {
  const urgencyColors = {
    low: "bg-green-100 text-green-800 border-green-200",
    medium: "bg-yellow-100 text-yellow-800 border-yellow-200",
    high: "bg-red-100 text-red-800 border-red-200",
  };

  const urgencyColor = urgencyColors[auction.urgencyLevel || "medium"];

  return (
    <ZCard variant="policy" className={cn("transition-all hover:shadow-lg p-3 h-full w-full min-w-[280px]", className)}>
      <div className="space-y-3 flex flex-col h-full">
        {/* Header with Asset Icon, Title and Status Badge */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3 min-w-0 flex-1">
            <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-br from-primary/10 to-primary/20 border border-primary/20 flex items-center justify-center text-sm shadow-sm">
              {getAssetTypeIcon((auction.assetType || "CAR") as AssetType)}
            </div>
            <div className="min-w-0 flex-1">
              <div className="flex items-center justify-between">
                <h3 className="font-bold text-sm text-foreground truncate">
                  {maskClientName(auction.clientName)}
                </h3>
                <Badge variant="outline" className={cn("px-2 py-1 text-xs font-semibold", urgencyColor)}>
                  <Clock className="w-3 h-3 mr-1" /> {auction.timeRemaining}
                </Badge>
              </div>
              {auction.assetDisplayName && (
                <div className="text-xs text-muted-foreground truncate mt-0.5" title={auction.assetDisplayName}>
                  {auction.assetDisplayName}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Main Content - Compact Layout */}
        <div className="flex-grow space-y-2">
          {/* Compact Info Grid */}
          <div className="grid grid-cols-1 gap-2 text-xs">
            {/* Coverage and Premium in one row */}
            <div className="flex justify-between items-center">
              <div className="min-w-0 flex-1">
                <div className="font-semibold text-foreground truncate">
                  {getCoverageTypeDisplay(auction.coverageType)}
                </div>
                <div className="text-muted-foreground truncate text-[10px]">
                  Cobertura
                </div>
              </div>
              <div className="text-right">
                <div className="font-semibold text-foreground">
                  {formatCurrency(auction.currentPremium)}/año
                </div>
                <div className="text-muted-foreground text-[10px]">
                  Prima actual
                </div>
              </div>
            </div>
            
            {/* Participants and Location in one row */}
            <div className="flex justify-between items-center pt-1 border-t border-border/50">
              <div className="min-w-0 flex-1">
                <div className="text-[10px] font-semibold text-muted-foreground uppercase tracking-wide">
                  Participantes
                </div>
                <div className="font-medium text-foreground text-[11px] flex items-center gap-1">
                  <Users className="w-3 h-3" />
                  {auction.participantCount}
                </div>
              </div>
              {auction.location && (
                <div className="text-right">
                  <div className="text-[10px] font-semibold text-muted-foreground uppercase tracking-wide">
                    Ubicación
                  </div>
                  <div className="text-muted-foreground text-[11px] truncate" title={auction.location}>
                    {auction.location}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Footer with custom content */}
        {children && (
          <div className="pt-2 border-t border-border/30">
            {children}
          </div>
        )}
      </div>
    </ZCard>
  );
}