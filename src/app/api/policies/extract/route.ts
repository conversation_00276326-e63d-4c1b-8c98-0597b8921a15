import { NextRequest, NextResponse } from "next/server";
import {
  GoogleGenerativeAI,
  Part,
  GenerationConfig,
} from "@google/generative-ai";
import { r2Client } from "@/lib/r2";
import { PutObjectCommand } from "@aws-sdk/client-s3";
import { db as prisma } from "@/lib/db";
import { randomUUID } from "crypto";
import { z } from "zod";
import { zodToJsonSchema } from "zod-to-json-schema";
import {
  AssetsType,
  FuelType,
  UsageType,
  GarageType,
  KmRange,
  PartyRole,
  GuaranteeType,
  PaymentPeriod,
  PolicyType,
} from "@prisma/client";

// Initialize the Gemini API client
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || "");

// This schema defines the response from the initial "bodyguard" validation call.
// It strictly checks if the document is a valid policy.
const ValidationSchema = z.object({
  isValid: z
    .boolean()
    .describe("Indicates if the document is a valid insurance policy."),
  reason: z.string().describe("A concise reason for the validation decision."),
});

// Maps the raw AI reason to a safe, standardized error code.
function mapReasonToErrorCode(reason: string): string {
  const lowerCaseReason = reason.toLowerCase();
  if (
    lowerCaseReason.includes("invoice") ||
    lowerCaseReason.includes("factura")
  ) {
    return "UNSUPPORTED_DOCUMENT_TYPE";
  }
  if (
    lowerCaseReason.includes("illegible") ||
    lowerCaseReason.includes("blurry") ||
    lowerCaseReason.includes("borroso")
  ) {
    return "ILLEGIBLE_DOCUMENT";
  }
  if (
    lowerCaseReason.includes("not a policy") ||
    lowerCaseReason.includes("no es una póliza") ||
    lowerCaseReason.includes("person") ||
    lowerCaseReason.includes("human") ||
    lowerCaseReason.includes("persona")
  ) {
    return "UNSUPPORTED_DOCUMENT_TYPE";
  }
  return "GENERIC_VALIDATION_FAILURE"; // Default fallback
}

// This schema is now more lenient, allowing null values for fields that Gemini might not find.
// This prevents validation failures and allows for partial data to be returned to the frontend.
const ExtractionSchema = z.object({
  policyNumber: z.string().min(1).nullable().describe("The policy number."),
  productName: z
    .string()
    .nullable()
    .describe("The commercial name of the insurance product."),
  startDate: z.coerce
    .date()
    .nullable()
    .describe("The start date of the policy coverage (YYYY-MM-DD)."),
  endDate: z.coerce
    .date()
    .nullable()
    .describe("The end date of the policy coverage (YYYY-MM-DD)."),
  paymentPeriod: z
    .nativeEnum(PaymentPeriod)
    .nullable()
    .describe("The frequency of premium payments."),
  policyType: z
    .nativeEnum(PolicyType)
    .nullable()
    .describe("The type of policy."),
  premium: z.number().nullable().describe("The cost of the insurance premium."),
  insurerName: z
    .string()
    .nullable()
    .describe("The full name of the insurance company."),
  asset: z
    .object({
      licensePlate: z
        .string()
        .nullable()
        .describe("The asset's license plate."),
      brand: z.string().nullable().describe("The brand of the asset."),
      model: z.string().nullable().describe("The model of the asset."),
      year: z
        .number()
        .nullable()
        .describe("The manufacturing year of the asset."),
      chassisNumber: z
        .string()
        .nullable()
        .describe("The asset's chassis number (VIN)."),
      firstRegistrationDate: z.coerce
        .date()
        .nullable()
        .describe("The date the asset was first registered (YYYY-MM-DD)."),
      type: z.nativeEnum(AssetsType).nullable().describe("The type of asset."),
      fuelType: z
        .nativeEnum(FuelType)
        .nullable()
        .describe("The type of fuel the asset uses."),
      powerCv: z
        .number()
        .nullable()
        .describe("The asset's power in horsepower (CV)."),
      seats: z
        .number()
        .nullable()
        .describe("The number of seats in the asset."),
      usageType: z
        .nativeEnum(UsageType)
        .nullable()
        .describe("The primary use of the asset."),
      garageType: z
        .nativeEnum(GarageType)
        .nullable()
        .describe("The type of garage where the asset is parked."),
      kmPerYear: z
        .nativeEnum(KmRange)
        .nullable()
        .describe("The estimated kilometers driven per year."),
    })
    .describe("Details of the insured asset."),
  insuredParties: z
    .array(
      z.object({
        fullName: z.string().describe("The full name of the insured person."),
        dni: z.string().describe("The DNI/NIE of the insured person."),
        role: z
          .nativeEnum(PartyRole)
          .describe(
            "The role of the person in the policy (e.g., POLICYHOLDER)."
          ),
      })
    )
    .describe("A list of all parties insured under the policy."),
  coverages: z
    .array(
      z.object({
        type: z
          .nativeEnum(GuaranteeType)
          .nullable()
          .describe("The type of coverage."),
        customName: z
          .string()
          .optional()
          .nullable()
          .describe("A custom name if the type is 'OTHER'."),
        limit: z
          .number()
          .optional()
          .nullable()
          .describe("The financial limit of the coverage."),
        deductible: z
          .number()
          .optional()
          .nullable()
          .describe("The deductible amount for the coverage."),
        description: z
          .string()
          .optional()
          .nullable()
          .describe("A brief description of the coverage."),
      })
    )
    .describe("A list of all coverages included in the policy."),
});

export async function uploadToR2(
  file: File,
  location = "policies"
): Promise<string> {
  const fileBuffer = Buffer.from(await file.arrayBuffer());
  const fileId = randomUUID();
  const bucketName = process.env.R2_BUCKET_NAME!;

  const key = `${location}/${fileId}-${file.name}`;

  const command = new PutObjectCommand({
    Bucket: bucketName,
    Key: key,
    Body: fileBuffer,
    ContentType: file.type,
  });

  await r2Client.send(command);
  return key;
}

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get("file") as File | null;

    if (!file) {
      return NextResponse.json({ error: "No file provided" }, { status: 400 });
    }

    const r2Key = await uploadToR2(file);

    const modelName = process.env.GEMINI_MODEL || "gemini-1.5-flash";
    const model = genAI.getGenerativeModel({ model: modelName });

    const generationConfig: GenerationConfig = {
      responseMimeType: "application/json",
    };

    // Stage 1: "Bodyguard" Validation
    const validationJsonSchema = zodToJsonSchema(
      ValidationSchema,
      "validationSchema"
    );
    const validationPrompt = `You are a strict validation gateway for an insurance policy upload wizard. Your primary function is to be a 'bodyguard'. You must rigorously analyze the document and determine with high confidence if it is a valid and legible insurance policy.

    Follow these rules strictly:
    1.  **Analyze Content:** Look for key identifiers like policy number, insured name, insurer, coverage types, and effective dates.
    2.  **Strict Rejection:** If the document is NOT a valid policy (e.g., it's an invoice, a random photo, a blank page, heavily corrupted, or completely illegible), you MUST return \`"isValid": false\`.
    3.  **Return ONLY JSON:** Your entire output must be a single JSON object conforming to the provided schema.

    JSON Schema:
    ${JSON.stringify(validationJsonSchema, null, 2)}
    `;

    const imagePart: Part = {
      inlineData: {
        mimeType: file.type,
        data: Buffer.from(await file.arrayBuffer()).toString("base64"),
      },
    };

    const geminiValidationResult = await model.generateContent({
      contents: [
        { role: "user", parts: [imagePart, { text: validationPrompt }] },
      ],
      generationConfig,
    });

    const validationResponseText = geminiValidationResult.response.text();
    const validationData = JSON.parse(validationResponseText);
    const parsedValidation = ValidationSchema.safeParse(validationData);

    if (!parsedValidation.success || !parsedValidation.data.isValid) {
      const reason = parsedValidation.success
        ? parsedValidation.data.reason
        : "Invalid validation response from AI.";

      // Log the failure to the database
      try {
        await prisma.extractionFailureLog.create({
          data: {
            reason: reason, // Log the original reason for internal analysis
            fileName: file.name,
            fileType: file.type,
            fileSize: file.size,
            r2Key: r2Key,
            // userId: can be added if you pass it from the client
          },
        });
      } catch (dbError) {
        console.error("Failed to log extraction failure:", dbError);
      }

      const errorCode = mapReasonToErrorCode(reason);
      return NextResponse.json({ isValid: false, errorCode });
    }

    // Stage 2: Data Extraction
    const extractionJsonSchema = zodToJsonSchema(
      ExtractionSchema,
      "extractionSchema"
    );
    const extractionPrompt = `You are an expert insurance data extractor for the Spanish market. The document has already been validated as an insurance policy. Now, analyze the provided policy document and return ONLY a single, clean JSON object that conforms to the following JSON schema.

- If a value cannot be found, use null.
- Dates must be in YYYY-MM-DD format.
- For ENUM fields, use the exact uppercase values provided in the schema.

JSON Schema:
${JSON.stringify(extractionJsonSchema, null, 2)}
`;

    const extractionResult = await model.generateContent({
      contents: [
        { role: "user", parts: [imagePart, { text: extractionPrompt }] },
      ],
      generationConfig,
    });

    const responseText = extractionResult.response.text();
    const extractedData = JSON.parse(responseText);

    // Transform coverages to handle non-enum values
    if (extractedData.coverages && Array.isArray(extractedData.coverages)) {
      const validGuaranteeTypes = Object.keys(GuaranteeType);
      extractedData.coverages.forEach((coverage: any) => {
        if (coverage.type && !validGuaranteeTypes.includes(coverage.type)) {
          console.warn(
            `Invalid GuaranteeType '${coverage.type}' received from AI. Mapping to 'OTHER'.`
          );
          coverage.customName = coverage.type; // Preserve original value
          coverage.type = "OTHER";
        }
      });
    }

    const extractionValidationResult =
      ExtractionSchema.safeParse(extractedData);

    if (!extractionValidationResult.success) {
      console.error(
        "Zod validation failed:",
        extractionValidationResult.error.flatten()
      );
      return NextResponse.json(
        {
          error: "Validation failed",
          details: extractionValidationResult.error.flatten(),
        },
        { status: 400 }
      );
    }

    return NextResponse.json({ data: extractionValidationResult.data });
  } catch (error: unknown) {
    console.error("Error in extraction API:", error);
    const errorMessage =
      error instanceof Error ? error.message : "An unknown error occurred";
    return NextResponse.json(
      { error: "Failed to process request", details: errorMessage },
      { status: 500 }
    );
  }
}
