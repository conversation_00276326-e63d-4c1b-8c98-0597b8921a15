"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import {
  CardD<PERSON><PERSON>,
  CardContent,
  CardHeader,
  <PERSON><PERSON><PERSON><PERSON>,
  Card,
} from "../../../../components/ui/card";

import { PolicyFileUploadStep } from "./PolicyFileUploadStep";
import { PolicySuccess } from "./PolicySuccess";
import { PolicyProgressStepper } from "./PolicyProgressStepper";
import { useUserAuthentication } from "../../../auth/hooks/useUserAuthentication";
import { useToast } from "../../../../components/ui/use-toast";

const policyFormSchema = z.object({
  type: z.enum(["CAR", "MOTORCYCLE"]),
});

type PolicyFormData = z.infer<typeof policyFormSchema>;

export function PolicyStepper() {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [user, setUser] = useState<any>(null);
  const [currentStep, setCurrentStep] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [hasSelectedType, setHasSelectedType] = useState(false);
  const [policyId, setPolicyId] = useState<string | null>(null);
  const [termsAccepted, setTermsAccepted] = useState(false);

  useUserAuthentication((user: any) => {
    setUser(user);
    setIsLoading(false);
  });

  const form = useForm<PolicyFormData>({
    resolver: zodResolver(policyFormSchema),
  });

  function getType() {
    switch (form.getValues("type")) {
      case "CAR":
        return "de Automóvil 🚗";
      case "MOTORCYCLE":
        return "de Motocicleta 🏍️";
      default:
        return "";
    }
  }

  const steps = useMemo(() => {
    return [
      {
        title: "Tipo de Póliza",
        description: "Selecciona tu tipo de póliza",
      },
      {
        title: hasSelectedType ? `Cargar Póliza ${getType()}` : "Cargar Póliza",
        description: `Carga tu póliza de seguro`,
      },
      {
        title: "Póliza Registrada",
        description: "Verifica que toda la información sea correcta",
      },
    ];
  }, [form]);

  const handleContinue = () => {
    setCurrentStep((prev) => prev + 1);
  };

  const handleTypeSelection = (type: "CAR" | "MOTORCYCLE") => {
    if (!user) {
      toast({
        variant: "destructive",
        title: "Sesión no iniciada",
        description: "Debes iniciar sesión para continuar.",
      });
      return;
    }

    form.setValue("type", type);
    setHasSelectedType(true);
    
    toast({
       title: "Tipo de póliza seleccionado",
       description: `Has seleccionado póliza de ${type === "CAR" ? "automóvil" : "motocicleta"}.`,
     });
    
    handleContinue();
  };

  const handleBack = () => {
    const newStep = Math.max(0, currentStep - 1);
    setCurrentStep(newStep);

    // Reset hasSelectedType when going back to step 0
    if (newStep === 0) {
      setHasSelectedType(false);
    }
  };

  const handleSubmit = async () => {
    if (!user) {
      toast({
        variant: "destructive",
        title: "Sesión no iniciada",
        description: "Debes iniciar sesión para guardar la póliza.",
      });
      return;
    }

    if (isSubmitting) {
      return; // Prevent multiple submissions
    }

    setIsSubmitting(true);

    try {
      const formData = new FormData();
      formData.append("type", form.getValues("type"));
      formData.append("termsAccepted", termsAccepted.toString());

      if (uploadedFile) {
        formData.append("file", uploadedFile);
      }
      const new_url = new URL("api/policies/create", window.location.origin);

      const response = await fetch(new_url.href, {
        method: "POST",
        body: formData,
      });
 
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Error al crear póliza");
      }

      // Store the policy ID from the response
      setPolicyId(result.policyId);
      
      toast({
        title: "Póliza registrada con éxito",
        description: "Tu póliza ha sido guardada y la subasta ha sido creada.",
      });
      setCurrentStep(2);
    } catch (error: any) {
      console.log("🚀 ~ handleSubmit ~ error:", error);
      toast({
        variant: "destructive",
        title: "Error al guardar póliza",
        description:
          error.message ||
          "Hubo un error al guardar tu póliza. Por favor intenta de nuevo.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[50vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 pb-12">
      {/* Main Content */}
      <div className="space-y-6">
        <CardHeader className="space-y-6">
          <CardTitle>
            {currentStep === 0
              ? "Haz que tu seguro compita por ti 🏁"
              : currentStep === 2
                ? "¡Póliza Registrada con éxito! 🎉"
                : "Sube tu póliza actual o expirada 📃"}
          </CardTitle>
          <CardDescription className="space-y-2">
            {currentStep === 0 ? (
              <>
                <b>
                  Recibe hasta 3 ofertas firmes en menos de 48h y ahorra hasta
                  un 40%
                </b>
                <br />
                <span>
                  ¿Tienes una póliza de seguro vigente? Sube tu póliza actual y
                  deja que las aseguradoras pujen. Tú eliges la mejor. Gratis y
                  sin compromiso 😎
                </span>
              </>
            ) : currentStep === 2 ? (
              <>Tu póliza ha sido registrada y la subasta comenzará pronto.</>
            ) : (
              <>
                <b>Sin preocupaciones, tus datos están seguros.</b>
                <br />
                <span>
                  Todos los datos que se necesitan, serán extraídos del
                  documento de la póliza actual o ya expirada.
                </span>
              </>
            )}
          </CardDescription>

          <PolicyProgressStepper currentStep={currentStep} steps={steps} />

          <CardContent className="space-y-6">
            {currentStep === 0 && (
              <div className="mt-4 space-y-6 text-center">
                <h3>¿Qué tipo de póliza quieres renovar?</h3>
                <div className="flex justify-center gap-6">
                  <Card
                    className="policy-selection-card-car cursor-pointer"
                    onClick={() => handleTypeSelection("CAR")}
                  >
                    <CardContent className="p-4 space-y-4">
                      <h2 className="text-5xl font-medium text-gray-500">🚗</h2>
                      <h3 className="text-xs text-gray-500">
                        Pólizas de Automóvil
                      </h3>

                    </CardContent>
                  </Card>
                  <Card
                    className="policy-selection-card-motorcycle cursor-pointer"
                    onClick={() => handleTypeSelection("MOTORCYCLE")}
                  >
                    <CardContent className="p-4 space-y-4">
                      <h4 className="text-5xl font-medium text-gray-500">🏍️</h4>
                      <h3 className="text-xs text-gray-500">
                        Pólizas de Motocicleta
                      </h3>

                    </CardContent>
                  </Card>
                </div>
              </div>
            )}

            {/* Step 1: File Upload */}
            {currentStep === 1 && (
              <div className="space-y-6">
                <PolicyFileUploadStep
                  onBack={handleBack}
                  onContinue={handleSubmit}
                  onFileSelect={(file) => setUploadedFile(file)}
                  onTermsAcceptanceChange={(accepted) => setTermsAccepted(accepted)}
                  isSubmitting={isSubmitting}
                />
              </div>
            )}

            {/* Step 2: Success */}
            {currentStep === 2 && (
              <div className="mt-8">
                <PolicySuccess />
              </div>
            )}
          </CardContent>
        </CardHeader>
      </div>
    </div>
  );
}
