import { db } from "@/lib/db";
import {
  transformPolicyInsuredPartiesData,
  transformInsuredPartiesData,
  type PolicyInsuredPartyData
} from "../utils/insured-party-transformer";

// Re-export the type for convenience
export type { PolicyInsuredPartyData };

export const PolicyInsuredPartiesService = {
  /**
   * Get all insured parties associated with a specific policy
   * This fetches from the PolicyInsuredParty join table to get the many-to-many relationship
   */
  async getInsuredPartiesForPolicy(policyId: string): Promise<PolicyInsuredPartyData[]> {
    try {
      const policyInsuredParties = await db.policyInsuredParty.findMany({
        where: {
          policyId: policyId
        },
        include: {
          insuredParty: true
        }
      });

      return transformPolicyInsuredPartiesData(policyInsuredParties);
    } catch (error) {
      console.error('Error fetching insured parties for policy:', error);
      return [];
    }
  },

  /**
   * Get all insured parties that belong to an account holder (reusable across policies)
   * This is useful for showing available insured parties when creating/editing policies
   */
  async getInsuredPartiesForAccountHolder(accountHolderId: string): Promise<PolicyInsuredPartyData[]> {
    try {
      const insuredParties = await db.insuredParty.findMany({
        where: {
          accountHolderId: accountHolderId
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      return transformInsuredPartiesData(insuredParties);
    } catch (error) {
      console.error('Error fetching insured parties for account holder:', error);
      return [];
    }
  },

  /**
   * Associate an existing insured party with a policy
   * This creates a record in the PolicyInsuredParty join table
   */
  async associateInsuredPartyWithPolicy(policyId: string, insuredPartyId: string): Promise<boolean> {
    try {
      await db.policyInsuredParty.create({
        data: {
          policyId,
          insuredPartyId
        }
      });
      return true;
    } catch (error) {
      console.error('Error associating insured party with policy:', error);
      return false;
    }
  },

  /**
   * Remove association between an insured party and a policy
   * This removes the record from the PolicyInsuredParty join table
   * Note: This doesn't delete the InsuredParty itself, just the association
   */
  async removeInsuredPartyFromPolicy(policyId: string, insuredPartyId: string): Promise<boolean> {
    try {
      await db.policyInsuredParty.delete({
        where: {
          policyId_insuredPartyId: {
            policyId,
            insuredPartyId
          }
        }
      });
      return true;
    } catch (error) {
      console.error('Error removing insured party from policy:', error);
      return false;
    }
  },

  /**
   * Get policies that an insured party is associated with
   * This is useful for showing which policies an insured party is covered by
   */
  async getPoliciesForInsuredParty(insuredPartyId: string): Promise<string[]> {
    try {
      const policyInsuredParties = await db.policyInsuredParty.findMany({
        where: {
          insuredPartyId: insuredPartyId
        },
        select: {
          policyId: true
        }
      });

      return policyInsuredParties.map(pip => pip.policyId);
    } catch (error) {
      console.error('Error fetching policies for insured party:', error);
      return [];
    }
  }
};
