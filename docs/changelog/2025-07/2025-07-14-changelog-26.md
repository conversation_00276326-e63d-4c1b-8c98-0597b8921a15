# Changelog 26 (2025-07-14)

## UI/UX Improvements & Consistency

This update focuses on aligning the application's user interface with the intended design system, specifically addressing color consistency, hover states, and typography across various components.

### 1. Refined Primary Color Definition
-   **Purpose:** Standardized the application's primary green color to a specific hex code (`#63E8A3`) for visual accuracy and consistency across all elements using the primary color.
-   **Changes:**
    -   Modified [`src/styles/globals.css`](src/styles/globals.css): Updated the `--primary` CSS variable's HSL value to `149.9 66.7% 65.1%`, which accurately renders `#63E8A3`.

### 2. Policy Filter Tabs (Top Bar)
-   **Purpose:** Ensured that selected policy filter tabs (e.g., "Todas 10", "Coche 7", "Moto 3") maintain their selected state appearance without unintended background or text color mutations on hover.
-   **Changes:**
    -   Modified [`src/features/account-holder/components/policy-list.tsx`](src/features/account-holder/components/policy-list.tsx):
        -   In the `getButtonClass` function, removed `hover:bg-primary/90` from the selected state to prevent background opacity changes on hover.
        -   Explicitly set `text-black` and `hover:text-black` for both selected and unselected states to ensure text color remains black and does not mutate on hover.

### 3. Clear Filter ("X") Button
-   **Purpose:** Standardized the appearance of the filter clear button (the "X" icon), ensuring its background is the primary green and its icon is white, consistent with the design.
-   **Changes:**
    -   Modified [`src/features/account-holder/components/policy-list.tsx`](src/features/account-holder/components/policy-list.tsx):
        -   Removed the `variant="ghost"` prop from the button.
        -   Applied `bg-primary` and `text-white` classes directly to ensure the correct background and icon color upon rendering.

### 4. Pagination Buttons ("Anterior" and "Siguiente")
-   **Purpose:** Aligned the "Anterior" and "Siguiente" pagination buttons to the primary green color with black text, matching the desired darker aesthetic without introducing an unwanted border.
-   **Changes:**
    -   Modified [`src/features/account-holder/components/policy-list.tsx`](src/features/account-holder/components/policy-list.tsx):
        -   Removed the `variant="outline"` prop from both buttons.
        -   Removed the previously added `border-2 border-black` class.
        -   Applied `bg-primary`, `text-black`, and `hover:bg-primary hover:text-black` classes to ensure the correct primary green background with solid black text, and no hover-induced color shifts.

### 5. Sidebar Navigation Hover Effects
-   **Purpose:** Eliminated unintended background changes on mouseover for the Zeeguros logo (icon and "Zeeguros" label) and the user's role label ("Asegurado") within the sidebar, ensuring static appearance.
-   **Changes:**
    -   Modified [`src/components/shared/app-sidebar.tsx`](src/components/shared/app-sidebar.tsx):
        -   Removed the `brand-hover` class from relevant `SidebarMenuButton` components (around lines 144 and 177) that wrap the logo and navigation items. This class was responsible for applying the unwanted hover background effect.

### 6. "Crear Nueva Subasta" Button Repositioning
-   **Purpose:** Repositioned the "Crear Nueva Subasta" button to the correct location as specified in the design, ensuring proper alignment and visual hierarchy within the policy list interface.
-   **Changes:**
    -   Modified [`src/features/account-holder/components/policy-list.tsx`](src/features/account-holder/components/policy-list.tsx):
        -   Moved the button from its previous position with the search input and asset filter buttons to the same row as the status filter dropdown.
        -   Applied `justify-between` layout to position the status filter controls on the left and the "Crear Nueva Subasta" button on the right.
        -   Restructured the layout containers to maintain proper responsive behavior across different screen sizes.
        -   Removed duplicate button instances to ensure the button appears only in the designated location.
### 7. Draft Status Badge Styling Fix
-   **Purpose:** Fixed the styling of "Borrador" (Draft) status badges to display in gray color instead of green, matching the design specification.
-   **Changes:**
    -   Modified [`src/features/policies/components/policy-status-badge.tsx`](src/features/policies/components/policy-status-badge.tsx):
        -   Added custom gray styling for draft status badges in the `getStatusClassName` function.
        -   Applied `border-transparent bg-gray-100 text-gray-800 hover:bg-gray-200` classes for draft status.
        -   Maintains consistency with the existing pattern used for other status colors (like yellow for "Renovar Pronto").

### 8. Policy Creation Stepper Color Update
-   **Purpose:** Updated the policy creation progress stepper to use the specified `#7AE4AE` color instead of the default green-600, aligning with the design requirements for the stepper component.
-   **Changes:**
    -   Modified [`src/features/account-holder/components/new-policy/PolicyProgressStepper.tsx`](src/features/account-holder/components/new-policy/PolicyProgressStepper.tsx):
        -   Replaced hardcoded `bg-green-600` classes with `bg-[#7AE4AE]` for both the progress line and step circle backgrounds.
        -   Updated the progress line background color (line 27) and active/completed step circle colors (lines 42, 44).
        -   Focused change limited to PolicyProgressStepper component only, maintaining existing functionality while implementing the requested color specification.

### 9. Policy Creation Button Hover State Standardization
-   **Purpose:** Standardized hover states across all buttons in the policy creation flow to use the design system's primary color tokens, ensuring consistent visual feedback and maintaining DRY principles.
-   **Changes:**
   -   Modified [`src/components/ui/file-upload.tsx`](src/components/ui/file-upload.tsx):
       -   Updated "Seleccionar archivo" button (line 240) to use `hover:bg-primary hover:border-primary hover:text-primary-foreground` classes instead of hardcoded color values.
   -   Modified [`src/features/account-holder/components/new-policy/PolicyFileUploadStep.tsx`](src/features/account-holder/components/new-policy/PolicyFileUploadStep.tsx):
       -   Updated "Atrás" button (line 164) to use design system hover classes: `hover:bg-primary hover:border-primary hover:text-primary-foreground`.
       -   Updated "Confirmar y crear subasta" button (line 173) to use consistent design system classes: `bg-primary hover:bg-primary hover:border-primary text-primary-foreground`.
   -   Modified [`src/app/account-holder/policies/new-policy/page.tsx`](src/app/account-holder/policies/new-policy/page.tsx):
       -   Updated return button (line 14) to use design system hover classes: `hover:bg-primary hover:border-primary hover:text-primary-foreground`.
   -   **Architecture Compliance:** All changes maintain 100% Screaming Architecture compliance by utilizing established design system tokens instead of creating new hardcoded color values, following DRY principles and ensuring consistency across the platform.

### 10. Policy Creation Flow UI Improvements
-   **Purpose:** Enhanced the policy creation flow with improved messaging, consistent styling, and better user experience across all steps.
-   **Changes:**
    -   Modified [`src/features/account-holder/components/new-policy/PolicyFileUploadStep.tsx`](src/features/account-holder/components/new-policy/PolicyFileUploadStep.tsx):
        -   Updated messaging from "Extrayendo datos..." to "Subiendo tu póliza..." for clarity (line 117).
        -   Updated toast message from "Extracción completada" to "Subida de tu póliza completada" (line 125).
        -   Centered terms and conditions checkbox using `justify-center` class for better alignment.
    -   Modified [`src/features/account-holder/components/new-policy/PolicySuccess.tsx`](src/features/account-holder/components/new-policy/PolicySuccess.tsx):
        -   Converted to sticky footer with sidebar-aware positioning using `useSidebar` hook.
        -   Implemented conditional left margin based on sidebar state for responsive behavior.
        -   Added two action buttons: "Ir a Mis Pólizas" (outline) and "Ver mi subasta" (primary).
    -   Modified [`src/features/account-holder/components/new-policy/policy-stepper.tsx`](src/features/account-holder/components/new-policy/policy-stepper.tsx):
        -   Updated card title for step 3: "¡Póliza Registrada con éxito! 🎉".
        -   Updated card description for step 3: "Tu póliza ha sido registrada y la subasta comenzará pronto.".
        -   Fixed TypeScript error with proper React fragments.
    -   Modified [`src/components/ui/file-upload.tsx`](src/components/ui/file-upload.tsx):
        -   Updated processing text styling to use brand-approved Lime Green background (`bg-[#3AE386]/20`) with black text (`text-black`) for better contrast (line 188).
        -   Added consistent hover styling to "Eliminar" button: `hover:bg-primary hover:border-primary hover:text-primary-foreground` to match other buttons (line 180).
-   **User Experience:** Eliminated confusion about data extraction vs file upload, improved visual consistency, and enhanced accessibility with better contrast ratios.

### 11. Toast System File Consolidation & DRY Optimization
-   **Purpose:** Optimized the toast notification system by consolidating redundant files while maintaining 100% functionality, improving maintainability and reducing code duplication.
-   **Changes:**
    -   **File Structure Optimization:**
        -   Consolidated [`src/components/ui/toaster.tsx`](src/components/ui/toaster.tsx) (35 lines) into [`src/components/ui/toast.tsx`](src/components/ui/toast.tsx) by moving the `Toaster` component.
        -   Reduced toast system from **3 files to 2 files**: UI components + renderer now in `toast.tsx`, state management in `use-toast.ts`.
        -   Updated [`src/app/layout.tsx`](src/app/layout.tsx) import from `@/components/ui/toaster` to `@/components/ui/toast`.
        -   **Removed:** `src/components/ui/toaster.tsx` file completely.
    -   **DRY Violations Eliminated:**
        -   Removed unused functions: `addToRemoveQueue`, `toastTimeouts` Map, and "success" variant.
        -   Introduced shared constant `TOAST_CONTAINER_STYLES` to eliminate duplicate styling across components.
        -   Consolidated all UI-related toast logic into single file for better organization.
    -   **Functionality Preservation:**
        -   Maintained circular green close buttons with `#6BE1A6` background color.
        -   Preserved immediate dismissal functionality and default blue toast styling.
        -   All existing toast behaviors remain unchanged.
    -   **Architectural Compliance:**
        -   Confirmed toast system belongs in [`src/components/ui/`](src/components/ui/) (not `src/components/shared/`) as it's a generic infrastructure component following shadcn/ui patterns.
        -   Maintains proper separation: UI Layer (toast.tsx) vs Logic Layer (use-toast.ts).
        -   Zero TypeScript errors introduced, maintaining code quality standards.
-   **Result:** Cleaner, more maintainable toast system with fewer files, no duplicate code, and preserved functionality while following all DRY and architectural principles.