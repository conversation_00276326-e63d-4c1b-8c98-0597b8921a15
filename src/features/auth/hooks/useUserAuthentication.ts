"use client";

import { useEffect, useState, useCallback, useRef } from "react";
import { createClient } from "@/lib/supabase/client";
import type { User } from "@supabase/supabase-js";

/**
 * Custom hook for user authentication state management
 * Only handles authentication state - all database operations must use server-side API routes
 */
export function useUserAuthentication(callback?: (user: User | null) => void) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const supabase = createClient();
  const callbackRef = useRef(callback);
  const subscriptionRef = useRef<{ subscription: { unsubscribe: () => void } } | null>(null);

  // Update callback ref when callback changes
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  const getInitialSession = useCallback(async () => {
    try {
      const {
        data: { session },
        error,
      } = await supabase.auth.getSession();
      if (error) {
        console.error("Error getting session:", error);
        setUser(null);
      } else {
        setUser(session?.user ?? null);
        if (callbackRef.current) {
          callbackRef.current(session?.user ?? null);
        }
      }
    } catch (error) {
      console.error("Error in getInitialSession:", error);
      setUser(null);
      if (callbackRef.current) {
        callbackRef.current(null);
      }
    } finally {
      setLoading(false);
    }
  }, [supabase.auth]);

  useEffect(() => {
    // Clean up any existing subscription
    if (subscriptionRef.current?.subscription) {
      subscriptionRef.current.subscription.unsubscribe();
      subscriptionRef.current = null;
    }

    getInitialSession();

    const { data } = supabase.auth.onAuthStateChange((_event, session) => {
      const currentUser = session?.user ?? null;
      setUser(currentUser);
      setLoading(false);

      if (callbackRef.current) {
        callbackRef.current(currentUser);
      }
    });

    subscriptionRef.current = data;

    return () => {
      if (subscriptionRef.current?.subscription) {
        subscriptionRef.current.subscription.unsubscribe();
        subscriptionRef.current = null;
      }
    };
  }, [supabase.auth, getInitialSession]);

  return { user, loading };
}
