# Changelog 41 - August 9, 2025

## Policy Card and Details Drawer UI Improvements

### Fixed
- **Policy Card Date Consistency**: Fixed font size inconsistency in policy cards where the expiration date value didn't match the "Vence:" label size. Both now use `text-sm` for consistent typography.

### Enhanced
- **Policy Details Drawer UX**: Improved the policy details drawer user experience with the following changes:
  - **Collapsed Sections by Default**: All accordion sections now start collapsed when the drawer opens, providing a cleaner initial view
  - **Status Badge Localization**: Added proper Spanish translation for "ACTIVE" status, now displays as "ACTIVA"
  - **Consistent Color Scheme**: Updated status badge colors to match the main policy cards:
    - ACTIVE status now uses primary green color (`bg-primary text-primary-foreground`) consistent with Zeeguros branding
    - EXPIRED status now uses destructive red color (`bg-destructive text-destructive-foreground`) matching the policy card styling

### Technical Changes
- Modified `PolicyDetailsDrawer.tsx`:
  - Changed accordion `defaultValue` from conditional opening to empty array `[]`
  - Enhanced `getStatusBadgeColor()` function to handle ACTIVE and EXPIRED statuses with proper color schemes
  - Enhanced `getStatusDisplayText()` function to translate ACTIVE status to Spanish
- Updated `policy-card.tsx`:
  - Added `text-sm` class to expiration date value for consistent sizing with label

### Impact
- Improved visual consistency across policy cards and detail views
- Better user experience with collapsed sections allowing users to focus on specific information
- Enhanced accessibility with proper Spanish localization
- Maintained Zeeguros brand color consistency throughout the application

## New Auction Management System for Account Holders

### Added
- **Auction List Component**: Created comprehensive auction management interface for account holders (`src/features/account-holder/components/auction-list.tsx`)
  - Paginated auction listing with search and filtering capabilities
  - Real-time auction status tracking with countdown timers
  - Asset type filtering (Car/Motorcycle) with dynamic counters
  - Status-based filtering (Open/Closed auctions)
  - Responsive grid layout for auction cards

- **Auction Summary Card**: Implemented reusable auction card component (`src/features/auctions/components/auction-summary-card.tsx`)
  - Dynamic status badges with color-coded urgency levels
  - Real-time countdown display for active auctions
  - Asset type icons and detailed auction information
  - Consistent styling with existing policy cards
  - Support for multiple auction statuses (OPEN, CLOSED, AWARDED, etc.)

- **Time Formatting Utilities**: Created comprehensive time formatting system (`src/features/auctions/utils/time-formatting.ts`)
  - `formatTimeRemaining()`: Formats auction countdown in human-readable format (e.g., "2d 5h", "3h 45m")
  - `getTimeUrgency()`: Determines urgency level based on remaining time
  - `getTimeRemainingBadgeClasses()`: Provides CSS classes for time-based styling
  - Handles edge cases for expired auctions and invalid dates

- **Auction Data Hooks**: Implemented React Query hooks for auction data management (`src/features/account-holder/hooks/useAuctions.ts`)
  - `useAuctions()`: Fetches paginated auction data with filtering and search
  - `useAuctionCounts()`: Provides real-time counters for auction categories
  - Comprehensive error handling and loading states
  - TypeScript interfaces for type safety

### Technical Features
- **Real-time Updates**: Auction countdown timers update dynamically
- **Advanced Filtering**: Multi-criteria filtering by status, asset type, and search terms
- **Pagination**: Efficient data loading with configurable page sizes
- **Error Handling**: Comprehensive error states with retry functionality
- **Responsive Design**: Mobile-first approach with adaptive layouts
- **Type Safety**: Full TypeScript support with defined interfaces

## Enhanced Policy Search Functionality

### Added
- **Accent-Insensitive Search**: Implemented comprehensive accent-insensitive search functionality for policy listings
  - Search queries like "maria" now correctly find policies for insured parties named "María"
  - Utilizes `normalizeForSearch()` utility function from `src/lib/text-utils.ts` for accent removal and normalization
  - Focuses exclusively on `InsuredParty` model fields (`firstName`, `lastName`, `displayName`)
  - Combines database-level case-insensitive search with post-processing accent-insensitive filtering

- **Enhanced Premium Search**: Significantly improved premium search capabilities with multiple matching strategies
  - **Exact Match**: Direct premium value matching for precise searches
  - **Range-Based Search**: Numeric searches now include a 1% tolerance range for flexible matching
  - **Partial String Match**: Supports partial premium value searches (e.g., "150" matches "1500.00")
  - **Prefix Matching**: Searches starting with specific digits find all matching premiums

### Technical Implementation
- **Database-Level Search**: Enhanced Prisma queries in `src/app/api/account-holder/policies/list/route.ts`
  - Case-insensitive search using `contains` with `mode: "insensitive"`
  - Premium search with both exact matching and range-based tolerance
  - Optimized query performance with targeted field searches

- **Post-Processing Filters**: Comprehensive client-side filtering for enhanced accuracy
  - Accent normalization using `normalizeForSearch()` utility
  - Multiple premium matching strategies (exact, tolerance-based, prefix)
  - Maintains search result consistency across different input formats

- **Text Utilities**: Leveraged existing `src/lib/text-utils.ts` infrastructure
  - `normalizeForSearch()`: Converts accented characters to base form and lowercases text
  - `matchesSearch()`: Performs accent-insensitive string comparisons
  - Reusable utilities for consistent search behavior across the platform

### Enhanced User Experience
- **Flexible Search Input**: Users can search using various formats and accented characters
- **Improved Accuracy**: Search results now include previously missed matches due to accent differences
- **Premium Search Versatility**: Multiple ways to search for premium values accommodate different user preferences
- **Consistent Results**: Combined database and post-processing approach ensures reliable search outcomes

### Impact
- Resolved search functionality issues where accent differences prevented finding relevant policies
- Enhanced premium search now supports partial matches and flexible number formats
- Improved overall search experience for Spanish-speaking users with accented names
- Maintained performance while adding comprehensive search capabilities

### Enhanced
- **Search Input Debounce Timing**: Increased search debounce delay from 400ms to 800ms in both policy and auction list components
  - Improved user experience when typing accented characters like "maría"
  - Users can now comfortably type full search terms without premature search triggering
  - Maintains search performance while providing better typing experience
  - Applied consistently across policy list (`PolicyList.tsx`) and auction list (`AuctionList.tsx`) components

### Fixed
- **Auction Search API Error**: Resolved PrismaClientValidationError in auction list search functionality
  - **Root cause**: Incorrect use of `contains` operator on UUID `id` field which doesn't support text operations
  - **Solution implemented**:
    - Removed problematic UUID search from `whereClause.OR`
    - Added proper accent-insensitive search using `normalizeForSearch` utility
    - Implemented enum-based insurer company search with accent normalization
    - Added vehicle brand/model search with case-insensitive matching
    - Included numeric premium search with exact and 1% tolerance range matching
  - **Files modified**: `src/app/api/account-holder/auctions/list/route.ts`
  - **User impact**: Auction search now works reliably without database errors

- **Auction Asset Type Icon Display**: Fixed incorrect asset type icon display in auction cards
  - **Root cause**: Component was using undefined `assetType` prop instead of `auction.assetType`, causing all auctions to default to car icon
  - **Solution implemented**:
    - Changed icon logic from `(assetType || "CAR")` to `(auction.assetType || "CAR")`
    - Removed unused `assetType` prop from component interface and function signature
    - Now correctly displays motorcycle icon for motorcycle policies and car icon for car policies
  - **Files modified**: `src/features/auctions/components/auction-summary-card.tsx`
  - **User impact**: Auction cards now display the correct asset type icon based on the actual auction data

## Seed Data Enhancements

### Fixed
- **Policy Expiration Dates in Seed Data**: Corrected policy expiration dates in `prisma/seed.ts` to align with business rules.
  - **Root cause**: Policies were generated with expiration dates in the past, violating the rule that policies can only enter an auction if they are 60 days or less from expiration.
  - **Solution implemented**:
    - Introduced a fixed "today" date (`2025-08-09`) for consistent data generation.
    - Adjusted policy `startDate` and `endDate` calculations to ensure all seeded policies expire within 60 days of this fixed "today" date.
    - Modified auction `startDate`, `endDate`, and `workingHoursClosedAt` to be relative to the fixed "today" date, ensuring `OPEN` auctions are in the future and other states are in the past.
    - Explicitly added the `OPEN` state to the `auctionStatesForDebugging` array to ensure a dedicated sample for consistent testing of `AuctionSummaryCard` styles.
  - **Files modified**: `prisma/seed.ts`
  - **User impact**: Provides more realistic and consistent test data for debugging `AuctionSummaryCard` styles across different auction states and ensures a dedicated `OPEN` auction sample for consistent debugging and testing.

## Summary

This changelog documents the resolution of a `PrismaClientValidationError` in the auction list search functionality, improvements to search timing for better user experience, and a fix for incorrect asset type icon display in auction cards.

## Changes Made

### 1. Search Timing Improvements
- **Files Modified**: 
  - `src/features/account-holder/components/policy-list.tsx`
  - `src/features/account-holder/components/auction-list.tsx`
- **Change**: Increased debounce delay from 400ms to 800ms
- **Reason**: Enhanced user experience by allowing users to complete typing search terms, especially beneficial for accented characters
- **Impact**: Reduces unnecessary API calls and improves search experience

### 2. Auction List Search API Fix
- **File Modified**: `src/app/api/account-holder/auctions/list/route.ts`
- **Root Cause**: `PrismaClientValidationError` due to using `contains` operator on UUID `id` field
- **Solutions Implemented**:
  - Removed problematic `id: { contains: ... }` search from whereClause
  - Added `normalizeForSearch` import and implementation for accent-insensitive search
  - Implemented enum-based search for `insurerCompany` field
  - Added vehicle search by brand and model with accent normalization
  - Included numeric premium search with exact match and 1% tolerance range
- **Impact**: Fixed search functionality and aligned with policy list search behavior

### 3. Auction Asset Type Icon Fix
- **File Modified**: `src/features/auctions/components/auction-summary-card.tsx`
- **Root Cause**: Component was using undefined `assetType` prop instead of `auction.assetType`, causing all auctions to default to car icon
- **Solutions Implemented**:
  - Changed icon logic from `(assetType || "CAR")` to `(auction.assetType || "CAR")`
  - Removed unused `assetType` prop from component interface and function signature
  - Now correctly displays motorcycle icon for motorcycle policies and car icon for car policies
- **Impact**: Auction cards now display the correct asset type icon based on the actual auction data

## Technical Details

### Auction Search API
The auction search API was failing because UUIDs don't support the `contains` operator in Prisma. The fix involved:
1. Removing the invalid UUID search
2. Implementing proper text search with accent normalization
3. Adding comprehensive search across relevant fields (insurer, vehicle, premium)
4. Maintaining consistency with the existing policy list search implementation

### Asset Type Icon Display
The auction summary card was incorrectly defaulting to car icons because:
1. The component expected an `assetType` prop that wasn't being passed from the auction list
2. The fallback logic used the undefined prop instead of the auction's actual `assetType` property
3. Fixed by using `auction.assetType` directly and cleaning up the unused prop

## UI Consistency Enhancements

### Changed
- **Auction Status Badge Styling**: Aligned the visual style of "PURCHASE_PENDING" and "PURCHASE_CONFIRMED" status badges in `auction-summary-card.tsx` with the "ACTIVE" status badge from `policy-card.tsx`.
  - **Before**: The badges had a light green background.
  - **After**: The badges now use the `default` variant, which renders a solid primary color background, to match the style of an active policy.
  - **Files modified**: `src/features/auctions/components/auction-summary-card.tsx`
  - **User impact**: This change creates a more cohesive and predictable user interface by ensuring that similar statuses are represented by consistent visual cues across different sections of the application.

- **Filter Button Spacing**: Reduced the spacing between the icon and text in the filter buttons on the auction list page.
  - **Before**: The buttons had a larger margin between the icon and the text.
  - **After**: The spacing has been reduced for a more compact and visually appealing look.
  - **Files modified**: `src/features/account-holder/components/auction-list.tsx`
  - **User impact**: This change improves the visual consistency of the UI and provides a cleaner look.

- **Expanded Auction Status Filters**: Added more comprehensive status filters to the auction list page.
  - **Before**: Only "Todas," "Abiertas," and "Cerradas" status filters were available.
  - **After**: The filter dropdown now includes all possible auction states: "Adjudicadas," "Acuerdo confirmado," "Compra pendiente," "Compra confirmada," "Canceladas," and "Expiradas."
  - **Files modified**: `src/features/account-holder/components/auction-list.tsx`, `prisma/schema.prisma`
  - **User impact**: Provides more granular control and better visibility over auction statuses, enhancing the user's ability to manage auctions.
  
  ## Testing
- ✅ Search functionality now works without errors
- ✅ Accent-insensitive search implemented
- ✅ Numeric premium search with tolerance
- ✅ Enum-based insurer company search
- ✅ Improved search timing reduces API calls
- ✅ Auction cards display correct asset type icons (motorcycle/car)