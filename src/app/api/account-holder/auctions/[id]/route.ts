import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/features/auth/utils/server-auth";
import { db } from "@/lib/db";
import { generateAuctionTimeline } from "@/features/auctions/utils/timeline-generator";
import { formatInsurerCompany } from "@/lib/format-insurer";

function toIdentifier(id: string) {
  return `ZEE-AU-${id.slice(-6).toUpperCase()}`;
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "No autorizado" }, { status: 401 });
    }

    const { id: auctionId } = await params;

    // Resolve account holder profile
    let accountHolderProfile = await db.accountHolderProfile.findUnique({ 
      where: { userId: user.id } 
    });
    
    // Create missing profile as a fallback for failed trigger execution
    if (!accountHolderProfile && user.role === "ACCOUNT_HOLDER") {
      try {
        accountHolderProfile = await db.accountHolderProfile.create({
          data: { userId: user.id }
        });
        console.log(`Created missing AccountHolderProfile for user ${user.id}`);
      } catch (error) {
        console.error(`Failed to create AccountHolderProfile for user ${user.id}:`, error);
        return NextResponse.json({ error: "Error al crear perfil de titular de cuenta" }, { status: 500 });
      }
    }
    
    if (!accountHolderProfile) {
      return NextResponse.json({ error: "Perfil de titular de cuenta no encontrado" }, { status: 404 });
    }

    // Get auction with all related data
    const auction = await db.auction.findFirst({
      where: {
        id: auctionId,
        accountHolderId: accountHolderProfile.id,
      },
      include: {
        policy: {
          include: {
            asset: {
              include: { 
                vehicleDetails: true 
              },
            },
            insuredParties: {
              include: {
                insuredParty: {
                  include: {
                    address: true,
                  },
                },
              },
            },
            coverages: true,
            document: true,
          },
        },
        bids: {
          include: {
            broker: {
              include: {
                user: true,
              },
            },
          },
          orderBy: {
            amount: 'asc', // Sort by lowest premium first
          },
        },
      },
    });

    if (!auction) {
      return NextResponse.json({ error: "Subasta no encontrada" }, { status: 404 });
    }

    // Transform auction data
    const id = auction.id;
    const identifier = toIdentifier(id);
    const premium = auction.policy?.premium?.toNumber() || 0;
    const insurer = auction.policy?.insurerCompany || null;

    let assetDisplayName = "Sin información del activo";
    const asset = auction.policy?.asset;
    if (asset?.vehicleDetails) {
      const v = asset.vehicleDetails;
      const brand = v.brand || "Sin marca";
      const model = v.model || "Sin modelo";
      const year = v.year || "Sin año";
      assetDisplayName = `${brand} ${model} (${year})`;
    } else if (asset?.description) {
      assetDisplayName = asset.description;
    }

    // Transform bids data
    const transformedBids = auction.bids.map((bid) => ({
      id: bid.id,
      annualPremium: bid.amount.toNumber(),
      brokerName: bid.broker.user.displayName || bid.broker.user.firstName || "Broker sin nombre",
      brokerCompany: bid.broker.insurerCompany ? formatInsurerCompany(bid.broker.insurerCompany) : "Empresa no especificada",
      createdAt: bid.createdAt.toISOString(),
      hasDocument: !!bid.documentId,
    }));

    // Transform policy data for PolicyDetailsDrawer compatibility
    const transformedPolicy = auction.policy ? {
      id: auction.policy.id,
      policyNumber: auction.policy.policyNumber,
      status: "ACTIVE", // Policies in auctions are typically active
      insurerCompany: auction.policy.insurerCompany,
      premium: auction.policy.premium?.toNumber() || 0,
      startDate: auction.policy.startDate?.toISOString() || null,
      endDate: auction.policy.endDate?.toISOString() || null,
      productName: auction.policy.productName,
      asset: auction.policy.asset ? {
        id: auction.policy.asset.id,
        assetType: auction.policy.asset.assetType,
        description: auction.policy.asset.description,
        vehicleDetails: auction.policy.asset.vehicleDetails ? {
          brand: auction.policy.asset.vehicleDetails.brand,
          model: auction.policy.asset.vehicleDetails.model,
          year: auction.policy.asset.vehicleDetails.year,
          licensePlate: auction.policy.asset.vehicleDetails.licensePlate,
          chassisNumber: auction.policy.asset.vehicleDetails.chassisNumber,
          firstRegistrationDate: auction.policy.asset.vehicleDetails.firstRegistrationDate?.toISOString() || null,
          version: auction.policy.asset.vehicleDetails.version,
          fuelType: auction.policy.asset.vehicleDetails.fuelType,
          powerCv: auction.policy.asset.vehicleDetails.powerCv,
          seats: auction.policy.asset.vehicleDetails.seats,
          usageType: auction.policy.asset.vehicleDetails.usageType,
          garageType: auction.policy.asset.vehicleDetails.garageType,
          kmPerYear: auction.policy.asset.vehicleDetails.kmPerYear,
          isLeased: auction.policy.asset.vehicleDetails.isLeased,
        } : null,
      } : null,
      insuredParties: auction.policy.insuredParties?.map(policyParty => ({
        id: policyParty.insuredParty.id,
        fullName: `${policyParty.insuredParty.firstName || ''} ${policyParty.insuredParty.lastName || ''}`.trim(),
        firstName: policyParty.insuredParty.firstName || '',
        lastName: policyParty.insuredParty.lastName || '',
        identification: policyParty.insuredParty.identification,
        roles: policyParty.insuredParty.roles || [],
        gender: policyParty.insuredParty.gender || '',
        email: '', // Email not available in InsuredParty model
        phone: '', // Phone not available in InsuredParty model
        birthDate: policyParty.insuredParty.birthDate?.toISOString() || null,
        address: policyParty.insuredParty.address?.street || '',
        postalCode: policyParty.insuredParty.address?.postalCode || '',
        regionName: policyParty.insuredParty.address?.region || '',
        country: policyParty.insuredParty.address?.country || '',
      })) || [],
      coverages: auction.policy.coverages?.map(coverage => ({
          id: coverage.id,
          title: coverage.customName || coverage.type,
          limit: coverage.limit,
          description: coverage.description,
          guaranteeType: coverage.type,
        })) || [],
      document: auction.policy.document ? {
        id: auction.policy.document.id,
        fileName: auction.policy.document.fileName,
        fileSize: auction.policy.document.fileSize,
        mimeType: auction.policy.document.mimeType,
        url: auction.policy.document.url,
        uploadedAt: auction.policy.document.uploadedAt.toISOString(),
      } : null,
    } : null;

    // Generate dynamic timeline events
    const timelineEvents = generateAuctionTimeline({
      id: auction.id,
      status: auction.status,
      startDate: auction.startDate,
      endDate: auction.endDate,
      createdAt: auction.createdAt,
      bids: auction.bids.map(bid => ({
        id: bid.id,
        createdAt: bid.createdAt,
        annualPremium: bid.amount.toNumber(),
        broker: {
          user: {
            displayName: bid.broker.user.displayName,
            firstName: bid.broker.user.firstName,
          }
        }
      }))
    });

    const response = {
      id,
      identifier,
      status: auction.status,
      startDate: auction.startDate?.toISOString() || null,
      endDate: auction.endDate?.toISOString() || null,
      annualPremium: premium,
      currency: "EUR",
      currentInsurer: insurer,
      assetDisplayName,
      assetType: asset?.assetType || null,
      quotesReceived: auction.bids.length,
      bids: transformedBids,
      policy: transformedPolicy,
      events: timelineEvents,
    };

    return NextResponse.json(response, {
      headers: { "Cache-Control": "no-store" },
    });
  } catch (error) {
    console.error("Error fetching auction details:", error);
    return NextResponse.json(
      { error: "Error interno del servidor" },
      { status: 500 }
    );
  }
}