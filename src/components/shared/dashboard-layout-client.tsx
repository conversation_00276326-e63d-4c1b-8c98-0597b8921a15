"use client";

import { ReactNode } from "react";

interface DashboardLayoutClientProps {
  children: ReactNode;
  userInitials: string;
}

export function DashboardLayoutClient({
  children,
  userInitials
}: DashboardLayoutClientProps) {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="flex h-screen">
        {/* Sidebar */}
        <div className="w-64 bg-white shadow-sm">
          <div className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                {userInitials}
              </div>
              <div className="text-sm font-medium text-gray-900">
                Dashboard
              </div>
            </div>
          </div>
        </div>
        
        {/* Main content */}
        <div className="flex-1 overflow-hidden">
          <div className="h-full overflow-y-auto">
            <div className="p-6">
              {children}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}