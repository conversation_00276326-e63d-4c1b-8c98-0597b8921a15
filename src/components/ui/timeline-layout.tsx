import React from 'react';
import { cn } from '@/lib/utils';
import {
  Timeline,
  TimelineItem,
} from './timeline';
import { Card, CardHeader, CardContent, CardTitle, CardDescription } from './card';
import { Check, X, Clock } from 'lucide-react';

// Define the structure for a timeline event
interface TimelineEvent {
  id: string;
  title: string;
  description?: string;
  time: string;
  status: 'completed' | 'pending';
  icon?: React.ReactNode;
}

// Example data for the timeline
const timelineEvents: TimelineEvent[] = [
  {
    id: '1',
    title: 'Auction Started',
    description: 'The auction has officially begun.',
    time: '2024-08-15 10:00 AM',
    status: 'completed',
    icon: <Check />,
  },
  {
    id: '2',
    title: 'First Bid Received',
    description: 'A bid of $500 was placed.',
    time: '2024-08-15 10:30 AM',
    status: 'completed',
    icon: <Check />,
  },
  {
    id: '3',
    title: 'Auction Ended',
    description: 'The auction is now closed.',
    time: '2024-08-16 10:00 AM',
    status: 'pending',
    icon: <Clock />,
  },
];

// Main layout component for the timeline
const TimelineLayout = () => {
  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardHeader>
        <CardTitle>Auction Timeline</CardTitle>
        <CardDescription>Follow the progress of the auction in real-time.</CardDescription>
      </CardHeader>
      <CardContent>
        <Timeline>
          {timelineEvents.map((event, index) => (
            <TimelineItem 
              key={event.id} 
              status={event.status}
              title={event.title}
              time={event.time}
              isLast={index === timelineEvents.length - 1}
            />
          ))}
        </Timeline>
      </CardContent>
    </Card>
  );
};

export default TimelineLayout;
