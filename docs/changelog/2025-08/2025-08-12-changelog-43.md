# Changelog - August 12, 2025

## UI/UX Improvements

### Sidebar Integration and Header Consistency
- **Fixed sidebar toggle visibility**: Moved sidebar toggle button from layout header into sticky page headers across all account-holder pages
- **Eliminated ghost padding**: Removed the layout header that was creating unwanted spacing above page content
- **Implemented consistent sticky headers**: All account-holder pages now use the same header structure with integrated sidebar trigger

### Pages Updated
- **Policies page** (`/account-holder/policies`): Updated PolicyList component to include sidebar trigger in sticky header
- **Settings page** (`/account-holder/settings`): Converted from PageLayout to custom sticky header structure
- **Support page** (`/account-holder/support`): Converted UnifiedSupportPage to custom sticky header structure
- **Auctions page** (`/account-holder/auctions`): Converted AuctionList component to custom sticky header structure

### Technical Changes
- **Enhanced PageLayout component**: Added `showSidebarTrigger` prop for conditional sidebar trigger display
- **Converted components to client components**: Added `"use client"` directive where needed for React hooks
- **Added scroll-based shadow effects**: Implemented consistent shadow behavior on scroll across all pages
- **Maintained existing functionality**: All forms, filters, cards, and interactive elements preserved

### Benefits
- Sidebar toggle button remains accessible when scrolling on any page
- Consistent user experience across all account-holder sections
- Improved visual hierarchy with proper spacing

## Mobile Responsiveness Improvements for Policy and Auction Lists

### Overview
Enhanced mobile user experience for policy and auction list pages by optimizing KPI cards, filter buttons, and pagination controls for smaller screen sizes.

### KPI Cards Mobile Optimization
- **Reduced spacing**: Changed gap from `gap-4` to `gap-2 sm:gap-4` for tighter spacing on mobile devices
- **Less cramped padding**: Reduced padding from `p-4` to `p-3 sm:p-4` to prevent overcrowding
- **Smaller text on mobile**: Changed font size from `text-2xl` to `text-xl sm:text-2xl` for better proportion
- **Optimized margins**: Reduced internal spacing with `mt-0.5` and `pr-2` for cleaner mobile layout
- **Applied to both**: Policy list (Borradores, Activas, Requieren Atención) and Auction list (Abiertas, Cerradas, Póliza firmada) KPI cards

### Filter Buttons Mobile Enhancement
- **Prevent wrapping**: Changed from `flex-col sm:flex-row` to always `flex` to keep buttons in one horizontal line
- **Tighter spacing**: Reduced gap from `gap-2` to `gap-1 sm:gap-2` for better mobile fit
- **Smaller text**: Added `text-xs sm:text-sm` classes for appropriate mobile font sizes
- **Reduced padding**: Changed to `px-2 sm:px-4` for more compact buttons on mobile
- **Smaller icons**: Changed icon size from `h-4 w-4` to `h-3 w-3 sm:h-4 sm:w-4` for better mobile proportion
- **Better flex behavior**: Added `flex-1 min-w-0` to prevent overflow and ensure equal button widths
- **Consistent across pages**: Applied to both "Todas, Coche, Moto" filter buttons in policy and auction lists

### Pagination Controls Mobile Optimization
- **Improved layout**: Changed from single row to stacked layout on mobile with `flex-col gap-3 sm:flex-row`
- **Better spacing**: Increased top padding from `pt-1` to `pt-4` for better visual separation
- **Centered alignment**: Added `justify-center` for better mobile centering of pagination controls
- **Smaller text**: Applied `text-xs sm:text-sm` for appropriate mobile font sizes
- **Compact buttons**: Reduced button padding to `px-2 sm:px-3` for mobile optimization
- **Smaller dropdown**: Changed dropdown width from `w-20` to `w-16 sm:w-20` for mobile
- **Tighter gaps**: Reduced gaps from `gap-2` to `gap-1 sm:gap-2` for better mobile spacing
- **Enhanced readability**: Added `px-2` padding to page indicator for better visual separation
- **Applied consistently**: Same improvements to both policy and auction list pagination

### Technical Implementation Details
- **Files Modified**:
  - `src/features/account-holder/components/policy-list.tsx`: KPI cards, filter buttons, and pagination improvements
  - `src/features/account-holder/components/auction-list.tsx`: KPI cards, filter buttons, and pagination improvements
- **Responsive Design Approach**: Used Tailwind CSS responsive prefixes (`sm:`) for progressive enhancement from mobile-first design
- **Maintained Functionality**: All existing filtering, pagination, and interaction behaviors preserved
- **Cross-browser Compatibility**: Changes use standard CSS flexbox and grid properties for broad browser support

### Mobile UX Benefits
- **Reduced visual clutter**: KPI cards no longer appear cramped on small screens
- **Improved usability**: Filter buttons remain accessible without horizontal scrolling
- **Better readability**: Appropriately sized text and spacing for mobile viewing
- **Enhanced navigation**: Pagination controls are easier to tap and read on mobile devices
- **Consistent experience**: Uniform improvements across both policy and auction list pages
- Better responsive design with integrated navigation controls

### Policy List Pagination Enhancements
- **Improved pagination spacing**: Increased top padding from `pt-8` to `pt-12` and bottom padding from `pb-4` to `pb-6` to create better visual separation between policy cards and pagination controls
- **Always show page indicator**: Fixed pagination to always display "Página X de X" label even when there's only one page (e.g., "Página 1 de 1"), providing better context about pagination state
- **Centered dropdown number**: Added `text-center` class to the items-per-page dropdown to center the selected number (6, 12, 24) within the button
- **Consistent pagination styling**: Standardized all pagination text to use `text-sm text-gray-600` for uniform appearance across "Anterior", "Página X de X", "Siguiente", "Mostrar", and "elementos" labels

### Additional Technical Details
- Modified `src/features/account-holder/components/policy-list.tsx` for pagination improvements
- Removed conditional rendering of page indicator (`pagination.totalPages > 1`)
- Enhanced SelectTrigger with centered text alignment
- Improved visual hierarchy and consistency in pagination controls

## Layout Consistency Improvements

### Auction List Layout Standardization
- **Implemented consistent header section**: Updated "Mis Subastas" page to match the exact layout structure of "Mis Pólizas" page
- **Redesigned stats cards**: Changed from large cards with `p-4` padding to compact cards with `p-3` padding, using `flex justify-between items-center` for right-aligned numbers
- **Standardized search and filter layout**: Implemented two-row structure matching policies page:
  - **Row 1**: Search bar with asset type buttons (Todas, Coche, Moto)
  - **Row 2**: Status filter dropdown, clear button, and "Crear nueva subasta" button positioned on the right
- **Updated content grid**: Changed to `md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-3` with `gap-3` spacing to match policies
- **Enhanced empty state**: Added FileText icon and conditional create button for better user experience
- **Standardized pagination**: Implemented exact same pagination structure as policies with proper spacing (`pt-1 pb-8`) and items per page options (6, 12, 24)

### Visual Consistency Enhancements
- **Unified spacing**: Reduced gaps and margins throughout to match policies page (`gap-2`, `mb-2`, `space-y-2`)
- **Consistent color scheme**: Maintained Zeeguros brand colors across all UI elements
- **Typography standardization**: Applied consistent text sizes and colors throughout the interface
- **Improved card styling**: Updated stats cards to use proper border colors (gray-400, green-500, orange-500) with compact layout

### Technical Implementation
- **Updated component structure**: Modified `src/features/account-holder/components/auction-list.tsx` to match policy list architecture
- **Added missing imports**: Included `FileText` icon from lucide-react for empty state
- **Fixed JSX structure**: Properly nested KPI cards within sticky header section
- **Removed unused imports**: Cleaned up `useMemo` import that was no longer needed
- **Enhanced error handling**: Maintained all existing functionality while improving layout consistency

### Benefits
- **Consistent user experience**: Users now have identical navigation and layout patterns across policies and auctions
- **Improved visual hierarchy**: Better spacing and alignment create cleaner, more professional appearance
- **Enhanced usability**: Standardized filter and search patterns reduce cognitive load
- **Maintainable codebase**: Consistent component structures make future updates easier

## Responsive Design and Title Handling Improvements

### KPI Cards Responsive Layout Enhancement
- **Improved KPI card responsiveness**: Updated both "Mis Pólizas" and "Mis Subastas" KPI cards layout
  - **Changed breakpoints**: From `grid-cols-1 sm:grid-cols-2 lg:grid-cols-3` to `grid-cols-1 md:grid-cols-3`
  - **Enhanced text containers**: Added `min-w-0 flex-1 pr-3` classes for better text overflow handling
  - **Fixed number positioning**: Added `flex-shrink-0` to KPI numbers to prevent shrinking
  - **Result**: Eliminates awkward 2-column stacking on medium screens, provides cleaner mobile-to-desktop transition

### Policy and Auction Title Handling
- **Improved long identifier display**: Enhanced title formatting for policy numbers (e.g., "POL-CAR-2024-007") and auction identifiers (e.g., "ZEE-AU-03D68F")
  - **Added responsive title structure**: Labels ("Póliza:", "Subasta:") now break to new lines on small screens
  - **Implemented monospace formatting**: Applied `font-mono text-sm` to identifiers for better readability
  - **Enhanced text wrapping**: Added `break-words` class to prevent overflow issues
  - **Added tooltips**: Implemented `truncate` with `title` attributes for asset descriptions

### Card Grid Layout Optimization
- **Updated responsive breakpoints**: Changed card grids from `grid-cols-1 sm:grid-cols-2 lg:grid-cols-3` to `grid-cols-1 md:grid-cols-2 xl:grid-cols-3`
  - **Mobile**: Single column layout for optimal readability
  - **Medium screens**: Two-column layout for balanced content display
  - **Extra-large screens**: Three-column layout for maximum content density

### Auction Summary Card Internal Layout
- **Enhanced internal responsiveness**: Updated auction card content grid from fixed `grid-cols-3` to responsive `grid-cols-1 sm:grid-cols-3`
- **Improved text overflow handling**: Added `min-w-0` and `truncate` classes to prevent content overflow
- **Better mobile stacking**: Increased `gap-y-3` for improved spacing when content stacks vertically
- **Enhanced data display**: Better formatting for insurer names, annual premiums, and offer counts

### Technical Implementation
- **Files modified**:
  - `src/features/account-holder/components/policy-list.tsx`: KPI cards and card grid responsive improvements
  - `src/features/account-holder/components/auction-list.tsx`: KPI cards and card grid responsive improvements
  - `src/features/policies/components/policy-card.tsx`: Policy title handling and text overflow fixes
  - `src/features/auctions/components/auction-summary-card.tsx`: Auction title handling and internal layout improvements

### Benefits
- **Improved mobile experience**: Better content stacking and readability on small screens
- **Enhanced tablet usability**: Optimal 2-column layout prevents awkward spacing on medium screens
- **Better text handling**: Long identifiers and descriptions now display properly without overflow
- **Consistent responsive behavior**: Unified breakpoint strategy across all card layouts
- **Professional appearance**: Monospace identifiers and proper text truncation improve visual quality

## Filter Layout Reorganization

### Two-Column Filter Layout Implementation
- **Implemented consistent two-column layout structure**: Reorganized filter controls across policy and auction list pages to improve organization and user experience
- **Left Column Structure**:
  - Search input field for policies/auctions
  - Asset type filter buttons (Todas, Coche, Moto) with counts
- **Right Column Structure**:
  - Status filter dropdown with all status options
  - Clear filter button (appears when filter is active)
  - "Crear nueva subasta" action button

### Technical Implementation Details
- **Responsive layout**: Used `flex flex-col lg:flex-row` for responsive column layout
- **Flexible sizing**: Left column uses `flex-1` to take available space
- **Proper alignment**: Right column uses `lg:items-end` for proper alignment on larger screens
- **Mobile-first design**: Maintains responsive behavior - stacks vertically on mobile, side-by-side on desktop
- **Preserved functionality**: All existing functionality and styling maintained

### Pages Updated
- **Policy List Page** (`src/features/account-holder/components/policy-list.tsx`): Applied two-column filter layout
- **Auction List Page** (`src/features/account-holder/components/auction-list.tsx`): Applied identical two-column filter layout for consistency

### Benefits
- **Improved visual organization**: Cleaner separation of search/filter vs action controls
- **Enhanced user experience**: Consistent layout patterns across both pages reduce cognitive load
- **Better alignment**: Related controls are now properly grouped and aligned
- **Responsive design**: Layout adapts seamlessly across different screen sizes
- **Maintainable structure**: Consistent component architecture makes future updates easier

## Navigation Flow Improvements

### "Crear nueva subasta" Button Navigation Update
- **Fixed navigation routing**: Updated "Crear nueva subasta" button in "Mis Subastas" page to navigate to the correct route
- **Route correction**: Changed navigation from `/account-holder/auctions/new-auction` to `/account-holder/policies/new-policy`
- **Consistent user flow**: Now properly directs users to the "Nueva Subasta" page with the updated header layout
- **Multiple button instances updated**: Fixed both the main action button and the empty state button

### Technical Implementation
- **File modified**: `src/features/account-holder/components/auction-list.tsx`
- **Updated Link components**: Changed `href` attribute from `/account-holder/auctions/new-auction` to `/account-holder/policies/new-policy`
- **Maintained functionality**: All existing button styling, icons, and text preserved
- **Consistent behavior**: Both primary and empty state "Crear nueva subasta" buttons now navigate to the same correct destination

### Benefits
- **Correct user flow**: Users clicking "Crear nueva subasta" now reach the proper page with consistent header layout
- **Improved navigation consistency**: Aligns with the updated page structure and header improvements
- **Better user experience**: Eliminates potential confusion from incorrect routing
- **Unified auction creation process**: Streamlines the path from auction list to auction creation

## Auction Details Page UI/UX Refinements

### Layout and Content Realignment
- **Summary Cards**: four main summary cards ("Mejor Oferta," "Ahorro Potencial," "Estado," and "Tiempo Restante") to align with the original design.
- **Added "Tiempo Restante" Card**: Implemented a new summary card to display the remaining time for an auction, which updates dynamically.
- **Corrected "Qué ocurre ahora?" Content**: Restored the original, user-approved text for the "¿Qué ocurre ahora?" card.
- **Reverted "Cronología de la subasta"**: Updated the auction timeline to match the static design, ensuring visual consistency.
- **Two-Column Layout**: Reorganized the "Detalles" tab into a two-column layout, placing the "Póliza actual" and "¿Qué ocurre ahora?" cards in the left column and the "Cronología de la subasta" in the right column.
- **Added "Póliza Actual" Card**: Introduced a new card to display the current policy number and a "Ver detalles" button.

### Technical Implementation
- **File Modified**: `src/features/account-holder/components/AuctionDetailsView.tsx`
- **State Management**: Added `useState` and `useEffect` to manage the `timeRemaining` state for the countdown timer.
- **Component Structure**: Refactored the JSX to create the two-column layout and integrate the new and restored cards.
- **Iconography**: Added the `Clock` icon from `lucide-react` for the "Tiempo Restante" card.

### Benefits
- **Design Consistency**: The auction details page now accurately reflects the intended design provided by the user.
- **Improved User Experience**: The layout is more intuitive and provides a clearer overview of the auction status.
- **Enhanced Information Display**: The "Tiempo Restante" card offers crucial, at-a-glance information to the user.

## Dynamic Timeline Implementation

### Overview

The auction timeline component has been successfully converted from using mock data to dynamic data based on real auction information from the database.

### Changes Made

#### 1. Timeline Generator Utility (`src/features/auctions/utils/timeline-generator.ts`)

Created a new utility function `generateAuctionTimeline` that:

- **Generates auction start event**: "Subasta iniciada" when the auction begins
- **Creates bid events**: Groups bids by day to avoid timeline clutter
  - First bid: "Primera oferta recibida de [Broker Name]"
  - Subsequent bids: "Nueva oferta recibida de [Broker Name]" or "[X] nuevas ofertas recibidas" for multiple bids per day
- **Adds auction end event**: "Subasta finalizada" (completed) or "Subasta finaliza" (pending)
- **Handles status logic**: Events are marked as 'completed' or 'pending' based on current time and auction status

#### 2. API Endpoint Update (`src/app/api/account-holder/auctions/[id]/route.ts`)

Modified the auction details API to:

- Import and use the `generateAuctionTimeline` function
- Include dynamic `events` in the API response
- Remove dependency on mock data

#### 3. Component Update (`src/components/AuctionDetailsView.tsx`)

Updated the auction details component to:

- Remove mock timeline data from `fetchAuctionDetails`
- Update `TimelineEvent` interface to match the new structure
- Remove unused status mappings

#### 4. Testing Infrastructure

Created testing tools to verify the implementation:

- **Test script**: `scripts/test-timeline.ts` - Verifies timeline generation with real data
- **NPM script**: `npm run test:timeline` - Easy way to run the test
- **Seed data**: Comprehensive auction data with bids for testing

### Data Structure

#### TimelineEvent Interface
```typescript
interface TimelineEvent {
  id: string;
  title: string;
  time: string;
  status: 'completed' | 'pending';
}
```

#### AuctionData Interface
```typescript
interface AuctionData {
  id: string;
  status: AuctionState;
  startDate: Date;
  endDate: Date;
  createdAt: Date;
  bids: Array<{
    id: string;
    createdAt: Date;
    broker: {
      user: {
        displayName?: string | null;
        firstName?: string | null;
      };
    };
  }>;
}
```

### Features

#### Smart Bid Grouping
- Bids are grouped by day to prevent timeline clutter
- First bid gets special treatment: "Primera oferta recibida"
- Multiple bids per day are summarized: "X nuevas ofertas recibidas"

#### Localized Time Display
- All timestamps are formatted in Spanish locale
- Format: "10 Ago 2025, 09:00"

#### Dynamic Status Handling
- Events are marked as 'completed' if they occurred in the past
- Future events (like auction end) are marked as 'pending'
- Auction status affects the final event title

### Benefits

1. **Real Data**: Timeline now reflects actual auction activity
2. **Scalable**: Handles any number of bids efficiently
3. **Localized**: All text and dates in Spanish
4. **Smart Grouping**: Prevents timeline clutter with many bids
5. **Status Aware**: Correctly shows completed vs pending events
6. **Testable**: Comprehensive testing infrastructure
### Future Enhancements

Potential improvements could include:
- Adding more event types (auction extensions, winner announcements)
- Including bid amounts in timeline events
- Adding broker profile links
- Implementing real-time timeline updates
- Adding timeline filtering options

## TypeScript Error Resolution

### Timeline Component Type Safety Improvements

#### Issues Resolved

**1. Timeline Generator Type Errors (`src/features/auctions/utils/timeline-generator.ts`)**
- **Problem**: `dayKey` variable was being inferred as `string | undefined` causing TypeScript errors in Map operations
- **Solution**: Added non-null assertion operator (`!`) to `bid.createdAt.toISOString().split('T')[0]!` since the operation is guaranteed to return a string
- **Impact**: Eliminated type errors in `groupBidsByDay` function without compromising type safety

**2. Timeline Layout Component Errors (`src/components/ui/timeline-layout.tsx`)**
- **Problem**: Importing non-existent timeline components (`TimelineConnector`, `TimelineHeader`, `TimelineTitle`, etc.)
- **Solution**: Removed all non-existent imports and updated to use only the actual `Timeline` and `TimelineItem` components
- **Problem**: Using unsupported `'failed'` status in timeline events
- **Solution**: Updated interface to only support `'completed' | 'pending'` statuses and removed the failed event example
- **Problem**: Incorrect `TimelineItem` component usage with nested JSX structure
- **Solution**: Updated to use the correct props structure: `title`, `time`, `status`, and `isLast`

#### Technical Implementation

**Timeline Generator Fix**:
```typescript
// Before (causing type errors)
const dayKey = bid.createdAt.toISOString().split('T')[0]; // string | undefined

// After (type-safe)
const dayKey = bid.createdAt.toISOString().split('T')[0]!; // string - guaranteed to exist
```

**Timeline Layout Restructure**:
```typescript
// Before (non-existent components)
<TimelineItem key={event.id} status={event.status}>
  <TimelineConnector />
  <TimelineHeader>
    <TimelineIcon status={event.status}>{event.icon}</TimelineIcon>
    <TimelineTitle>{event.title}</TimelineTitle>
    <TimelineTime>{event.time}</TimelineTime>
  </TimelineHeader>
  <TimelineContent>
    <TimelineDescription>{event.description}</TimelineDescription>
  </TimelineContent>
</TimelineItem>

// After (correct component usage)
<TimelineItem 
  key={event.id} 
  status={event.status}
  title={event.title}
  time={event.time}
  isLast={index === timelineEvents.length - 1}
/>
```

#### Benefits

- **Type Safety**: All TypeScript errors eliminated without compromising type checking
- **Component Compatibility**: Timeline layout now correctly uses the actual timeline components
- **Maintainability**: Proper component structure makes future updates easier
- **Code Quality**: Cleaner, more readable component implementation
- **Integration Ready**: Timeline layout can now be safely integrated with the dynamic timeline system

#### Files Modified

- `src/features/auctions/utils/timeline-generator.ts`: Fixed type inference for date string operations
- `src/components/ui/timeline-layout.tsx`: Updated to use correct timeline component structure and removed unsupported features

#### Testing Status

- All TypeScript compilation errors resolved
- Timeline generator maintains full functionality with improved type safety
- Timeline layout component properly integrates with existing timeline system
- No breaking changes to existing dynamic timeline implementation

## DetailsCard Component Refactoring and Alignment Improvements

### Overview

Refactored the CollapsibleCard component into a non-collapsible DetailsCard component and resolved alignment issues with action buttons in the auction details view.

### Changes Made

#### 1. Component Refactoring (`src/components/ui/details-card.tsx`)

**File Rename and Component Transformation**:
- **Renamed file**: `collapsible-card.tsx` → `details-card.tsx` to reflect new purpose
- **Component rename**: `CollapsibleCard` → `DetailsCard` for clarity
- **Removed collapsible functionality**: Eliminated all expansion/collapse logic, state management, and chevron buttons
- **Added action prop**: Introduced optional `action?: React.ReactNode` prop for header actions
- **Made children optional**: Updated `DetailsCardProps` to make `children` optional for header-only cards
- **Conditional content rendering**: `CardContent` only renders when `children` are provided

**Enhanced Props Interface**:
```typescript
interface DetailsCardProps {
  title: string;
  action?: React.ReactNode;
  children?: React.ReactNode;
}
```

#### 2. AuctionDetailsView Integration (`src/features/account-holder/components/AuctionDetailsView.tsx`)

**Import and Usage Updates**:
- **Updated import**: Changed from `CollapsibleCard` to `DetailsCard` and updated import path
- **Removed defaultExpanded props**: Eliminated all `defaultExpanded` attributes as component is no longer collapsible
- **Button repositioning**: Moved "Ver detalles" button from card content to header via `action` prop
- **Improved button styling**: Applied `size="sm"` for more compact button appearance

**Policy Card Enhancement**:
```typescript
<DetailsCard 
  title="Póliza actual" 
  action={
    <Button 
      variant="outline" 
      size="sm"
      onClick={() => setIsPolicyDrawerOpen(true)}
    >
      Ver detalles
    </Button>
  }
/>
```

### Technical Implementation

#### Component Structure Improvements
- **Static display**: Component now provides consistent, always-visible content display
- **Header-focused design**: Optimized for title and action button combinations
- **Flexible content**: Supports both header-only and header-with-content configurations
- **Clean styling**: Maintained existing card styling while removing interactive elements

#### Alignment Resolution
- **Button positioning**: "Ver detalles" button now properly aligned in card header
- **Consistent spacing**: Improved visual hierarchy with proper padding and margins
- **Responsive design**: Button maintains proper alignment across different screen sizes

### Benefits

1. **Improved Alignment**: "Ver detalles" button now properly positioned in card header instead of floating at bottom
2. **Simplified Component**: Removed unnecessary complexity of collapsible functionality
3. **Better UX**: Consistent, predictable card behavior across the application
4. **Enhanced Flexibility**: Action prop allows for various header actions beyond just buttons
5. **Cleaner Code**: Eliminated unused state management and event handlers
6. **Type Safety**: Improved TypeScript interfaces with optional props

### Files Modified

- `src/components/ui/collapsible-card.tsx` → `src/components/ui/details-card.tsx`: Complete component refactoring
- `src/features/account-holder/components/AuctionDetailsView.tsx`: Updated imports, component usage, and button positioning

### Migration Notes

- All `CollapsibleCard` instances updated to `DetailsCard`
- `defaultExpanded` props removed as no longer applicable
- Action buttons moved from content to header via `action` prop
- Import paths updated to reflect new component location and name