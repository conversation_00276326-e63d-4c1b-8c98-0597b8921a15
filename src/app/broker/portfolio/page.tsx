"use client";

import { useState, useEffect } from "react";
import { createClient } from "@/lib/supabase/client";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Search, Car, FileText, Map, Wallet, Phone, Bike, Truck } from "lucide-react";

interface Client {
  id: number;
  name: string;
  AssetsType: "car" | "motorcycle" | "van";
  paymentType: string;
  location: string;
  contact: string;
  policyCount: number;
  renewalMonths: number;
}

// Mock client data
const mockClients: Client[] = [
  {
    id: 1,
    name: "<PERSON>",
    AssetsType: "car",
    paymentType: "Mensual",
    location: "Madrid",
    contact: "654 321 987",
    policyCount: 2,
    renewalMonths: 1,
  },
  {
    id: 2,
    name: "<PERSON>",
    AssetsType: "motorcycle",
    paymentType: "Anual",
    location: "Barcelona",
    contact: "612 345 678",
    policyCount: 1,
    renewalMonths: 3,
  },
  {
    id: 3,
    name: "<PERSON>",
    AssetsType: "van",
    paymentType: "Semestral",
    location: "Valencia",
    contact: "678 901 234",
    policyCount: 3,
    renewalMonths: 6,
  },
  {
    id: 4,
    name: "Ana Martínez",
    AssetsType: "car",
    paymentType: "Anual",
    location: "Sevilla",
    contact: "645 678 901",
    policyCount: 1,
    renewalMonths: 2,
  },
  {
    id: 5,
    name: "Pablo Sánchez",
    AssetsType: "motorcycle",
    paymentType: "Mensual",
    location: "Bilbao",
    contact: "623 456 789",
    policyCount: 2,
    renewalMonths: 0,
  },
];

const ClientCard = ({ client }: { client: Client }) => {
  const getAssetIcon = (type: string) => {
    switch (type) {
      case "car":
        return <Car className="h-5 w-5" />;
      case "motorcycle":
        return <Bike className="h-5 w-5" />;
      case "van":
        return <Truck className="h-5 w-5" />;
      default:
        return <Car className="h-5 w-5" />;
    }
  };

  const getRenewalBadge = (months: number) => {
    if (months === 0) {
      return <Badge variant="destructive">Renovación urgente</Badge>;
    } else if (months <= 2) {
      return <Badge variant="secondary">Renovación próxima</Badge>;
    }
    return <Badge variant="outline">{months} meses</Badge>;
  };

  return (
    <Card className="cursor-pointer hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="rounded-full bg-primary/10 p-2">
              {getAssetIcon(client.AssetsType)}
            </div>
            <div>
              <CardTitle className="text-lg">{client.name}</CardTitle>
              <p className="text-sm text-muted-foreground flex items-center gap-1">
                <Map className="h-3 w-3" />
                {client.location}
              </p>
            </div>
          </div>
          {getRenewalBadge(client.renewalMonths)}
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-4">
          <div className="flex items-center gap-2">
            <FileText className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm">{client.policyCount} pólizas</span>
          </div>
          <div className="flex items-center gap-2">
            <Wallet className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm">{client.paymentType}</span>
          </div>
          <div className="flex items-center gap-2 col-span-2">
            <Phone className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm">{client.contact}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default function BrokerPortfolioPage() {
  const [userName, setUserName] = useState<string>("Corredor");
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredClients, setFilteredClients] = useState<Client[]>(mockClients);
  const supabase = createClient();

  useEffect(() => {
    const getUserData = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        setUserName(user.user_metadata?.first_name || "Corredor");
      }
    };

    getUserData();
  }, [supabase]);

  useEffect(() => {
    if (searchQuery.trim() === "") {
      setFilteredClients(mockClients);
    } else {
      const query = searchQuery.toLowerCase();
      setFilteredClients(
        mockClients.filter(
          (client) =>
            client.name.toLowerCase().includes(query) ||
            client.location.toLowerCase().includes(query) ||
            client.contact.includes(query)
        )
      );
    }
  }, [searchQuery]);

  return (
    <div className="flex w-full flex-col overflow-hidden">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Cartera</h1>
        <div className="flex items-center gap-2">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Buscar cliente..."
              className="pl-9 w-[250px]"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Button>Añadir Cliente</Button>
        </div>
      </div>

      <div className="flex gap-2 mb-4">
        <Badge variant="default" className="px-3 py-1">
          Todos (12)
        </Badge>
        <Badge variant="secondary" className="px-3 py-1">
          <Car className="mr-1 h-3 w-3" />
          Coches (8)
        </Badge>
        <Badge variant="secondary" className="px-3 py-1">
          <FileText className="mr-1 h-3 w-3" />
          Pólizas múltiples (5)
        </Badge>
        <Badge variant="destructive" className="px-3 py-1">
          Renovación próxima (3)
        </Badge>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {filteredClients.map((client) => (
          <ClientCard key={client.id} client={client} />
        ))}
      </div>
    </div>
  );
}