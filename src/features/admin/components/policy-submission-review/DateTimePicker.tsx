"use client";

import * as React from "react";
import { format } from "date-fns";
import { Calendar as CalendarIcon } from "lucide-react";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

interface DateTimePickerProps {
  value: Date | null | undefined;
  onChange: (date: Date | undefined) => void;
  disabled?: boolean;
  showTime?: boolean;
}

export function DateTimePicker({
  value,
  onChange,
  disabled,
  showTime = true,
}: DateTimePickerProps) {
  const [open, setOpen] = React.useState(false);

  const dateValue = value ? new Date(value) : undefined;

  const handleDateSelect = (selectedDate: Date | undefined) => {
    if (!selectedDate) {
      onChange(undefined);
      return;
    }
    // Keep existing time if available, otherwise default to midnight
    const hours = dateValue?.getHours() ?? 0;
    const minutes = dateValue?.getMinutes() ?? 0;
    const seconds = dateValue?.getSeconds() ?? 0;
    
    const newDate = new Date(
      selectedDate.getFullYear(),
      selectedDate.getMonth(),
      selectedDate.getDate(),
      hours,
      minutes,
      seconds
    );
    onChange(newDate);
    setOpen(false);
  };

  const handleTimeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const time = e.target.value;
    if (!time || !dateValue) return;

    const timeParts = time.split(":");
    const hoursString = timeParts[0];
    const minutesString = timeParts[1];

    if (hoursString !== undefined) {
      const hours = parseInt(hoursString, 10);
      const minutes = minutesString ? parseInt(minutesString, 10) : 0;

      if (!isNaN(hours) && !isNaN(minutes)) {
        const newDate = new Date(dateValue);
        newDate.setHours(hours);
        newDate.setMinutes(minutes);
        newDate.setSeconds(0);
        onChange(newDate);
      }
    }
  };

  const timeValue = dateValue
    ? `${String(dateValue.getHours()).padStart(2, "0")}:${String(
        dateValue.getMinutes()
      ).padStart(2, "0")}`
    : "";

  return (
    <div className="flex items-center gap-2">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant={"outline"}
            className={cn(
              "w-full justify-start text-left font-normal",
              !dateValue && "text-muted-foreground"
            )}
            disabled={disabled}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {dateValue ? (
              format(dateValue, "dd/MM/yyyy")
            ) : (
              <span>Selecciona una fecha</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            mode="single"
            selected={dateValue}
            onSelect={handleDateSelect}
            initialFocus
            disabled={disabled}
          />
        </PopoverContent>
      </Popover>
      {showTime && (
        <Input
          type="time"
          value={timeValue}
          onChange={handleTimeChange}
          className="w-[110px]"
          disabled={!dateValue || disabled}
        />
      )}
    </div>
  );
}