import { createClient } from '@/lib/supabase/server';
import { db } from '@/lib/db';
import { Role } from '@prisma/client';
import { ROLES_CONFIG } from '@/features/auth/config/roles';

export const AuthService = {
  async signIn(credentials: any) {
    const supabase = await createClient();
    const { data, error } = await supabase.auth.signInWithPassword(credentials);

    if (error) {
      switch (error.message) {
        case 'Invalid login credentials':
          return { error: 'Correo electrónico o contraseña incorrectos' };
        case 'Email not confirmed':
          return { error: 'Por favor, confirma tu correo electrónico antes de iniciar sesión' };
        default:
          return { error: error.message };
      }
    }

    return { data };
  },

  async signUp(formData: FormData) {
    const supabase = await createClient();
    const email = formData.get('email') as string;
    const password = formData.get('password') as string;
    const firstName = formData.get('firstName') as string;
    const lastName = formData.get('lastName') as string;
    const phone = formData.get('phone') as string;

    if (!email || !password || !firstName || !lastName) {
      return {
        success: false,
        error: 'Todos los campos son obligatorios',
      };
    }

    const metadata = {
      first_name: firstName.trim(),
      last_name: lastName.trim(),
      phone: phone ? phone.trim() : '',
    };

    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: email.trim(),
      password,
      options: {
        data: {
          ...metadata,
          role: 'ACCOUNT_HOLDER',
        },
      },
    });

    if (authError) {
      switch (authError.message) {
        case 'User already registered':
          return { success: false, error: 'Este correo electrónico ya está registrado' };
        case 'Password should be at least 6 characters':
          return { success: false, error: 'La contraseña debe tener al menos 6 caracteres' };
        case 'Unable to validate email address':
          return { success: false, error: 'El correo electrónico no es válido' };
        default:
          return {
            success: false,
            error: 'Error al crear la cuenta. Por favor, inténtalo de nuevo.',
          };
      }
    }

    return { success: true, email: email.trim() };
  },

  async getUser() {
    const supabase = await createClient();
    return supabase.auth.getUser();
  },

  async getUserRole() {
    const { data } = await this.getUser();
    return data.user?.user_metadata?.role as Role | undefined;
  },

  getHomeRouteForRole(role?: Role) {
    return role ? ROLES_CONFIG[role].home : '/';
  },
};