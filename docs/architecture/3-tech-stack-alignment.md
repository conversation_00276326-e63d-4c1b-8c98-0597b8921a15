# 3. Tech Stack Alignment

## 3.1 Current Technology Stack

**✅ ESTABLISHED STACK:**
- **Framework:** Next.js 15+ (App Router with role-based organization)
- **Language:** TypeScript
- **UI Framework:** React with Tailwind CSS and Shadcn UI components
- **Backend:** Next.js API Routes with server-side security patterns
- **Database & ORM:** PostgreSQL via Supabase with Prisma ORM
- **Authentication:** Supabase Auth with role-based access control
- **AI & ML:** Google Gemini API for document processing
- **Email & SMTP:** Brevo for transactional communications
- **Deployment:** OCI ARM Ampere VPS with Docker containers via Dokploy
- **Infrastructure:** Cloudflare (DNS, CDN, R2 Storage) + <PERSON>inger (domains)
- **Key Libraries:** Zod validation, React Hook Form, TanStack Query
- **Linter:** ESLint with TypeScript support.

## 3.2 Architecture Patterns

**✅ IMPLEMENTED PATTERNS:**
- **Screaming Architecture:** 100% business domain organization
- **Role-Based Access Control (RBAC):** Complete user role segregation
- **Server-Side Security:** Mandatory API route pattern for all data operations
- **Domain-Driven Design:** Clear business domain boundaries
- **Component Composition:** Reusable UI components with clear boundaries

## 3.3 Enhancement Compatibility

**New Enhancement Requirements:**
- Must maintain 100% Screaming Architecture compliance
- Must follow established security patterns (server-side API routes only)
- Must respect role-based organization principles
- Must maintain TypeScript A+ compliance
- Must follow DRY principles and existing code patterns

---
