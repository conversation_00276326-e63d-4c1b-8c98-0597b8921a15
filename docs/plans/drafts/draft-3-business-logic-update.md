Enhance the Brevo email service to implement a comprehensive notification system for auction lifecycle management with the following requirements:

## 1. Administrative Notifications System

**Target Recipients:** System administrators at `<EMAIL>`

**Events requiring admin notifications:**
- **Auction Created**: When a new auction is initiated (status changes to OPEN)
- **Auction Closed**: When an auction expires or is manually closed (status changes to CLOSED)
- **Winners Selected**: When the system automatically selects the top 3 winning bidders
- **System Errors**: When auction processing encounters errors or failures

**Admin Email Template Requirements:**
- **Subject Format**: `[ZEEGUROS-ADMIN] [EVENT_TYPE] - Auction ${auctionId} - Policy ${policyNumber}`
- **Content Structure**: Technical details including:
  - Auction ID, Policy ID, and database references
  - Execution timestamps and processing duration
  - System metrics (total bids, participant count, winner selection criteria)
  - Direct links to admin dashboard: `${ADMIN_DASHBOARD_URL}/auctions/${auctionId}`
  - Error details and troubleshooting information (if applicable)
- **Tone**: Professional, technical, system-oriented language
- **Format**: Structured HTML with clear sections for quick scanning

## 2. Automatic Winner Selection System

**Business Logic Update:**
- **Remove manual winner selection**: The system must automatically select winners when auction closes
- **Selection Criteria**: Rank bidders by two factors (in order of priority):
  1. **Lowest price offered** (primary criterion)
  2. **Most comprehensive coverage** (secondary criterion)
- **Winner Count**: Automatically select top 3 bidders based on ranking algorithm
- **Contact Data Revelation**: Upon winner selection, automatically reveal contact information bidirectionally:
  - Winners can see account holder contact details
  - Account holder can see winners' contact details

**Winner Notification Requirements:**
- **Immediate notification**: Send emails to all 3 selected winners simultaneously
- **Winner email content**: Include their ranking position (1st, 2nd, 3rd), winning bid amount, and account holder contact information
- **Account holder notification**: Send summary email with all 3 winners' details and contact information

## 3. Implementation Requirements

**Code Modifications:**
- **Update `BrevoEmailService` class**:
  - Add `sendAdminAuctionNotification(adminData: AdminNotificationData)` method
  - Add `sendWinnerNotification(winnerData: WinnerNotificationData)` method
  - Add `sendAccountHolderWinnersNotification(winnersData: WinnersSelectionData)` method
  - Modify existing methods to trigger admin notifications as secondary actions

- **Update Edge Function** (`supabase/functions/sendAuctionNotification/index.ts`):
  - Add admin notification handling for all auction events
  - Implement automatic winner selection algorithm
  - Add winner notification dispatch logic
  - Ensure proper error handling and logging for all notification types

- **Database Integration**:
  - Update `notification_log` table to track admin and winner notifications separately
  - Add notification type enum: `'admin_auction_created'`, `'admin_auction_closed'`, `'winner_selected'`, `'account_holder_winners'`
  - Ensure admin notifications don't impact customer quota tracking

**Email Template Structure:**
- **Admin templates**: Technical, system-focused with actionable information
- **Winner templates**: Professional, congratulatory with clear next steps and contact details
- **Account holder templates**: Summary format with winner comparison table and contact information

**Environment Variables:**
- Add `ADMIN_EMAIL="<EMAIL>"` to environment configuration
- Add `ADMIN_DASHBOARD_URL` for direct admin links

## 4. Testing Requirements

**Test Scenarios:**
- Verify admin notifications are sent for each auction lifecycle event
- Test automatic winner selection algorithm with various bid scenarios
- Confirm bidirectional contact information revelation works correctly
- Validate that admin emails don't count against customer quotas
- Test error handling and admin error notifications

**Success Criteria:**
- All auction events trigger appropriate notifications to all relevant parties
- Winner selection is fully automated based on specified criteria
- Contact information is revealed automatically upon winner selection
- Admin notifications provide actionable technical information
- System maintains audit trail of all notifications in database