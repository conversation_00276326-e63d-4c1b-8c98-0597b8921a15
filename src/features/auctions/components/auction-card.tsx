"use client";

import React from "react";
import { ZCard } from "@/components/shared/z-card";
import { Badge } from "@/components/ui/badge";
import { cn, formatCurrency } from "@/lib/utils";
import { formatTimeRemaining } from "@/features/auctions/utils/time-formatting";
import { Clock } from "lucide-react";
import { AssetType } from "@/types/policy";

export interface AuctionCardData {
  id: string;
  clientName: string;
  coverageType: string;
  timeRemaining: string;
  currentPremium: number;
  assetType?: AssetType | null;
}

interface AuctionCardProps {
  auction: AuctionCardData;
  className?: string;
}

export function AuctionCard({ auction, className }: AuctionCardProps) {
  const getAssetTypeIcon = (assetType: AssetType): string => {
    return "⚖️";
  };

  return (
    <ZCard variant="policy" className={cn("transition-all hover:shadow-lg p-3 h-full w-full min-w-[280px]", className)}>
      <div className="space-y-3 flex flex-col h-full">
        {/* Header with Asset Icon, Title and Status Badge */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3 min-w-0 flex-1">
            <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-br from-primary/10 to-primary/20 border border-primary/20 flex items-center justify-center text-sm shadow-sm">
              {getAssetTypeIcon((auction.assetType || "CAR") as AssetType)}
            </div>
            <div className="min-w-0 flex-1">
              <div className="flex items-center justify-between">
                <h3 className="font-bold text-sm text-foreground truncate">
                  {auction.clientName}
                </h3>
                <Badge variant="outline" className="px-2 py-1 text-xs font-semibold bg-emerald-100 text-emerald-800 border-emerald-200">
                  <Clock className="w-3 h-3 mr-1" /> {auction.timeRemaining}
                </Badge>
              </div>
              <div className="text-xs text-muted-foreground truncate mt-0.5">
                {auction.coverageType}
              </div>
            </div>
          </div>
        </div>

        {/* Main Content - Compact Layout */}
        <div className="flex-grow space-y-2">
          {/* Compact Info Grid */}
          <div className="grid grid-cols-1 gap-2 text-xs">
            {/* Premium info */}
            <div className="flex justify-between items-center">
              <div className="min-w-0 flex-1">
                <div className="text-[10px] font-semibold text-muted-foreground uppercase tracking-wide">
                  Prima Actual
                </div>
                <div className="font-semibold text-foreground">
                  {formatCurrency(auction.currentPremium)}/año
                </div>
              </div>
              <div className="text-right">
                <div className="text-[10px] font-semibold text-muted-foreground uppercase tracking-wide">
                  Estado
                </div>
                <div className="text-muted-foreground text-[11px]">
                  Activa
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ZCard>
  );
}