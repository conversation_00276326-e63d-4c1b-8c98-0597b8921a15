import { Lightbulb } from "lucide-react";
import { cn } from "@/lib/utils";

interface TipBoxProps {
  className?: string;
}

export function TipBox({ className }: TipBoxProps) {
  return (
    <div
      className={cn(
        "flex flex-col items-center space-y-2 rounded-lg bg-accent/5 p-4 text-center",
        className,
      )}
    >
      <div className="flex items-center space-x-2">
        <Lightbulb className="h-5 w-5 text-accent" />
        <h4 className="text-lg font-semibold text-accent">Consejo</h4>
      </div>
      <p className="text-sm text-muted-foreground">
        Para mejores resultados, asegúrate de que el documento esté bien iluminado y sea legible. Nuestro sistema extraerá automáticamente los datos principales.
      </p>
    </div>
  );
}