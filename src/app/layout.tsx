import "@/styles/globals.css";

import { Inter } from "next/font/google";
import { Toaster } from "@/components/ui/toast";
import { QueryProvider } from "@/app/_components/providers/query-client-provider";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-sans",
});

export const metadata = {
  title: "Zeeguros",
  description: "Plataforma de seguros",
  icons: {
    icon: "/icon.svg",
    apple: "/apple-icon.svg",
    shortcut: "/icon.svg",
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // This suppresses the hydration warning caused by browser extensions
  // that add attributes to the body element
  const suppressHydrationWarning = true;

  return (
    <html lang="es">
      <body className={`font-sans ${inter.variable}`} suppressHydrationWarning={suppressHydrationWarning}>
        <QueryProvider>
          {children}
        </QueryProvider>
        <Toaster />
      </body>
    </html>
  );
}
