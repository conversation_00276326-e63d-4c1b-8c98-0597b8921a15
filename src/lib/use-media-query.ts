"use client";

import { useEffect, useState } from "react";

/**
 * A custom hook to detect if the current viewport matches a mobile media query.
 * Defaults to a max-width of 768px.
 *
 * @param query The media query string to match.
 * @returns `true` if the media query matches, otherwise `false`.
 */
export function useMediaQuery(query: string = "(max-width: 767px)") {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    const mediaQueryList = window.matchMedia(query);
    const listener = () => setMatches(mediaQueryList.matches);

    // Initial check
    listener();

    // Listen for changes
    mediaQueryList.addEventListener("change", listener);

    return () => {
      mediaQueryList.removeEventListener("change", listener);
    };
  }, [query]);

  return matches;
}