-- Migration: Update database functions to use environment variables
-- This migration updates the notification functions to retrieve the service role key
-- from environment variables instead of using hardcoded values

-- Helper function to get service role key from environment variables
CREATE OR REPLACE FUNCTION public.get_service_role_key()
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    service_key TEXT;
BEGIN
    -- Get service role key from environment variable
    -- In Supabase, environment variables are accessible through current_setting
    BEGIN
        service_key := current_setting('app.supabase_service_role_key', true);
        
        -- If not found in app settings, try vault.secrets (Supabase Vault)
        IF service_key IS NULL OR service_key = '' THEN
            SELECT decrypted_secret INTO service_key
            FROM vault.decrypted_secrets
            WHERE name = 'SUPABASE_SERVICE_ROLE_KEY'
            LIMIT 1;
        END IF;
        
        -- Fallback: return empty string if no key found
        IF service_key IS NULL THEN
            service_key := '';
        END IF;
        
        RETURN service_key;
    EXCEPTION WHEN OTHERS THEN
        -- Log warning and return empty string on error
        RAISE WARNING 'Failed to retrieve service role key: %', SQLERRM;
        RETURN '';
    END;
END;
$$;

-- Grant execute permission on the helper function
GRANT EXECUTE ON FUNCTION public.get_service_role_key TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_service_role_key TO service_role;

-- Update the comprehensive auction notifications function
CREATE OR REPLACE FUNCTION public.trigger_comprehensive_auction_notifications()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    closed_auction_ids UUID[];
    created_auction_ids UUID[];
    edge_function_url TEXT;
    request_payload JSONB;
    response_id BIGINT;
    service_role_key TEXT;
BEGIN
    -- Get service role key from environment variables
    service_role_key := public.get_service_role_key();
    
    -- Edge Function URL
    edge_function_url := 'https://etzouzmoluegjbfikshb.supabase.co/functions/v1/sendAuctionNotification';

    -- Find auctions that were created in the last 5 minutes (for admin notifications)
    SELECT ARRAY_AGG(id) INTO created_auction_ids
    FROM public.auction
    WHERE status = 'OPEN'::auction_state
      AND created_at >= NOW() - INTERVAL '5 minutes'
      AND created_at <= NOW()
      -- Only notify for auctions that haven't been notified yet
      AND NOT EXISTS (
          SELECT 1 FROM public.notification_log nl
          WHERE nl.auction_id = auction.id
            AND nl.notification_type = 'ADMIN_AUCTION_CREATED'
            AND nl.status = 'sent'
      );

    -- Find auctions that were closed in the last 5 minutes
    SELECT ARRAY_AGG(id) INTO closed_auction_ids
    FROM public.auction
    WHERE status = 'CLOSED'::auction_state
      AND updated_at >= NOW() - INTERVAL '5 minutes'
      AND updated_at <= NOW()
      -- Only notify for auctions that haven't been notified yet
      AND NOT EXISTS (
          SELECT 1 FROM public.notification_log nl
          WHERE nl.auction_id = auction.id
            AND nl.notification_type = 'AUCTION_CLOSED'
            AND nl.status = 'sent'
      );

    -- Process auction creation notifications (admin only)
    IF created_auction_ids IS NOT NULL AND array_length(created_auction_ids, 1) > 0 THEN
        -- Prepare request payload for auction creation
        request_payload := jsonb_build_object(
            'type', 'auction_created',
            'auctionIds', to_jsonb(created_auction_ids)
        );

        -- Make HTTP request to Edge Function
        BEGIN
            SELECT net.http_post(
                url := edge_function_url,
                headers := jsonb_build_object(
                    'Content-Type', 'application/json',
                    'Authorization', 'Bearer ' || service_role_key
                ),
                body := request_payload
            ) INTO response_id;
        EXCEPTION WHEN OTHERS THEN
            -- Log error but don't fail the entire process
            RAISE WARNING 'Failed to trigger auction creation notifications: %', SQLERRM;
        END;
    END IF;

    -- Process auction closure notifications (admin + account holder + winner selection)
    IF closed_auction_ids IS NOT NULL AND array_length(closed_auction_ids, 1) > 0 THEN
        -- Prepare request payload for auction closure
        request_payload := jsonb_build_object(
            'type', 'auction_closed',
            'auctionIds', to_jsonb(closed_auction_ids)
        );

        -- Make HTTP request to Edge Function
        BEGIN
            SELECT net.http_post(
                url := edge_function_url,
                headers := jsonb_build_object(
                    'Content-Type', 'application/json',
                    'Authorization', 'Bearer ' || service_role_key
                ),
                body := request_payload
            ) INTO response_id;
        EXCEPTION WHEN OTHERS THEN
            -- Log error but don't fail the entire process
            RAISE WARNING 'Failed to trigger auction closure notifications: %', SQLERRM;
        END;
    END IF;
END;
$$;

-- Update the manual trigger function
CREATE OR REPLACE FUNCTION public.trigger_comprehensive_notifications_manual(
    auction_ids UUID[] DEFAULT NULL,
    notification_type TEXT DEFAULT 'auction_closed'
)
RETURNS TABLE(success BOOLEAN, message TEXT, auction_count INTEGER)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    target_auction_ids UUID[];
    edge_function_url TEXT;
    request_payload JSONB;
    response_id BIGINT;
    service_role_key TEXT;
BEGIN
    -- Get service role key from environment variables
    service_role_key := public.get_service_role_key();
    
    -- Use provided auction IDs or find recent auctions based on type
    IF auction_ids IS NOT NULL THEN
        target_auction_ids := auction_ids;
    ELSE
        IF notification_type = 'ADMIN_AUCTION_CREATED' THEN
            SELECT ARRAY_AGG(id) INTO target_auction_ids
            FROM public.auction
            WHERE status = 'OPEN'::auction_state
              AND created_at >= NOW() - INTERVAL '1 hour'
            LIMIT 5;
        ELSE
            SELECT ARRAY_AGG(id) INTO target_auction_ids
            FROM public.auction
            WHERE status = 'CLOSED'::auction_state
              AND updated_at >= NOW() - INTERVAL '1 hour'
            LIMIT 5;
        END IF;
    END IF;

    -- If no auctions found, return early
    IF target_auction_ids IS NULL OR array_length(target_auction_ids, 1) = 0 THEN
        RETURN QUERY SELECT false, 'No auctions found for notification type: ' || notification_type, 0;
        RETURN;
    END IF;

    -- Edge Function URL
    edge_function_url := 'https://etzouzmoluegjbfikshb.supabase.co/functions/v1/sendAuctionNotification';

    -- Prepare request payload
    request_payload := jsonb_build_object(
        'type', notification_type,
        'auctionIds', to_jsonb(target_auction_ids)
    );

    -- Make HTTP request
    BEGIN
        SELECT net.http_post(
            url := edge_function_url,
            headers := jsonb_build_object(
                'Content-Type', 'application/json',
                'Authorization', 'Bearer ' || service_role_key
            ),
            body := request_payload
        ) INTO response_id;

        RETURN QUERY SELECT true, 'Notifications triggered successfully', array_length(target_auction_ids, 1);

    EXCEPTION WHEN OTHERS THEN
        RETURN QUERY SELECT false, 'Failed to trigger notifications: ' || SQLERRM, array_length(target_auction_ids, 1);
    END;
END;
$$;

-- Log successful migration
DO $$
BEGIN
    RAISE NOTICE 'Successfully updated database functions to use environment variables for service role key';
END $$;