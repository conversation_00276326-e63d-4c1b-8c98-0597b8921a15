'use client';

import React, { useState } from 'react';
import { useFormState } from 'react-dom';
import { selectWinners } from '@/features/account-holder/auctions/actions/select-winners';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardT<PERSON>le, CardFooter } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { Auction, AuctionBid } from '@prisma/client';
import { useEffect } from 'react';

type AuctionWithBids = Auction & {
  bids: AuctionBid[];
};

interface WinnerSelectionFormProps {
  auction: AuctionWithBids;
}

type FormState = {
  message: string;
  success: boolean;
  auction?: Auction | null;
};

const initialState: FormState = {
  message: '',
  success: false,
};

export function WinnerSelectionForm({ auction }: WinnerSelectionFormProps) {
  const [selectedBids, setSelectedBids] = useState<string[]>([]);
  const [formState, formAction] = useFormState(selectWinners, initialState);
  const { toast } = useToast();

  const handleCheckboxChange = (bidId: string) => {
    setSelectedBids((prev) => {
      if (prev.includes(bidId)) {
        return prev.filter((id) => id !== bidId);
      }
      if (prev.length < 3) {
        return [...prev, bidId];
      }
      return prev;
    });
  };

  useEffect(() => {
    if (formState?.message) {
      if (formState.success) {
        toast({
          title: 'Éxito',
          description: formState.message,
        });
        // Optionally, you could redirect the user or update the UI further
      } else {
        toast({
          variant: 'destructive',
          title: 'Error',
          description: formState.message,
        });
      }
    }
  }, [formState, toast]);


  return (
    <Card>
      <form action={formAction}>
        <input type="hidden" name="auctionId" value={auction.id} />
        <CardHeader>
          <CardTitle>Seleccionar Ganadores</CardTitle>
          <p className="text-sm text-gray-500">Elige hasta 3 de las mejores ofertas.</p>
        </CardHeader>
        <CardContent className="space-y-4">
          {auction.bids.map((bid) => (
            <div key={bid.id} className="flex items-center space-x-2 p-2 rounded-md border">
              <Checkbox
                id={bid.id}
                name="bidIds"
                value={bid.id}
                checked={selectedBids.includes(bid.id)}
                onCheckedChange={() => handleCheckboxChange(bid.id)}
                disabled={selectedBids.length >= 3 && !selectedBids.includes(bid.id)}
              />
              <Label htmlFor={bid.id} className="flex-1 text-sm font-medium">
                Oferta de €{bid.amount.toFixed(2)}
              </Label>
            </div>
          ))}
        </CardContent>
        <CardFooter>
          <Button type="submit" disabled={selectedBids.length === 0}>
            Confirmar Selección
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
}