# Changelog - 2025-06-28

## Features & Refactoring

- **"Partes Aseguradas" UI/UX Overhaul & AI Integration**:
  - **Schema Refactoring**: Restructured the data model to support profile reuse by creating a new `Person` model to store core individual data (`fullName`, `dni`, etc.). The `InsuredParty` model was refactored to act as a linking table between `Policy` and `Person`, storing only the `role` for that specific policy. This eliminates data duplication and enables the "Search Existing Profile" feature.
  - **AI-First Workflow**: The "Partes Aseguradas" section was refactored to be AI-first. The Gemini API prompt was updated to extract a nested `insuredParties` array, aligning the AI output with the new multi-party data model.
  - **Mobile-First UI**: Implemented a responsive, mobile-first UI for managing insured parties, featuring a summary view and an "Add/Edit Party" modal. The UI now correctly handles various screen sizes and resolutions.
  - **Full "Edit" Functionality**: Added full "Edit" functionality to the `AddPartyModal`, allowing users to correct or update AI-extracted data.

## Fixes

- **Modal Behavior**:
  - Fixed a bug that caused the "Añadir Nueva Parte" modal to open automatically and prevented it from being closed. The modal's visibility is now explicitly controlled with a dedicated state variable.
  - Corrected an issue where the "Editar Parte" modal would not close when the "X" button was clicked.
- **UI & Styling**:
  - Resolved a styling issue where the active state color was not correctly applied to all tabs in the wizard.
  - Fixed a layout bug that caused the navigation tabs to overflow on smaller screens by implementing `flex-wrap`.
  - Removed redundant titles and descriptions from the main wizard page to create a cleaner layout.
  - Adjusted the styling of the "Añadir Parte" button to match the design of the "Seleccionar Archivo" button, ensuring a consistent look and feel.
  - Reduced the vertical spacing in the policy creation wizard by removing redundant padding from the main layout and page containers, resulting in a more compact and visually appealing interface.
- **Form Logic**:
  - Fixed a bug where the conditional visibility of the address fields was not working correctly in the "Edit" mode. The address fields are now displayed only when the "Tomador" (Policy Holder) role is selected.
  - Removed redundant UI elements ("Profile Sourcing" and "Save Profile" options) from the "Edit" mode to streamline the user experience.