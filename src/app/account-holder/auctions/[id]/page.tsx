import { Suspense } from "react";
import { AuctionDetailsView } from "@/features/account-holder/components/AuctionDetailsView";

interface AuctionDetailsPageProps {
  params: {
    id: string;
  };
}

export default async function AuctionDetailsPage({ params }: AuctionDetailsPageProps) {
  const { id } = await params;
  
  return (
    <Suspense fallback={<div>Cargando detalles de la subasta...</div>}>
      <AuctionDetailsView auctionId={id} />
    </Suspense>
  );
}
