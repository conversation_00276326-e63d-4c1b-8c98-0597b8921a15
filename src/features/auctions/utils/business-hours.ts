import { addHours, isWeekend, setHours, setMinutes, setSeconds, setMilliseconds, addDays, getDay } from 'date-fns';

/**
 * Checks if a given date is a weekday (Monday to Friday).
 * @param date The date to check.
 * @returns True if the date is a weekday, false otherwise.
 */
const isWeekday = (date: Date): boolean => {
  const day = getDay(date);
  return day >= 1 && day <= 5; // 1 = Monday, 5 = Friday
};

/**
 * Calculates the closing time for an auction based on business hours.
 * Business hours are Monday to Friday, 9:00 to 19:00.
 *
 * @param startDate The date when the auction starts.
 * @param durationHours The total duration of the auction in business hours.
 * @returns The calculated closing date and time.
 */
export const calculateBusinessHoursClosingTime = (startDate: Date, durationHours: number): Date => {
  let remainingHours = durationHours;
  let currentDate = new Date(startDate);

  // If the start date is outside business hours, adjust to the next business day's start.
  if (isWeekend(currentDate) || currentDate.getHours() >= 19) {
    currentDate = addDays(currentDate, isWeekend(currentDate) ? (8 - getDay(currentDate)) % 7 : 1);
    currentDate = setHours(setMinutes(setSeconds(setMilliseconds(currentDate, 0), 0), 0), 9);
  } else if (currentDate.getHours() < 9) {
    currentDate = setHours(setMinutes(setSeconds(setMilliseconds(currentDate, 0), 0), 0), 9);
  }

  while (remainingHours > 0) {
    if (isWeekday(currentDate)) {
      const hoursToday = 19 - currentDate.getHours();
      const hoursToDeduct = Math.min(remainingHours, hoursToday);

      currentDate = addHours(currentDate, hoursToDeduct);
      remainingHours -= hoursToDeduct;

      if (remainingHours > 0) {
        // Move to the start of the next business day
        currentDate = addDays(currentDate, getDay(currentDate) === 5 ? 3 : 1); // If Friday, jump to Monday
        currentDate = setHours(setMinutes(setSeconds(setMilliseconds(currentDate, 0), 0), 0), 9);
      }
    } else {
      // If it's a weekend, move to the next Monday morning
      currentDate = addDays(currentDate, 8 - getDay(currentDate));
      currentDate = setHours(setMinutes(setSeconds(setMilliseconds(currentDate, 0), 0), 0), 9);
    }
  }

  return currentDate;
};