import type { InsuredParty } from "@prisma/client";

/**
 * Standardized interface for transformed insured party data
 * Used consistently across all policy-related components
 */
export interface PolicyInsuredPartyData {
  id: string;
  fullName: string;
  firstName?: string;
  lastName?: string;
  displayName?: string;
  identification: string;
  roles: string[];
  gender?: string;
  birthDate?: string;
  driverLicenseNumber?: string;
  driverLicenseIssuedAt?: string;
}

/**
 * Transforms raw InsuredParty data from Prisma into standardized PolicyInsuredPartyData
 * This eliminates duplicate transformation logic across services and actions
 *
 * @param party - Raw InsuredParty data from database
 * @returns Standardized PolicyInsuredPartyData object
 */
export function transformInsuredPartyData(party: InsuredParty): PolicyInsuredPartyData {
  return {
    id: party.id,
    fullName: `${party.firstName || ''} ${party.lastName || ''}`.trim() || party.displayName || 'Sin nombre',
    firstName: party.firstName || undefined,
    lastName: party.lastName || undefined,
    displayName: party.displayName || undefined,
    identification: party.identification,
    roles: party.roles.map(role => role.toString()),
    gender: party.gender?.toString() || undefined,
    birthDate: party.birthDate?.toISOString() || undefined,
    driverLicenseNumber: party.driverLicenseNumber || undefined,
    driverLicenseIssuedAt: party.driverLicenseIssuedAt?.toISOString() || undefined,
  };
}

/**
 * Transforms an array of InsuredParty data
 * Convenience function for bulk transformations
 *
 * @param parties - Array of raw InsuredParty data from database
 * @returns Array of standardized PolicyInsuredPartyData objects
 */
export function transformInsuredPartiesData(parties: InsuredParty[]): PolicyInsuredPartyData[] {
  return parties.map(transformInsuredPartyData);
}

/**
 * Transforms InsuredParty data from PolicyInsuredParty join table relationship
 * Handles the nested structure from Prisma includes
 *
 * @param policyInsuredParties - Array of PolicyInsuredParty with included InsuredParty data
 * @returns Array of standardized PolicyInsuredPartyData objects
 */
export function transformPolicyInsuredPartiesData(
  policyInsuredParties: Array<{ insuredParty: InsuredParty }>
): PolicyInsuredPartyData[] {
  return policyInsuredParties.map(pip => transformInsuredPartyData(pip.insuredParty));
}