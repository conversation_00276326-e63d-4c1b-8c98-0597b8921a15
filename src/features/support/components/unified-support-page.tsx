"use client";

import { SupportCard } from "./support-card";
import { supportConfig } from "../config/support.config";
import { UserRole } from "../types/support.types";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Separator } from "@/components/ui/separator";
import { useEffect, useState } from "react";

interface UnifiedSupportPageProps {
  userRole: UserRole;
  title?: string;
  description?: string;
}

// Default titles and descriptions based on role
const getRoleDefaults = (userRole: UserRole) => {
  switch (userRole) {
    case "ACCOUNT_HOLDER":
      return {
        title: "Soporte",
        description: "¿Necesitas ayuda? Estamos aquí para asistirte."
      };
    case "BROKER":
      return {
        title: "Soporte",
        description: "¿Necesitas ayuda con subastas, comisiones o gestión de clientes? Te ayudamos."
      };
    case "ADMIN":
      return {
        title: "Soporte Técnico - Administración",
        description: "Recursos técnicos y soporte especializado para administradores del sistema."
      };
    default:
      return {
        title: "Soporte",
        description: "¿Necesitas ayuda? Estamos aquí para asistirte."
      };
  }
};

export function UnifiedSupportPage({ userRole, title, description }: UnifiedSupportPageProps) {
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 0);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Get default title and description if not provided
  const defaults = getRoleDefaults(userRole);
  const finalTitle = title || defaults.title;
  const finalDescription = description || defaults.description;
  // Filter contacts and resources based on user role
  const contacts = supportConfig.contacts.filter(contact =>
    contact.roles.includes(userRole)
  );

  const resources = supportConfig.resources.filter(resource =>
    resource.roles.includes(userRole)
  );

  return (
    <div className="flex flex-1 flex-col">
      {/* Sticky Header */}
      <div className={`sticky top-0 z-10 bg-white py-4 ${isScrolled ? 'shadow-md' : ''} transition-shadow duration-200`}>
        <div className="px-4">
          {/* Title with Sidebar Trigger */}
          <div className="flex items-center gap-4 mb-4">
            <SidebarTrigger className="-ml-1 hover:bg-gray-100" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{finalTitle}</h1>
              <p className="text-gray-600">{finalDescription}</p>
            </div>
          </div>
          <Separator className="mb-4" />
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 px-4 pb-4">
        <div className="space-y-6">
          {/* Contact Cards */}
          {contacts.length > 0 && (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {contacts.map((contact) => (
                <SupportCard
                  key={contact.id}
                  item={contact}
                />
              ))}
            </div>
          )}

          {/* Resource Cards */}
          {resources.length > 0 && (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {resources.map((resource) => (
                <SupportCard
                  key={resource.id}
                  item={resource}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}