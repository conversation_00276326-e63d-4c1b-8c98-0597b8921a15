import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

/**
 * Creates a server-side Supabase client with service role key for administrative operations.
 * This client bypasses RLS policies and should only be used in server-side contexts
 * where full database access is required (e.g., middleware, server actions).
 * 
 * SECURITY NOTE: This client uses the service role key and should NEVER be exposed
 * to the client side. Only use in server-side code.
 */
export async function createServerAdminClient() {
  try {
    const cookieStore = await cookies()

    // Ensure environment variables are defined
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY

    if (!supabaseUrl || !supabaseServiceRoleKey) {
      throw new Error('Missing required Supabase environment variables for admin client')
    }

    return createServerClient(
      supabaseUrl,
      supabaseServiceRoleKey, // Use service role key for admin operations
      {
        cookies: {
          get(name) {
            return cookieStore.get(name)?.value
          },
          set(name, value, options) {
            cookieStore.set(name, value, options)
          },
          remove(name, options) {
            cookieStore.set(name, '', { ...options, maxAge: 0 })
          },
        },
      }
    )
  } catch (error) {
    console.error('Error creating Supabase admin client:', error)
    throw error
  }
}

/**
 * Creates a server-side Supabase client with anon key for user-facing operations.
 * This client respects RLS policies and should be used for operations that need
 * to respect user permissions.
 */
export async function createServerUserClient() {
  try {
    const cookieStore = await cookies()

    // Ensure environment variables are defined
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

    if (!supabaseUrl || !supabaseAnonKey) {
      throw new Error('Missing required Supabase environment variables for user client')
    }

    return createServerClient(
      supabaseUrl,
      supabaseAnonKey, // Use anon key for user operations (respects RLS)
      {
        cookies: {
          get(name) {
            return cookieStore.get(name)?.value
          },
          set(name, value, options) {
            cookieStore.set(name, value, options)
          },
          remove(name, options) {
            cookieStore.set(name, '', { ...options, maxAge: 0 })
          },
        },
      }
    )
  } catch (error) {
    console.error('Error creating Supabase user client:', error)
    throw error
  }
}