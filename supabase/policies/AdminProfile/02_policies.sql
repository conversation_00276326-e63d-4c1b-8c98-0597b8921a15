-- AdminProfile policies: self and ADMIN
DROP POLICY IF EXISTS "admin_profile_select_own" ON public.admin_profile;
CREATE POLICY "admin_profile_select_own"
ON public.admin_profile
FOR SELECT
USING (
  auth.uid() = user_id OR public.get_user_role() = 'ADMIN'
);

DROP POLICY IF EXISTS "admin_profile_insert_self" ON public.admin_profile;
CREATE POLICY "admin_profile_insert_self"
ON public.admin_profile
FOR INSERT
WITH CHECK (
  auth.uid() = user_id OR public.get_user_role() = 'ADMIN'
);

DROP POLICY IF EXISTS "admin_profile_update_own" ON public.admin_profile;
CREATE POLICY "admin_profile_update_own"
ON public.admin_profile
FOR UPDATE
USING (
  auth.uid() = user_id OR public.get_user_role() = 'ADMIN'
)
WITH CHECK (
  auth.uid() = user_id OR public.get_user_role() = 'ADMIN'
);