import { db } from "@/lib/db";
import type { AccountHolderProfile, Prisma } from "@prisma/client";

export const AccountHolderProfileService = {
  // Create a new account holder profile
  async create(data: Prisma.AccountHolderProfileCreateInput): Promise<AccountHolderProfile> {
    return db.accountHolderProfile.create({
      data,
      include: {
        user: true,
        policies: true
      }
    });
  },

  // Get a account holder profile by ID
  async getById(id: string): Promise<AccountHolderProfile | null> {
    return db.accountHolderProfile.findUnique({
      where: { id },
      include: {
        user: true,
        policies: {
          include: {
            asset: true,
            broker: true,
            coverages: true
          }
        }
      }
    });
  },

  // Get a account holder profile by user ID
  async getByUserId(userId: string): Promise<AccountHolderProfile | null> {
    return db.accountHolderProfile.findUnique({
      where: { userId },
      include: {
        user: true,
        policies: {
          include: {
            asset: true,
            broker: true,
            coverages: true
          }
        }
      }
    });
  },

  // Update a account holder profile
  async update(id: string, data: Prisma.AccountHolderProfileUpdateInput): Promise<AccountHolderProfile> {
    return db.accountHolderProfile.update({
      where: { id },
      data,
      include: {
        user: true,
        policies: true
      }
    });
  },

  // Delete a account holder profile
  async delete(id: string): Promise<AccountHolderProfile> {
    return db.accountHolderProfile.delete({
      where: { id }
    });
  },

  // List account holder profiles with pagination and optional filters
  async list(params: {
    skip?: number;
    take?: number;
    where?: Prisma.AccountHolderProfileWhereInput;
    orderBy?: Prisma.AccountHolderProfileOrderByWithRelationInput;
  }): Promise<{ accountHolders: AccountHolderProfile[]; total: number }> {
    const { skip = 0, take = 10, where, orderBy } = params;
    
    const [accountHolders, total] = await Promise.all([
      db.accountHolderProfile.findMany({
        skip,
        take,
        where,
        orderBy,
        include: {
          user: true,
          policies: {
            include: {
              asset: true,
              broker: true,
              coverages: true
            }
          }
        }
      }),
      db.accountHolderProfile.count({ where })
    ]);

    return { accountHolders, total };
  }
};