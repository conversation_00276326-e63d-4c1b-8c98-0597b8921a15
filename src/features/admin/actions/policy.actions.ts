'use server';

import { revalidatePath } from 'next/cache';
import { PolicyStatus } from '@prisma/client';
import { AdminPolicyService } from '../services/policy.service';
import { AuctionService } from '@/features/auctions/services/auction.service';

/**
 * Updates the status of a policy. This is an admin-only action.
 * If the new status is 'RENEW_SOON', it triggers the creation of a new auction.
 *
 * @param policyId The ID of the policy to update.
 * @param newStatus The new status for the policy.
 */
export const updatePolicyStatus = async (
  policyId: string,
  newStatus: PolicyStatus
) => {
  try {
    // In a real app, you'd get the user's role from their session
    // For now, we assume this action is protected by middleware or route guards.
    const userRole = 'ADMIN'; 

    if (userRole !== 'ADMIN') {
      throw new Error('You are not authorized to perform this action.');
    }

    const updatedPolicy = await AdminPolicyService.updateStatus(policyId, newStatus);

    console.log(`Policy ${policyId} status updated to ${newStatus}`);

    // If the policy is now ready for renewal, create an auction for it.
    if (newStatus === PolicyStatus.RENEW_SOON) {
      console.log(`Policy ${policyId} is ready for renewal. Creating auction...`);
      await AuctionService.createAuctionFromPolicy(policyId);
      console.log(`Auction created for policy ${policyId}`);
    }

    // Revalidate the policies page to show the updated status
    revalidatePath('/admin/policies');
    revalidatePath(`/admin/policies/${policyId}`);

    return {
      success: true,
      policy: updatedPolicy,
    };
  } catch (error) {
    console.error('Error updating policy status:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return {
      success: false,
      error: errorMessage,
    };
  }
};