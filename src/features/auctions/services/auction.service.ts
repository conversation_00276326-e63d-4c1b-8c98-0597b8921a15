import { db } from "@/lib/db";
import { AuctionState, type Auction, type Prisma } from "@prisma/client";
import { calculateWorkingHoursClosedAt, DEFAULT_AUCTION_DURATION_HOURS } from "@/lib/auction/working-hours";

export const AuctionService = {
  // Create a new auction
  async create(data: Prisma.AuctionCreateInput): Promise<Auction> {
    return db.auction.create({
      data,
      include: {
        accountHolder: true,
        policy: true,
        bids: {
          include: {
            broker: true
          }
        }
      }
    });
  },

  // Get an auction by ID
  async getById(id: string): Promise<Auction | null> {
    return db.auction.findUnique({
      where: { id },
      include: {
        accountHolder: true,
        policy: true,
        bids: {
          include: {
            broker: true
          }
        }
      }
    });
  },

  // Get auctions by account holder ID
  async getByAccountHolderId(accountHolderId: string): Promise<Auction[]> {
    return db.auction.findMany({
      where: { accountHolderId },
      include: {
        policy: true,
        bids: {
          include: {
            broker: true
          }
        }
      }
    });
  },

  // Get active auctions
  async getActive(): Promise<Auction[]> {
    const now = new Date();
    return db.auction.findMany({
      where: {
        status: 'OPEN',
        endDate: {
          gt: now
        }
      },
      include: {
        accountHolder: true,
        policy: true,
        bids: {
          include: {
            broker: true
          }
        }
      }
    });
  },

  // Update an auction
  async update(id: string, data: Prisma.AuctionUpdateInput): Promise<Auction> {
    return db.auction.update({
      where: { id },
      data,
      include: {
        accountHolder: true,
        policy: true,
        bids: {
          include: {
            broker: true
          }
        }
      }
    });
  },

  // Close an auction
  async close(id: string): Promise<Auction> {
    return db.auction.update({
      where: { id },
      data: {
        status: 'CLOSED'
      },
      include: {
        bids: {
          include: {
            broker: true
          }
        }
      }
    });
  },

  // Delete an auction
  async delete(id: string): Promise<Auction> {
    return db.auction.delete({
      where: { id }
    });
  },

  // List auctions with pagination and optional filters
  async list(params: {
    skip?: number;
    take?: number;
    where?: Prisma.AuctionWhereInput;
    orderBy?: Prisma.AuctionOrderByWithRelationInput;
  }): Promise<{ auctions: Auction[]; total: number }> {
    const { skip = 0, take = 10, where, orderBy } = params;

    const [auctions, total] = await Promise.all([
      db.auction.findMany({
        skip,
        take,
        where,
        orderBy,
        include: {
          accountHolder: true,
          policy: true,
          bids: {
            include: {
              broker: true
            }
          }
        }
      }),
      db.auction.count({ where })
    ]);

    return { auctions, total };
  },

  // Create an auction from a policy
  async createAuctionFromPolicy(policyId: string): Promise<Auction> {
    const policy = await db.policy.findUnique({
      where: { id: policyId },
      include: {
        accountHolder: true
      }
    });

    if (!policy || !policy.accountHolder) {
      throw new Error("Policy or account holder not found for the given policy ID.");
    }

    const startDate = new Date();
    // Calculate end date using working hours business logic (48 working hours, Mon-Fri 06:00-23:59 Madrid time)
    const endDate = calculateWorkingHoursClosedAt(startDate, DEFAULT_AUCTION_DURATION_HOURS);

    const auctionData: Prisma.AuctionCreateInput = {
      accountHolder: { connect: { id: policy.accountHolderId! } },
      policy: { connect: { id: policyId } },
      startDate,
      endDate,
      status: AuctionState.OPEN,
    };

    return db.auction.create({ data: auctionData });
  }
};

// Note: Auction expiration is now handled by Supabase cron jobs
// The closeExpiredAuctions function has been removed as it's replaced by:
// - Database cron job: 'close-expired-auctions' (runs every 5 minutes)
// - Notification cron job: 'auction-notifications' (runs every 5 minutes)
// See supabase/migrations/ for the implementation