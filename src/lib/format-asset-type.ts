import { AssetType } from '@prisma/client';

/**
 * Formats AssetType enum values into proper Spanish display names
 * with correct capitalization and accents.
 * 
 * @param assetType - The AssetType enum value
 * @returns Formatted Spanish display name
 */
export function formatAssetType(assetType: AssetType | string): string {
  const formatMap: Record<AssetType, string> = {
    CAR: 'Coche',
    MOTORCYCLE: 'Motocicle<PERSON>',
    PROPERTY: 'Propiedad',
    PERSON: 'Persona',
    OTHER: 'Otro'
  };

  return formatMap[assetType as AssetType] || assetType;
}

/**
 * Gets all available asset types as formatted display options
 * for use in select components or dropdowns.
 * 
 * @returns Array of objects with value (enum) and label (formatted Spanish name)
 */
export function getAssetTypeOptions() {
  return Object.values(AssetType).map(value => ({
    value,
    label: formatAssetType(value)
  }));
}