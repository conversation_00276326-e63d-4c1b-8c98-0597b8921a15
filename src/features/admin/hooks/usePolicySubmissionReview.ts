import { useState, useCallback } from "react";
import { useToast } from "@/components/ui/use-toast";
import { createClient } from "@/lib/supabase/client";
import { useUserAuthentication } from "@/features/auth/hooks/useUserAuthentication";
import {
  PolicyType,
  PaymentPeriod,
  AssetsType,
  FuelType,
  UsageType,
  KmRange,
  GarageType,
  PartyRole,
  GuaranteeType,
} from "@prisma/client";

export { GuaranteeType };

export type Coverage = {
  id?: string;
  type: GuaranteeType;
  customName?: string;
  limit?: number;
  deductible?: number;
  description?: string;
};

export type PolicyData = {
  // Policy details
  policyNumber: string;
  insurerName: string;
  policyType: PolicyType | null;
  productName: string | null;
  startDate: Date | null;
  endDate: Date | null;
  paymentPeriod: PaymentPeriod | null;
  premium: number | null;

  // Insured details
  insuredParties: {
    personId?: string;
    fullName: string;
    dni: string;
    roles: PartyRole[];
  }[];

  // Asset details
  brand: string;
  model: string;
  year: number | null;
  version: string | null;
  chassisNumber: string;
  licensePlate: string;
  firstRegistrationDate: Date | null;
  type: AssetsType | null;
  fuelType: FuelType | null;
  usageType: UsageType | null;
  kmPerYear: KmRange | null;
  garageType: GarageType | null;
  seats: number | null;
  powerCv: number | null;
  isLeased: boolean;

  // Coverage details
  coverages: Coverage[];
};

// This type now reflects the lenient schema from the API, allowing for nulls
export type ExtractedData = {
  policyNumber: string | null;
  productName: string | null;
  startDate: string | null;
  endDate: string | null;
  paymentPeriod: PaymentPeriod | null;
  policyType: PolicyType | null;
  premium: number | null;
  insurerName: string | null;
  asset: {
    licensePlate: string | null;
    brand: string | null;
    model: string | null;
    year: number | null;
    chassisNumber: string | null;
    firstRegistrationDate: string | null;
    type: AssetsType | null;
    fuelType: FuelType | null;
    powerCv: number | null;
    seats: number | null;
    usageType: UsageType | null;
    garageType: GarageType | null;
    kmPerYear: KmRange | null;
    isLeased?: boolean;
    version?: string | null;
  };
  insuredParties: {
    fullName: string;
    dni: string;
    role: PartyRole;
  }[];
  coverages: {
    type: GuaranteeType;
    customName?: string;
    limit?: number | null;
    deductible?: number | null;
    description?: string | null;
  }[];
};


const initializeNewPolicy = (): PolicyData => {
  const startDate = new Date();
  const endDate = new Date();
  endDate.setFullYear(startDate.getFullYear() + 1);

  return {
    policyNumber: "",
    insurerName: "",
    policyType: null,
    productName: "",
    startDate: null,
    endDate: null,
    paymentPeriod: null,
    premium: null,
    insuredParties: [],
    brand: "",
    model: "",
    year: null,
    version: "",
    chassisNumber: "",
    licensePlate: "",
    firstRegistrationDate: null,
    type: null,
    fuelType: null,
    usageType: null,
    kmPerYear: null,
    garageType: null,
    seats: null,
    powerCv: null,
    isLeased: false,
    coverages: [],
  };
};

export const usePolicySubmissionReview = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [policyData, setPolicyData] = useState<PolicyData>(initializeNewPolicy());
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [auctionDuration, setAuctionDuration] = useState<24 | 48 | 72 | null>(null);
  const { toast } = useToast();
  const supabase = createClient();
  const { user } = useUserAuthentication();

  const handleFileUploadComplete = useCallback((file: File, extractedData: ExtractedData) => {
    setUploadedFile(file);
    
    const transformedParties = (extractedData.insuredParties || []).reduce((acc: any[], party: any) => {
      const existingParty = acc.find(p => p.dni === party.dni && p.fullName === party.fullName);
      if (existingParty) {
        if (party.role && !existingParty.roles.includes(party.role)) {
          existingParty.roles.push(party.role);
        }
      } else {
        acc.push({ ...party, roles: party.role ? [party.role] : [] });
      }
      return acc;
    }, []);

    // Safely transform the extracted data, providing defaults for null values.
    const transformedData: PolicyData = {
      policyNumber: extractedData.policyNumber ?? "",
      insurerName: extractedData.insurerName ?? "",
      policyType: extractedData.policyType ?? null,
      productName: extractedData.productName,
      startDate: extractedData.startDate ? new Date(extractedData.startDate) : null,
      endDate: extractedData.endDate ? new Date(extractedData.endDate) : null,
      paymentPeriod: extractedData.paymentPeriod ?? null,
      premium: extractedData.premium,
      
      brand: extractedData.asset.brand ?? "",
      model: extractedData.asset.model ?? "",
      year: extractedData.asset.year,
      version: extractedData.asset.version ?? null,
      chassisNumber: extractedData.asset.chassisNumber ?? "",
      licensePlate: extractedData.asset.licensePlate ?? "",
      firstRegistrationDate: extractedData.asset.firstRegistrationDate ? new Date(extractedData.asset.firstRegistrationDate) : null,
      type: extractedData.asset.type ?? null,
      fuelType: extractedData.asset.fuelType,
      usageType: extractedData.asset.usageType,
      kmPerYear: extractedData.asset.kmPerYear,
      garageType: extractedData.asset.garageType,
      seats: extractedData.asset.seats,
      powerCv: extractedData.asset.powerCv,
      isLeased: extractedData.asset.isLeased ?? false,

      insuredParties: transformedParties,
      coverages: (extractedData.coverages || [])
        .filter((c) => c.type !== null)
        .map((c) => ({
          ...c,
          limit: c.limit ?? undefined,
          deductible: c.deductible ?? undefined,
          description: c.description ?? undefined,
        })),
    };
    
    setPolicyData(transformedData);
  }, []);

  const handleDataUpdate = useCallback((updatedData: PolicyData) => {
    setPolicyData(updatedData);
  }, []);

  const handleAuctionDurationSelect = (duration: 24 | 48 | 72) => {
    setAuctionDuration(duration);
  };

  const handleContinue = () => {
    setCurrentStep((prev) => prev + 1);
  };

  const handleBack = () => {
    setCurrentStep((prev) => Math.max(0, prev - 1));
  };

  const handleSubmit = async () => {
    if (!policyData || !auctionDuration) {
      toast({
        variant: "destructive",
        title: "Falta información",
        description: "Por favor completa todos los campos requeridos.",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      if (!user) {
        toast({
          variant: "destructive",
          title: "Sesión no iniciada",
          description: "Debes iniciar sesión para guardar la póliza.",
        });
        return;
      }

      if (uploadedFile) {
        const userId = user.id;
        const sanitizeFilename = (filename: string) => {
          const lastDotIndex = filename.lastIndexOf('.');
          const name = lastDotIndex > 0 ? filename.substring(0, lastDotIndex) : filename;
          const extension = lastDotIndex > 0 ? filename.substring(lastDotIndex) : '';
          const sanitizedName = name
            .replace(/[^a-zA-Z0-9\-_]/g, '_')
            .replace(/_{2,}/g, '_')
            .replace(/^_+|_+$/g, '');
          return sanitizedName + extension;
        };
        const sanitizedFilename = sanitizeFilename(uploadedFile.name);
        const filePath = `${userId}/${Date.now()}_${sanitizedFilename}`;
        const { error: uploadError } = await supabase.storage
          .from('policy_documents')
          .upload(filePath, uploadedFile);

        if (uploadError) {
          throw new Error(`Error subiendo documento: ${uploadError.message}`);
        }
        console.log("Document uploaded to:", filePath);
      }

      await new Promise(resolve => setTimeout(resolve, 1000));

      toast({
        title: "Póliza registrada con éxito",
        description: "Tu póliza ha sido guardada y la subasta ha sido creada.",
      });

      setCurrentStep(4);
    } catch (error: any) {
      console.error("Error en registro de póliza:", error);
      toast({
        variant: "destructive",
        title: "Error al guardar póliza",
        description: error.message || "Hubo un error al guardar tu póliza. Por favor intenta de nuevo.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    currentStep,
    policyData,
    isSubmitting,
    auctionDuration,
    handleFileUploadComplete,
    handleDataUpdate,
    handleAuctionDurationSelect,
    handleContinue,
    handleBack,
    handleSubmit,
  };
};
