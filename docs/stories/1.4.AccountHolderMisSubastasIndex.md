---
story_id: "1.4"
epic_id: "1"
title: "Account Holder: Mis Subastas Index View"
status: "In Progress"
created_by: "<PERSON> the Scrum Master"
created_at: "2024-07-30"
---

# Story 1.4: Account Holder: Mis Subastas Index View

## Status
In Progress

# User Story

As an **Account Holder**, I want to **view a list of my active and past insurance auctions** so that I can **track their status and see the offers I have received**.

# Acceptance Criteria

1.  **Empty State:** When I have no auctions, the page at `/account-holder/auctions` must display the title "Mis Subastas," the subtitle "Gestiona tus subastas de seguros," and a message "No hay subastas disponibles."
2.  **List State:** When I have one or more auctions, the page must display a responsive grid of cards, with each card showing:
    *   Auction Identifier (e.g., `ZEE-AU-000123`)
    *   Time Remaining (e.g., `Xd Yh`, `Xh Ym`, or `Xm`). If the auction is finished, it must show "Finalizada".
    *   Annual Premium (formatted as EUR currency).
    *   Current Insurer.
    *   Asset (e.g., "Seat León (2022)").
    *   Number of quotes received.
3.  **Navigation:** Clicking on an auction card must navigate the user to `/account-holder/auctions/[id]`.
4.  **Security:** The API endpoint must return a `401 Unauthorized` error if the user is not authenticated. The client-side code must not contain any database access keys or direct calls to the Supabase database.
5.  **Error Handling:** If the API returns an error, the UI must display a generic error message: "No se pudo cargar la información. Inténtalo de nuevo."
6.  **Accessibility:** All interactive elements must be keyboard-accessible and have descriptive `aria-label` attributes.

# Data Contract

The UI will expect an array of `AuctionSummary` objects from the API:

```ts
export type AuctionSummary = {
  id: string;
  identifier: string;
  endsAt: string; // ISO date string
  annualPremium: number;
  currency?: string; // Defaults to 'EUR'
  currentInsurer?: string | null;
  assetDisplayName: string;
  quotesReceived: number;
};
```

# Dev Notes

*   **API Endpoint:** `GET /api/auctions?scope=account-holder`
*   **Authentication:** The endpoint must validate the user's session and role (`ACCOUNT_HOLDER`) on the server-side.
*   **Validation:** Use Zod for server-side validation of any query parameters.
*   **Database:** Use the Prisma client singleton from `src/lib/db.ts` for all database operations.
*   **Performance:** Use `cache: "no-store"` for the API route to ensure the time remaining is always accurate.
*   **UI:** Reuse existing `shadcn/ui` components (`Card`, `Badge`, `Separator`) and `lucide-react` icons. The UI must be in Spanish.

# Tasks / Subtasks

*   **Backend:**
    *   [ ] Create the API route at `src/app/api/auctions/route.ts`.
    *   [ ] Implement the `GET` handler to fetch auction data for the authenticated user.
    *   [ ] Add server-side validation for the user's session and role.
    *   [ ] Write the Prisma query to fetch the required data for the `AuctionSummary` type.
*   **Frontend:**
    *   [ ] Create the page component at `src/app/account-holder/auctions/page.tsx`.
    *   [ ] Implement the data fetching logic to call the `/api/auctions` endpoint.
    *   [ ] Create the UI to display the list of auctions, including the empty state and loading/error states.
    *   [ ] Create the `AuctionCard` component to display the details of a single auction.
    *   [ ] Ensure the UI is responsive and accessible.

# Dev Agent Record

## Agent Model Used
Claude Sonnet 4

## Debug Log References
- Initial story analysis and setup

## Completion Notes
- Story status updated from Draft to In Progress
- Beginning implementation following develop-story workflow

## File List
*Files will be listed as they are created/modified*

## Change Log
- Updated story status to "In Progress"
- Added Dev Agent Record section with checkboxes for task tracking