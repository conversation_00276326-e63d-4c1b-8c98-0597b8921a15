import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";

// Zod schema for request validation
const sendOfferSchema = z.object({
  policyId: z.string().uuid("Invalid policy ID format"),
  annualPremium: z.number().positive("Annual premium must be positive"),
  fileUrl: z.string().url("Invalid file URL").optional(),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate request body
    const validatedData = sendOfferSchema.parse(body);
    const { policyId, annualPremium, fileUrl } = validatedData;

    // TODO: Get actual broker ID from session/auth
    // For now, using a mock broker ID
    const brokerId = "mock-broker-id";

    // Find the auction for this policy
    const auction = await db.policyAuction.findFirst({
      where: {
        policyId: policyId,
        status: "OPEN", // Only allow bids on open auctions
      },
    });

    if (!auction) {
      return NextResponse.json(
        { error: "Auction not found or not active" },
        { status: 404 }
      );
    }

    // Check if broker already has a bid for this auction
    const existingBid = await db.bid.findFirst({
      where: {
        auctionId: auction.id,
        brokerId: brokerId,
      },
    });

    if (existingBid) {
      // Update existing bid
      await db.bid.update({
        where: { id: existingBid.id },
        data: {
          amount: annualPremium,
          // TODO: Add fileUrl once Prisma client is regenerated
        },
      });
    } else {
      // Create new bid
      await db.bid.create({
        data: {
          auctionId: auction.id,
          brokerId: brokerId,
          amount: annualPremium,
          // TODO: Add fileUrl once Prisma client is regenerated
        },
      });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error sending offer:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}