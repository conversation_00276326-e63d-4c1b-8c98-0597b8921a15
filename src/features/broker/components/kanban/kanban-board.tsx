import type { ComponentProps } from 'react';
import { cn } from '@/lib/utils';

/*
Board Components
*/

export function KanbanBoard({
  className,
  ref,
  ...props
}: ComponentProps<'div'>) {
  return (
    <div
      className={cn(
        'flex h-full flex-grow items-start gap-x-4 overflow-hidden',
        className,
      )}
      ref={ref}
      {...props}
    />
  );
}

/**
 * Add some extra margin to the right of the container to allow for scrolling
 * when adding a new column.
 */
export function KanbanBoardExtraMargin({
  className,
  ref,
  ...props
}: ComponentProps<'div'>) {
  return (
    <div
      className={cn('h-1 w-2 flex-shrink-0', className)}
      ref={ref}
      {...props}
    />
  );
}