# 4. Data Models and Schema

## 4.1 Core Entity Relationships

For the complete Entity-Relationship Diagram (ERD) showing all core entities and their relationships, please refer to the [main README.md](../README.md#-entity-relationship-diagram-erd).

## 4.2 Key Data Models

**User Management:**
- `User`: Authentication and role information (`ACCOUNT_HOLDER`, `BROKER`, `ADMIN`)
- `AccountHolderProfile` / `BrokerProfile` / `AdminProfile`: Role-specific profile data
- Role-based access patterns with Supabase RLS

**Policy Domain:**
- `Policy`: Complete insurance policy details with status tracking
- `Asset`: Insured asset information (vehicles, properties, etc.)
- `Vehicle`: Vehicle-specific details linked to assets
- `InsuredParty`: Individuals covered by policies with roles and personal information
- `Coverage`: Specific guarantees and financial details per policy
- `PolicyInsuredParty`: Many-to-many relationship between policies and insured parties

**Auction Domain:**
- `Auction`: Insurance auction management with working hours and winner limits
- `Bid`: Broker bids on auctions with document attachments
- `AuctionWinner`: Selected winning brokers (up to 3 per auction)
- `AuctionCommission`: Lead payment management (10% of policy premium)
- Business hours validation and auction lifecycle management

**Supporting Models:**
- `Documentation`: Unified document storage for all entities
- `Address`: Location information for brokers and insured parties
- `Subscription`: Stripe-based broker subscription management

## 4.3 Schema Enhancement Guidelines

**For New Models:**
- All models must include descriptive comments
- Follow established naming conventions (English, camelCase/PascalCase)
- Implement appropriate relationships and constraints
- Consider RLS policies for data security
- Validate against existing domain boundaries

---
