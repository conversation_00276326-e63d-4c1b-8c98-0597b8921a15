import { <PERSON>bar<PERSON>rov<PERSON>, <PERSON>barTrigger, SidebarInset } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/shared/app-sidebar";
import { createClient } from "@/lib/supabase/server";
import { UserDataProvider } from "@/features/auth/components/user-data-provider";
import { extractUserName } from "@/features/auth/utils/user-metadata";
import React from "react";

export default async function AccountHolderLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser();
  const { fullName } = user ? extractUserName(user) : { fullName: "Usuario" };

  return (
    <SidebarProvider>
      <AppSidebar userRole="ACCOUNT_HOLDER" />
      <SidebarInset>
        <div className="flex flex-1 flex-col">
          <UserDataProvider user={user} userName={fullName}>
            {children}
          </UserDataProvider>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}