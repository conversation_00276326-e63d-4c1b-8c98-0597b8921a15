import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";
import { r2Client } from "@/lib/r2";
import { db } from "@/lib/db";
import { GetObjectCommand } from "@aws-sdk/client-s3";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const key = searchParams.get('key');

    if (!key) {
      return NextResponse.json(
        { error: "Document key is required" },
        { status: 400 }
      );
    }

    // Authenticate the user
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Verify that the user has access to this document
    const document = await db.documentation.findFirst({
      where: {
        url: key,
        OR: [
          {
            accountHolder: {
              userId: user.id
            }
          },
          {
            broker: {
              userId: user.id
            }
          }
        ]
      },
      include: {
        accountHolder: true,
        broker: true
      }
    });

    if (!document) {
      return NextResponse.json(
        { error: "Document not found or access denied" },
        { status: 404 }
      );
    }

    // Fetch the file directly from R2 using the AWS SDK
    const bucketName = process.env.R2_BUCKET_NAME!;
    const getObjectCommand = new GetObjectCommand({
      Bucket: bucketName,
      Key: key,
    });

    const r2Response = await r2Client.send(getObjectCommand);

    if (!r2Response.Body) {
      return NextResponse.json(
        { error: "Document not available" },
        { status: 404 }
      );
    }

    // Convert the stream to buffer
    const fileBuffer = await r2Response.Body.transformToByteArray();

    // Return the file with appropriate headers
    return new NextResponse(Buffer.from(fileBuffer), {
      status: 200,
      headers: {
        'Content-Type': document.mimeType || 'application/octet-stream',
        'Content-Disposition': `attachment; filename="${document.fileName || 'document'}"`,
        'Content-Length': fileBuffer.byteLength.toString(),
      },
    });

  } catch (error) {
    console.error("Error downloading document:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
