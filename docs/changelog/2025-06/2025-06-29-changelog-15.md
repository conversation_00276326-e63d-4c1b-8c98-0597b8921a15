---
title: "Refactoring of the Policy Form and Vehicle Data Model"
date: 2025-06-29
---

## Summary

Today, a significant refactoring of the policy creation form and the underlying data model for assets was carried out. The main objective has been to improve the information structure, data consistency, and user experience.

## Key Changes

### 1. Asset Data Model (`prisma/schema.prisma`)

- **Added `version` field**: An optional `version` field of type `String` has been added to the `Asset` model to capture the specific version of the asset (e.g., "1.5 HYBRID ACTIVE").
- **Removed `usageDescription` field**: The `usageDescription` field has been removed to simplify the model and eliminate redundancy, as the `usageType` field already covers this information in a standardized way.

### 2. Frontend Data Types and Logic (`useNewPolicy.ts`)

- **Updated `PolicyData` type**: The `PolicyData` type has been modified to reflect the changes in the `Asset` model, adding the `version` field and removing `usageDescription`.
- **Updated mock data**: The `getMockPolicyData` function has been updated to include the new `version` field and remove `usageDescription`.

### 3. User Interface (`PolicyDataForm.tsx`)

- **Reorganized asset fields**: The fields in the asset data form have been reorganized to group the main asset information at the top, improving usability and logical flow. The order is now:
    1.  Brand
    2.  Model
    3.  Version
    4.  Year
    5.  Number of seats
    6.  Power (CV)
    7.  Chassis / VIN
    8.  License Plate
    9.  First registration date
    10. Asset type
    11. Fuel type
    12. Usage type
    13. Km/year
    14. Garage type
    15. Is it a leased asset?
- **Added `version` field**: A new input field has been added to the form to capture the asset version.
- **Removed `usageDescription` field**: The usage description field has been removed from the form.

### 4. Documentation and Examples (`examples/data-modeling-assets.json`)

- **Updated data modeling example**: The `data-modeling-assets.json` file has been updated to reflect the new data structure, including the `version` field, removing `usageDescription`, and reordering the fields to match the new form structure.

## Impact

These changes improve the consistency and usability of the application. The data model is now cleaner, and the user interface is more intuitive. The documentation and data examples have been updated to accurately reflect the current state of the system.