import { Separator } from "@/components/ui/separator";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { ProfileSettingsForm } from "@/features/settings/components/profile-settings-form";
import { PasswordSettingsForm } from "@/features/settings/components/password-settings-form";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { User, Shield, Bell, Settings, Database, Users, BarChart3, Globe } from "lucide-react";

export default function AdminSettingsPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-semibold tracking-tight">Configurac<PERSON> del Sistema</h1>
        <p className="text-muted-foreground">
          Panel de administración para configurar la plataforma Zeeguros.
        </p>
      </div>
      <Separator />
      
      <Tabs defaultValue="profile" className="space-y-6">
        <TabsList className="grid w-full grid-cols-8">
          <TabsTrigger value="profile" className="flex items-center gap-2">
            <User className="h-4 w-4" />
            Perfil
          </TabsTrigger>
          <TabsTrigger value="password" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Contraseña
          </TabsTrigger>
          <TabsTrigger value="system" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Sistema
          </TabsTrigger>
          <TabsTrigger value="database" className="flex items-center gap-2">
            <Database className="h-4 w-4" />
            Base de Datos
          </TabsTrigger>
          <TabsTrigger value="users" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Usuarios
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Analíticas
          </TabsTrigger>
          <TabsTrigger value="platform" className="flex items-center gap-2">
            <Globe className="h-4 w-4" />
            Plataforma
          </TabsTrigger>
          <TabsTrigger value="notifications" className="flex items-center gap-2">
            <Bell className="h-4 w-4" />
            Notificaciones
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="profile" className="space-y-6">
          <ProfileSettingsForm 
            title="Perfil de Administrador"
            description="Información del administrador del sistema."
          />
        </TabsContent>
        
        <TabsContent value="password" className="space-y-6">
          <PasswordSettingsForm 
            title="Seguridad de Administrador"
            description="Mantén la seguridad del sistema con una contraseña fuerte."
          />
        </TabsContent>
        
        <TabsContent value="system" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Configuración del Sistema
              </CardTitle>
              <CardDescription>
                Configuración general de la plataforma Zeeguros.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label htmlFor="maintenance-mode">Modo Mantenimiento</Label>
                    <p className="text-sm text-muted-foreground">
                      Activar para realizar mantenimiento del sistema
                    </p>
                  </div>
                  <Switch id="maintenance-mode" />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label htmlFor="new-registrations">Nuevos Registros</Label>
                    <p className="text-sm text-muted-foreground">
                      Permitir el registro de nuevos usuarios
                    </p>
                  </div>
                  <Switch id="new-registrations" defaultChecked />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label htmlFor="auction-creation">Creación de Subastas</Label>
                    <p className="text-sm text-muted-foreground">
                      Permitir la creación de nuevas subastas
                    </p>
                  </div>
                  <Switch id="auction-creation" defaultChecked />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="max-auction-duration">Duración Máxima de Subasta (días)</Label>
                  <Input id="max-auction-duration" type="number" placeholder="30" className="w-32" />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="min-auction-commission">Comisión Mínima (%)</Label>
<Input id="min-auction-commission" type="number" placeholder="5" className="w-32" />
                </div>
                
                <div className="flex justify-end pt-4">
                  <Button>Guardar Configuración</Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="database" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Administración de Base de Datos
              </CardTitle>
              <CardDescription>
                Herramientas para gestionar la base de datos del sistema.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="space-y-1">
                    <p className="font-medium">Backup Automático</p>
                    <p className="text-sm text-muted-foreground">
                      Último backup: Hoy a las 03:00
                    </p>
                  </div>
                  <Badge variant="secondary">Activado</Badge>
                </div>
                
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="space-y-1">
                    <p className="font-medium">Limpieza de Datos</p>
                    <p className="text-sm text-muted-foreground">
                      Eliminar datos obsoletos automáticamente
                    </p>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">Configurar</Button>
                    <Button variant="outline" size="sm">Ejecutar</Button>
                  </div>
                </div>
                
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="space-y-1">
                    <p className="font-medium">Optimización de Consultas</p>
                    <p className="text-sm text-muted-foreground">
                      Optimizar índices y consultas de la base de datos
                    </p>
                  </div>
                  <Button variant="outline" size="sm">Optimizar</Button>
                </div>
                
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="space-y-1">
                    <p className="font-medium">Estadísticas de Uso</p>
                    <p className="text-sm text-muted-foreground">
                      Tamaño: 2.4 GB | Tablas: 15 | Registros: 1,234,567
                    </p>
                  </div>
                  <Button variant="outline" size="sm">Ver Detalles</Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="users" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Gestión de Usuarios
              </CardTitle>
              <CardDescription>
                Configuración de políticas y límites para usuarios.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="max-policies-per-user">Máximo Pólizas por Usuario</Label>
                    <Input id="max-policies-per-user" type="number" placeholder="10" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="max-auctions-per-user">Máximo Subastas por Usuario</Label>
                    <Input id="max-auctions-per-user" type="number" placeholder="5" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="session-timeout">Timeout de Sesión (minutos)</Label>
                    <Input id="session-timeout" type="number" placeholder="30" />
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label htmlFor="email-verification">Verificación de Email Obligatoria</Label>
                    <p className="text-sm text-muted-foreground">
                      Requiere verificación de email para nuevos usuarios
                    </p>
                  </div>
                  <Switch id="email-verification" defaultChecked />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label htmlFor="broker-approval">Aprobación Manual de Brokers</Label>
                    <p className="text-sm text-muted-foreground">
                      Los brokers requieren aprobación manual
                    </p>
                  </div>
                  <Switch id="broker-approval" defaultChecked />
                </div>
                
                <div className="flex justify-end pt-4">
                  <Button>Actualizar Políticas</Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="analytics" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Configuración de Analíticas
              </CardTitle>
              <CardDescription>
                Configurar recolección y análisis de datos de la plataforma.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label htmlFor="google-analytics">Google Analytics</Label>
                    <p className="text-sm text-muted-foreground">
                      Integración con Google Analytics 4
                    </p>
                  </div>
                  <Switch id="google-analytics" defaultChecked />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label htmlFor="user-tracking">Seguimiento de Usuarios</Label>
                    <p className="text-sm text-muted-foreground">
                      Rastrear comportamiento de usuarios en la plataforma
                    </p>
                  </div>
                  <Switch id="user-tracking" defaultChecked />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label htmlFor="performance-monitoring">Monitoreo de Rendimiento</Label>
                    <p className="text-sm text-muted-foreground">
                      Monitorear rendimiento de la aplicación
                    </p>
                  </div>
                  <Switch id="performance-monitoring" defaultChecked />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="retention-period">Período de Retención (días)</Label>
                  <Input id="retention-period" type="number" placeholder="365" className="w-32" />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="platform" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                Configuración de Plataforma
              </CardTitle>
              <CardDescription>
                Configuración general de la plataforma y marca.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="platform-name">Nombre de la Plataforma</Label>
                    <Input id="platform-name" placeholder="Zeeguros" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="support-email">Email de Soporte</Label>
                    <Input id="support-email" type="email" placeholder="<EMAIL>" />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="platform-url">URL de la Plataforma</Label>
                  <Input id="platform-url" placeholder="https://www.zeeguros.com" />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="terms-url">URL de Términos y Condiciones</Label>
                  <Input id="terms-url" placeholder="https://www.zeeguros.com/terminos" />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="privacy-url">URL de Política de Privacidad</Label>
                  <Input id="privacy-url" placeholder="https://www.zeeguros.com/privacidad" />
                </div>
                
                <div className="flex justify-end pt-4">
                  <Button>Guardar Configuración</Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="notifications" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                Notificaciones del Sistema
              </CardTitle>
              <CardDescription>
                Configurar alertas y notificaciones del sistema.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="space-y-1">
                    <p className="font-medium">Alertas de Sistema</p>
                    <p className="text-sm text-muted-foreground">
                      Notificaciones críticas del sistema
                    </p>
                  </div>
                  <Badge variant="secondary">Activado</Badge>
                </div>
                
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="space-y-1">
                    <p className="font-medium">Reportes de Errores</p>
                    <p className="text-sm text-muted-foreground">
                      Notificaciones de errores y excepciones
                    </p>
                  </div>
                  <Badge variant="secondary">Activado</Badge>
                </div>
                
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="space-y-1">
                    <p className="font-medium">Actividad Sospechosa</p>
                    <p className="text-sm text-muted-foreground">
                      Alertas de seguridad y actividad anómala
                    </p>
                  </div>
                  <Badge variant="secondary">Activado</Badge>
                </div>
                
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="space-y-1">
                    <p className="font-medium">Reportes Diarios</p>
                    <p className="text-sm text-muted-foreground">
                      Resumen diario de actividad de la plataforma
                    </p>
                  </div>
                  <Badge variant="outline">Desactivado</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}