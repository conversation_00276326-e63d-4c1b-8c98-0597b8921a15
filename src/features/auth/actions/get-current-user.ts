"use server";

import { AuthService } from "../services/auth.service";

/**
 * A server action to get the current authenticated user's data.
 * This is the primary, secure way to access user data from the client.
 * It ensures that all database access happens on the server via the AuthService.
 *
 * @returns The user object or null if the user is not authenticated.
 */
export async function getCurrentUser() {
  try {
    const {
      data: { user },
    } = await AuthService.getUser();
    return user;
  } catch (error) {
    console.error("Error in getCurrentUser action:", error);
    return null;
  }
}