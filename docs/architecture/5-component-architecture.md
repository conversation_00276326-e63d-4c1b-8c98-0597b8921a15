# 5. Component Architecture

## 5.1 Current Component Organization

**✅ ESTABLISHED STRUCTURE:**

```
src/
├── app/                       # Role-based app router
│   ├── admin/                 # Admin role routes
│   │   ├── dashboard/         # Admin dashboard
│   │   ├── policies/          # Admin policy management
│   │   ├── auctions/          # Admin auction oversight
│   │   ├── settings/          # Admin settings
│   │   └── support/           # Admin support tools
│   ├── broker/                # Broker role routes
│   │   ├── dashboard/         # Broker dashboard
│   │   ├── crm/              # Broker CRM
│   │   ├── portfolio/         # Broker portfolio
│   │   ├── policies/          # Broker policy views
│   │   ├── auctions/          # Broker auction participation
│   │   ├── settings/          # Broker settings
│   │   └── support/           # Broker support
│   └── account-holder/        # Account holder role routes
│       ├── dashboard/         # Account holder dashboard
│       ├── policies/          # Account holder policy management
│       ├── auctions/          # Account holder auction creation
│       ├── settings/          # Account holder settings
│       └── support/           # Account holder support
├── features/                  # Business domain organization
│   ├── account-holder/        # Account holder specific features
│   ├── admin/                 # Admin specific features
│   ├── auctions/              # Auction domain logic
│   ├── auth/                  # Authentication domain
│   ├── broker/                # Broker specific features
│   ├── policies/              # Policy domain logic
│   └── settings/              # Settings domain logic
├── components/
│   ├── ui/                    # Generic shadcn/ui components
│   └── shared/                # Shared application components
└── lib/                       # Pure infrastructure layer
    ├── db.ts                  # Database client singleton
    ├── utils.ts               # Generic utilities only
    └── supabase/              # Supabase configuration
```

## 5.2 Component Enhancement Patterns

**For New Components:**

1. **Domain-Specific Components:** Place in `src/features/{domainName}/components/`
2. **Role-Specific UI Pages:** Place in `src/app/{role}/{domain}/`
3. **Generic UI Components:** Place in `src/components/ui/` (shadcn/ui only)
4. **Shared App Components:** Place in `src/components/shared/`
5. **Infrastructure Components:** Place in `src/lib/`

**Anti-Patterns to Avoid:**
- ❌ Never create technical domains (`src/app/auctions/`, `src/app/policies/`)
- ❌ Never duplicate logic across components
- ❌ Never place domain-specific logic in generic locations
- ❌ Never create new Prisma clients (use singleton from `src/lib/db.ts`)

## 5.3 UI/UX Standards

**Language Conventions:**
- **UI Layer (User-Facing):** All visible text in Spanish
- **Code Layer (Developer-Facing):** All code in English
- **Consistent Professional Spanish:** Neutral, brand-aligned copy
- **System Design:** We are using Shadcn/UI components and following their design system.

**Color Palette:**
- White (`#FFFFFF`)
- Black (`#000000`) 
- Lime Green (`#3AE386`)
- Emerald Green (`#3EA050`)

---
