# 10.8 Security Requirements

- **API Routes Only:** All database operations must go through `/api/` endpoints
- **Server-Side Validation:** Use Zod schemas to validate all incoming data
- **Authentication:** Validate user sessions server-side before database operations
- **File Uploads:** Never use client-side Supabase for file operations
- **Error Handling:** Never expose internal errors to client

---
