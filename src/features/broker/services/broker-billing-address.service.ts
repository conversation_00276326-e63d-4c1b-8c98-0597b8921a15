import { db } from "@/lib/db";
import type { BrokerBillingAddress, Prisma } from "@prisma/client";

export const BrokerBillingAddressService = {
  // Create a new billing address
  async create(data: Prisma.BrokerBillingAddressCreateInput): Promise<BrokerBillingAddress> {
    return db.brokerBillingAddress.create({
      data,
      include: {
        broker: true
      }
    });
  },

  // Get a billing address by ID
  async getById(id: string): Promise<BrokerBillingAddress | null> {
    return db.brokerBillingAddress.findUnique({
      where: { id },
      include: {
        broker: true
      }
    });
  },

  // Get billing address by broker ID
  async getByBrokerId(brokerId: string): Promise<BrokerBillingAddress | null> {
    return db.brokerBillingAddress.findUnique({
      where: { brokerId },
      include: {
        broker: true
      }
    });
  },

  // Update a billing address
  async update(id: string, data: Prisma.BrokerBillingAddressUpdateInput): Promise<BrokerBillingAddress> {
    return db.brokerBillingAddress.update({
      where: { id },
      data,
      include: {
        broker: true
      }
    });
  },

  // Delete a billing address
  async delete(id: string): Promise<BrokerBillingAddress> {
    return db.brokerBillingAddress.delete({
      where: { id }
    });
  }
};