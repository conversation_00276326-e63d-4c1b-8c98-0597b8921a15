- Initial entry for July 24, 2025.

## feat(auctions): enhance auction cards and policy details with real data

- Add licensePlate field to ConfirmedAuction interface
- Replace policyNumber with auction ID in all auction cards
- Update mock data with realistic IDs and statuses
- Show unmasked client data for confirmed auctions
- Add new auction status types and handling
- Disable offer section for non-eligible statuses

### Technical Details
- Updated all broker auction Kanban cards to display `auction.id` instead of `auction.policyNumber` in the header
- Ensured `auction.policyNumber` is correctly displayed within the `PolicyDetailsDrawer` when viewing auction details
- Modified auction card components: `available-auction-card.tsx`, `participating-auction-card.tsx`, `won-auction-card.tsx`, `lost-auction-card.tsx`, `confirmed-auction-card.tsx`
- Enhanced policy details drawer to show appropriate data masking based on auction status