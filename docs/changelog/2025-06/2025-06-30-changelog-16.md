# Changelog - 2025-06-30

## New Features

### Enhanced Coverages Management Component

- **feat(policies):** Implemented a new component for managing policy coverages within the "Añadir nueva póliza" wizard, providing full CRUD (Create, Read, Update, Delete) functionality.
- **refactor(policies):** Replaced the old accordion-based system with a more flexible and user-friendly interface using a new `CoveragesManagement` component and a `CoverageFormModal` for adding and editing coverages.
- **feat(db):** Updated the `Coverage` model in the `prisma.schema` to support custom names, limits, and deductibles, allowing for greater flexibility.
- **refactor(policies):** Refactored the `GuaranteeType` enum to be sorted alphabetically, with a pinned "OTHER" option at the top for custom entries. This change was propagated through all related components to ensure consistency.
- **fix(ui):** Corrected the sorting order in the "Añadir Nueva Cobertura" dropdown to be alphabetical, with "Otra" pinned at the top, based on user feedback.
- **style(ui):** Adjusted the coverage cards to display the description, improving clarity and providing more context to the user.
### UI/UX Enhancements in New Policy Wizard

- **style(policies):** Added titles "Datos de la Póliza" and "Información del Vehículo" to the corresponding tabs in the new policy form for better section clarity.
- **ux(policies):** Improved the "Return to my policies" navigation by changing the microcopy to "Mis Pólizas" for conciseness and clarity.
- **style(policies):** Enhanced the "Mis Pólizas" navigation element by changing it to an `outline` button style for better visibility.
- **fix(layout):** Adjusted the top padding on the new policy page to prevent the navigation button from colliding with the header.
- **style(icons):** Replaced the navigation arrow icon with a more intuitive `Undo2` icon to better represent the "return to list" action.
- **feat(ui):** Implemented a sticky footer for the navigation buttons in the new policy wizard, ensuring they are always visible and accessible.
- **fix(layout):** Corrected a z-index issue where the new sticky footer was overlapping the main sidebar by adjusting the stacking order.
- **style(buttons):** Aligned the sticky footer buttons with the application's design system, applying the correct primary and secondary styles for visual consistency.
- **feat(ui):** Implemented a sticky header for the new policy wizard, ensuring that the main header, "Mis Pólizas" button, stepper, and tabs remain fixed at the top of the viewport during scrolling.
- **style(policies):** Moved the section titles within the new policy form into their respective cards for better visual consistency.
- **style(ui):** Updated the application's color scheme, changing the primary green to #3AE386 and setting all labels to black for improved contrast and readability.
- **fix(layout):** Resolved a critical layout issue where the sticky footer in the new policy wizard was breaking on mobile and tablet devices. The final fix combines a default `left-0` class for mobile with conditional classes that override the `left` property on larger screens, accounting for both expanded and collapsed sidebar states. This robust, CSS-only solution ensures the footer is perfectly aligned across all devices and configurations without regressions.
- **fix(ui):** Corrected a layout issue where the tabs in the new policy wizard were wrapping incorrectly on tablet devices by adjusting the responsive breakpoint from `sm:grid-cols-4` to `lg:grid-cols-4`. This ensures the tabs are perfectly aligned and readable on all devices, including the iPad Mini.
- **style(ui):** Updated the active tab text color from white to black for better readability and design consistency.
- **refactor(ui):** Corrected a significant layout issue in the "Partes Aseguradas" tab by moving the `AddPartyModal` from the `InsuredPartiesAccordion` to the `PolicyDataForm` component. This refactoring fixed a long-standing alignment problem where the section title and the "Añadir Parte" button could not be aligned on the same line.
- **style(ui):** Unified the button styles in the "Partes Aseguradas" card, including the "Editar" and "Eliminar" buttons, to match the design of the "Coberturas" card for a more consistent and polished user experience.
- **fix(responsive):** Corrected a responsive layout issue where the "Coberturas" and "Partes Aseguradas" cards were breaking on mobile screens. The flexbox containers have been updated to stack vertically on smaller devices, ensuring the action buttons remain properly aligned.
- **fix(responsive):** Resolved a header collision issue on mobile devices for the "Coberturas" and "Partes Aseguradas" cards by making the card headers responsive. The headers now stack vertically on small screens and switch to a horizontal layout on larger screens, preventing the title and buttons from overlapping.
- **refactor(ui):** Overhauled the structure of the "Partes Aseguradas" cards to match the "Coberturas" cards, resolving a persistent vertical alignment issue. The `CardHeader` and `CardContent` were replaced with a single flex container, ensuring all content, including action buttons, is correctly aligned.
- **fix(layout):** Redesigned the data import component to improve clarity and user flow by moving the "Rellenar datos manualmente" link to be externally below the drag-and-drop container, with clear visual separation.
- **style(ui):** Reduced the padding on the "Consejo" (Tip) box to make it more compact on mobile screens.
- **style(ui):** Reduced the top padding for the file upload container to bring the content higher on the page.
- **style(ui):** Reduced the top margin of the file upload container to close the gap between the stepper and the file upload component.
- **style(ui):** Updated the "Guardar" and "Guardar Parte" buttons to match the application's primary button style for a consistent user experience.
- **feat(ui):** Added a "Cancelar" button to the "Añadir Nueva Parte" modal to provide a consistent user experience with the "Añadir Nueva Cobertura" modal.
- **style(ui):** Added spacing between the checkboxes in the "Asignar Roles" section of the "Añadir Nueva Parte" modal to improve layout and readability.
- **style(ui):** Adjusted the padding of the checkboxes in the "Asignar Roles" section to move them slightly to the right for better alignment.
- **style(ui):** Adjusted the inactive step circles in the new policy wizard to have a solid, darker background, ensuring the connecting line correctly appears behind them.
- **style(ui):** Changed the color of the separator line in the file upload component to black.
- **style(ui):** Updated the "Rellenar datos manualmente" button to have a gray border, matching the "Atrás" button style.

## Documentation

- **docs(readme):** Generated a new, comprehensive `README.md` to accurately reflect the project's current state, including updated installation instructions, architecture diagrams, and documentation for the Gemini API integration.
- **docs(readme):** Corrected the version badge and updated the license to be "All rights reserved by ZEEGUROS PLATFORM S.L.".

## Housekeeping

- **chore(deps):** Uninstalled unused `tRPC` dependencies (`@trpc/client`, `@trpc/next`, `@trpc/react-query`, `@trpc/server`, `superjson`) to clean up the project.
- **chore(cleanup):** Removed several unused components (`nav-projects.tsx`, `nav-secondary.tsx`, `chart.tsx`, `menubar.tsx`, `pagination.tsx`, `slider.tsx`) to reduce codebase size.
- **chore(cleanup):** Deleted obsolete changelog diagrams related to completed architectural changes.
- **chore(cleanup):** Removed empty directories (`src/types`, `src/app/api/auctions/[id]/bids`) left over from file deletions.