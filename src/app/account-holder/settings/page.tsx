"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { ProfileSettingsForm } from "@/features/settings/components/profile-settings-form";
import { PasswordSettingsForm } from "@/features/settings/components/password-settings-form";
import { User, Shield } from "lucide-react";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Separator } from "@/components/ui/separator";
import { useEffect, useState } from "react";

export default function AccountHolderSettingsPage() {
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 0);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div className="flex flex-1 flex-col">
      {/* <PERSON><PERSON>er */}
      <div className={`sticky top-0 z-10 bg-white py-4 ${isScrolled ? 'shadow-md' : ''} transition-shadow duration-200`}>
        <div className="px-4">
          {/* Title with Sidebar Trigger */}
          <div className="flex items-center gap-4 mb-4">
            <SidebarTrigger className="-ml-1 hover:bg-gray-100" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Configuración</h1>
              <p className="text-gray-600">Administra tu cuenta y preferencias.</p>
            </div>
          </div>
          <Separator className="mb-4" />
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 px-4 pb-4">
        <Tabs defaultValue="profile" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="profile" className="flex items-center gap-2">
              <User className="h-4 w-4" />
              Perfil
            </TabsTrigger>
            <TabsTrigger value="password" className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              Contraseña
            </TabsTrigger>
          </TabsList>

          <TabsContent value="profile" className="space-y-6">
            <ProfileSettingsForm
              title="Información Personal"
              description="Actualiza tu información personal."
            />
          </TabsContent>

          <TabsContent value="password" className="space-y-6">
            <PasswordSettingsForm
              title="Cambiar Contraseña"
              description="Actualiza tu contraseña para mantener tu cuenta segura."
            />
          </TabsContent>

        </Tabs>
      </div>
    </div>
  );
}