'use server'

import { createClient } from '@/lib/supabase/server'
import { revalidatePath } from 'next/cache'

// Update user profile data
export async function updateUserProfile(
  userId: string,
  fullName: string,
  phone?: string
) {
  const supabase = await createClient()

  try {
    console.log('Updating user profile for user:', userId)

    // Check if profile exists
    const { data: existingProfile, error: fetchError } = await supabase
      .from('profiles')
      .select('*')
      .eq('user_id', userId)
      .maybeSingle()

    if (fetchError) {
      console.error('Error fetching profile:', fetchError)
      return { success: false, error: 'Error al obtener el perfil' }
    }

    let result;

    if (existingProfile) {
      // Update existing profile
      const { data, error } = await supabase
        .from('profiles')
        .update({
          full_name: fullName,
          phone,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)
        .select()
        .single()

      if (error) {
        console.error('Error updating profile:', error)
        return { success: false, error: 'Error al actualizar el perfil' }
      }

      result = data
    } else {
      // Create new profile
      const { data, error } = await supabase
        .from('profiles')
        .insert({
          user_id: userId,
          full_name: fullName,
          phone,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single()

      if (error) {
        console.error('Error creating profile:', error)
        return { success: false, error: 'Error al crear el perfil' }
      }

      result = data
    }

    console.log('Profile updated successfully:', result)

    // Revalidate cache to update profile information across all role-based settings
    revalidatePath('/account-holder/settings')
    revalidatePath('/broker/settings')
    revalidatePath('/admin/settings')
    revalidatePath('/dashboard')

    return { success: true, data: result }
  } catch (error) {
    console.error('Unexpected error updating profile:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Error desconocido al actualizar el perfil'
    }
  }
}
