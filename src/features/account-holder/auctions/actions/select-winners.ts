'use server';

import { revalidatePath } from 'next/cache';
import { db as prisma } from '@/lib/db';
import { AuctionState } from '@prisma/client';

// This is a simplified example. In a real application, you would have
// robust authentication and authorization checks.

/**
 * Allows a user to select up to 3 winning bids for their closed auction.
 *
 * @param auctionId The ID of the auction.
 * @param selectedBidIds An array of the selected bid IDs (max 3).
 */
export const selectWinners = async (
  prevState: any,
  formData: FormData
) => {
  try {
    const auctionId = formData.get('auctionId') as string;
    const selectedBidIds = formData.getAll('bidIds') as string[];

    // In a real app, you'd get the user's ID from their session
    const userId = 'user_id_from_session'; // Hardcoded for this example

    if (selectedBidIds.length === 0 || selectedBidIds.length > 3) {
      return {
        success: false,
        message: 'Debe seleccionar entre 1 y 3 ofertas.',
      };
    }

    const auction = await prisma.policyAuction.findUnique({
      where: { id: auctionId },
      select: { userId: true, status: true },
    });

    if (!auction) {
      return { success: false, message: 'Subasta no encontrada.' };
    }

    if (auction.userId !== userId) {
      return { success: false, message: 'No tiene autorización para realizar esta acción.' };
    }

    if (auction.status !== AuctionState.CLOSED) {
      return { success: false, message: 'Solo puede seleccionar ganadores para una subasta cerrada.' };
    }

    // This is a simplified logic. A real implementation would involve creating
    // a new 'WinningBid' model or similar to track the ranked winners and notify them sequentially.
    // For now, we just update the auction status to AWARDED.

    const updatedAuction = await prisma.policyAuction.update({
      where: { id: auctionId },
      data: { status: AuctionState.AWARDED },
    });

    console.log(`Auction ${auctionId} has been awarded. Selected bids: ${selectedBidIds.join(', ')}`);

    // Revalidate the auction page to show the updated status
    revalidatePath(`/auctions/${auctionId}`);

    return {
      success: true,
      message: '¡Ganadores seleccionados con éxito!',
      auction: updatedAuction,
    };
  } catch (error) {
    console.error('Error selecting winners:', error);
    const errorMessage = error instanceof Error ? error.message : 'Ocurrió un error desconocido.';
    return {
      success: false,
      message: errorMessage,
    };
  }
};