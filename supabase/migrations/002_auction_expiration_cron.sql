-- Cron Job 1: Auction Expiration
-- Automatically close expired auctions every 5 minutes
-- This replaces the GitHub Actions workflow for auction expiration

-- Remove any existing cron job with the same name to avoid duplicates
SELECT cron.unschedule('close-expired-auctions');

-- Schedule the auction expiration cron job
-- Runs every 5 minutes to close auctions that have passed their end_date
-- <PERSON><PERSON> proper timezone conversion for Spanish business hours
-- Note: end_date is calculated using working hours business logic (48 working hours, Mon-Fri 06:00-23:59 Madrid time)
SELECT cron.schedule(
    'close-expired-auctions',           -- Job name
    '*/5 * * * *',                      -- Cron expression: every 5 minutes
    $$
    UPDATE public.auction
    SET status = 'CLOSED'::auction_state,
        updated_at = NOW()
    WHERE status = 'OPEN'::auction_state
      AND end_date <= (NOW() AT TIME ZONE 'Europe/Madrid')::timestamp
      AND status != 'CLOSED'::auction_state;
    $$
);

-- Create a function to manually trigger auction expiration (for testing)
CREATE OR REPLACE FUNCTION public.close_expired_auctions_manual()
RETURNS TABLE(closed_count INTEGER, closed_auction_ids UUID[])
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    result_count INTEGER;
    result_ids UUID[];
BEGIN
    -- Get the IDs of auctions that will be closed
    SELECT ARRAY_AGG(id) INTO result_ids
    FROM public.auction
    WHERE status = 'OPEN'::auction_state
      AND end_date <= (NOW() AT TIME ZONE 'Europe/Madrid')::timestamp;

    -- Update expired auctions to CLOSED status
    UPDATE public.auction
    SET status = 'CLOSED'::auction_state,
        updated_at = NOW()
    WHERE status = 'OPEN'::auction_state
      AND end_date <= (NOW() AT TIME ZONE 'Europe/Madrid')::timestamp;

    -- Get the count of updated rows
    GET DIAGNOSTICS result_count = ROW_COUNT;

    -- Return results
    RETURN QUERY SELECT result_count, COALESCE(result_ids, ARRAY[]::UUID[]);
END;
$$;

-- Grant execute permission on the manual function to authenticated users
GRANT EXECUTE ON FUNCTION public.close_expired_auctions_manual() TO authenticated;

-- Add a comment to document the cron job
COMMENT ON FUNCTION public.close_expired_auctions_manual() IS
'Manual function to close expired auctions based on end_date with proper Spain timezone handling. Used for testing and emergency situations. The automated version runs every 5 minutes via pg_cron. Note: end_date is calculated using working hours business logic (48 working hours, Mon-Fri 06:00-23:59 Madrid time).';

-- Log the successful creation of the cron job
DO $$
BEGIN
    RAISE NOTICE 'Auction expiration cron job scheduled successfully. Job name: close-expired-auctions, Schedule: every 5 minutes, Logic: end_date with Spain timezone conversion (calculated using working hours business logic)';
END $$;
