import React from "react";
import { ZCard } from "./z-card";
import { cn } from "../../lib/utils";

interface MetricCardProps {
  title?: React.ReactNode;
  value: string | number;
  trend?: {
    value: number;
    direction: "up" | "down";
  };
  icon?: React.ReactNode;
  className?: string;
}

export function MetricCard({
  title,
  value,
  trend,
  icon,
  className,
  ...props
}: MetricCardProps & Omit<React.HTMLAttributes<HTMLDivElement>, "title">) {
  return (
    <ZCard variant="metric" className={cn("", className)} {...props}>
      <div className="flex justify-between items-start">
        <div>
          {title && (
            <h3 className="text-sm font-medium text-muted-foreground mb-1">
              {title}
            </h3>
          )}
          <p className="text-2xl font-bold">{value}</p>
          {trend && (
            <div
              className={cn(
                "text-xs font-medium mt-1",
                trend.direction === "up" ? "text-green-600" : "text-red-600"
              )}
            >
              {trend.direction === "up" ? "↑" : "↓"}
              {trend.value}%
            </div>
          )}
        </div>
        {icon && <div className="text-muted-foreground">{icon}</div>}
      </div>
    </ZCard>
  );
}