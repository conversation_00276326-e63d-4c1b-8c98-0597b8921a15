import { Metadata } from "next";
import { Suspense } from "react";
import { AuthLoginForm } from "@/features/auth/components/auth/auth-login-form";

export const metadata: Metadata = {
  title: "Iniciar <PERSON> | Zeeguros",
  description: "Inicia sesión en tu cuenta de Zeeguros",
};

export default function LoginPage() {
  return (
    <div className="flex min-h-svh flex-col items-center justify-center gap-6 bg-background p-6 md:p-10">
      <div className="w-full max-w-sm">
        <Suspense fallback={<div>Cargando...</div>}>
          <AuthLoginForm />
        </Suspense>
      </div>
    </div>
  );
}
