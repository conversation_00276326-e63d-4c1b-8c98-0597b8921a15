"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { createClient } from "@/lib/supabase/client";

export default function LogoutPage() {
  const router = useRouter();
  const supabase = createClient();

  useEffect(() => {
    const handleLogout = async () => {
      try {
        // Clear local storage
        localStorage.clear();
        sessionStorage.clear();

        // Sign out using Supabase client
        await supabase.auth.signOut();

        // Redirect to login page
        router.push("/login");
      } catch (error) {
        console.error("Error during logout:", error);
        // If there's an error, still try to redirect to login
        router.push("/login");
      }
    };

    // Add a small delay to ensure the page is fully loaded
    const timeoutId = setTimeout(() => {
      handleLogout();
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [router, supabase]);

  return (
    <div className="flex min-h-screen items-center justify-center">
      <div className="text-center">
        <div className="mb-4 animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto"></div>
        <h1 className="text-xl font-semibold">Cerrando sesión...</h1>
        <p className="text-muted-foreground">Serás redirigido en un momento.</p>
      </div>
    </div>
  );
}
