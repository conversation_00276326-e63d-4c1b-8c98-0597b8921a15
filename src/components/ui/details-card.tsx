import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";

interface DetailsCardProps {
  title: string;
  children?: React.ReactNode;
  className?: string;
  headerContent?: React.ReactNode;
  icon?: React.ReactNode;
  action?: React.ReactNode;
}

export function DetailsCard({
  title,
  children,
  className,
  headerContent,
  icon,
  action
}: DetailsCardProps) {
  return (
    <Card className={cn("transition-all duration-200", className)}>
      <CardHeader className="flex flex-row items-center justify-between p-4">
        <div className="flex items-center gap-3 flex-1 min-w-0">
          {icon && (
            <div className="bg-blue-100 p-2 rounded-lg flex-shrink-0">
              {icon}
            </div>
          )}
          <div className="flex-1 min-w-0">
            <CardTitle className="text-base font-semibold leading-tight truncate">
              {title}
            </CardTitle>
            {headerContent && (
              <div className="mt-1">
                {headerContent}
              </div>
            )}
          </div>
        </div>
        {action && (
          <div className="flex-shrink-0 ml-4">
            {action}
          </div>
        )}
      </CardHeader>
      {children && (
        <CardContent className="px-4 pb-4 pt-0">
          {children}
        </CardContent>
      )}
    </Card>
  );
}