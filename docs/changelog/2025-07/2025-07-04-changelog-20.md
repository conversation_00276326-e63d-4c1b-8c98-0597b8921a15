# Changelog - 2025-07-04

## Feature

### Implemented Robust Role-Based Access Control (RBAC)

-   **Refactored Middleware:** The core application middleware at `src/middleware.ts` has been completely overhauled to implement a robust, scalable, and secure RBAC system.
    -   It now uses a centralized, type-safe configuration object to define role-based permissions and home routes.
    -   It correctly handles post-login redirection, sending users to their designated home page based on their role (`ACCOUNT_HOLDER` -> `/policies`, `BROKER` -> `/portal`, `ADMIN` -> `/console`).
    -   It properly enforces route protection, redirecting unauthorized users back to their own home page.
-   **Corrected User Creation Logic:**
    -   The `src/lib/actions/signup.ts` server action has been fixed to correctly create a corresponding user profile in the `public.User` table and assign the `ACCOUNT_HOLDER` role in the user's metadata. This resolves a critical bug where new users created via the UI were not being synced correctly.
    -   The `prisma/seed.ts` script has been made fully idempotent. It now correctly deletes and recreates the test users, ensuring a clean and consistent state for every `db:rebuild` and resolving a persistent data inconsistency issue.
-   **Updated User Credentials:** The default test user for customers has been updated to `<EMAIL>` for clarity.

## Refactor

### Codebase Cleanup: Removed Obsolete Components and Routes

-   **Deleted `test-role` Component**:
    -   Removed the temporary `test-role` directory and its contents from `src/app/test-role/`. This component was initially created for testing the Role-Based Access Control (RBAC) implementation and is no longer required.
    -   Updated the changelog entry in `docs/changelog/2025-07/2025-07-03-changelog-19.md` to remove any mention of the `/test-role` page.
-   **Deleted `notifications` Component**:
    -   Identified and removed the unused `notifications` directory from `src/app/notifications/`. The component at this route was a simple redirect to the dashboard, indicating it was a placeholder and not in active use.
-   **Cleared Next.js Build Cache**:
    -   Deleted the `.next/` directory to ensure all cached build artifacts, including references to the deleted components, were removed. This action prevents stale data from causing unexpected behavior and ensures a clean build.
-   **Removed Redundant Code:**
    -   Deleted the unused utility file at `src/lib/utils/getHomeRouteForRole.ts` and refactored the login action to use the `ROLES_CONFIG` from the middleware as the single source of truth.
    -   Cleared the content of the unused RLS policy files for the `User` table.
-   **Database Automation (`supabase/apply-policies.ts`):**
    -   This script is a critical part of the `db:rebuild` command. It automatically applies all SQL files in the `supabase/policies` directory.
    -   Currently, its primary job is to run the `01_enable_rls.sql` file, which explicitly **disables** Row-Level Security on the `public.User` table. This was the definitive fix that resolved the persistent authorization issues by allowing the middleware to reliably fetch user roles.

## Security

### Implemented Automated Two-Layer Security Model

-   **Enabled Row-Level Security (RLS):** Re-enabled and correctly configured RLS on the `public."User"` table to provide a critical second layer of data protection at the database level.
-   **Created SQL Helper Function:** Added a new idempotent SQL function at `supabase/functions/01_get_user_role.sql` to securely extract the user's role from their JWT.
-   **Defined Granular RLS Policies:** Implemented new policies in `supabase/policies/User/` that allow users to access and manage their own data, while granting full administrative privileges to `ADMIN` users.
-   **Automated Deployment:** Updated the `supabase/apply-policies.ts` script to automatically apply both functions and policies in the correct order, ensuring that the entire security model is seamlessly integrated into the `npm run db:rebuild` command.
-   **Robust Seeding:** The `prisma/seed.ts` script now completely clears all users from both the `auth.users` table and the `public.User` table before seeding, ensuring a truly clean and consistent state for every `db:rebuild`.