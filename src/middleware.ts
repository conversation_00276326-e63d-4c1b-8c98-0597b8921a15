import { NextResponse, type NextRequest } from "next/server";
import { Role } from "@prisma/client";
import { getServerUser, isValidRole } from "@/features/auth/utils/server-auth";

// Define the structure for each role's configuration.
interface RoleConfig {
  home: string; // The default page for a user with this role.
  allowedPaths: string[]; // The URL prefixes this role can access.
}

// Master configuration for role-based access control.
// This object is the single source of truth for all role-based rules.
export const ROLES_CONFIG: Record<Role, RoleConfig> = {
  ADMIN: {
    home: "/admin/dashboard",
    allowedPaths: [
      "/admin/dashboard",
      "/admin/policies",
      "/admin/auctions",
      "/admin/settings",
      "/admin/support",
      "/api",
    ],
  },
  BROKER: {
    home: "/broker/auctions",
    allowedPaths: [
      "/broker/portfolio",
      "/broker/auctions",
      "/broker/settings",
      "/broker/support",
      "/api",
    ],
  },
  ACCOUNT_HOLDER: {
    home: "/account-holder/policies",
    allowedPaths: [
      "/account-holder/policies",
      "/account-holder/auctions",
      "/account-holder/settings",
      "/account-holder/support",
      "/api",
    ],
  },
};

// Define public paths that do not require authentication.
const PUBLIC_PATHS = [
  "/login",
  "/signup",
  "/forgot-password",
  "/reset-password",
  "/auth/confirm",
  "/error",
  "/unauthorized",
];

export async function middleware(request: NextRequest) {
  let response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  });

  const { pathname } = request.nextUrl;

  // --- 1. Check if the path is public ---
  // If the path is public, allow access immediately.
  if (PUBLIC_PATHS.some((path) => pathname.startsWith(path))) {
    return response;
  }

  // --- 2. Check for authenticated user ---
  // If the path is not public, it's a protected route. We need to validate the user.
  const user = await getServerUser(request, response);

  // If no user is found, redirect to the login page.
  if (!user) {
    return NextResponse.redirect(new URL("/login", request.url));
  }

  // --- 3. Handle Authenticated Users (RBAC) ---
  const userRole = user?.user_metadata?.role as string | undefined;

  // If the user has no role or an invalid role, redirect to unauthorized.
  // This is safe because /unauthorized is a public path and will be caught by step 1.
  if (!userRole || !isValidRole(userRole)) {
    console.error(
      `User ${user.id} has no valid role. Redirecting to /unauthorized.`
    );
    return NextResponse.redirect(new URL("/unauthorized", request.url));
  }

  const roleConfig = ROLES_CONFIG[userRole as Role];

  // If an authenticated user is on the root page, redirect to their role's home.
  if (pathname === "/") {
    return NextResponse.redirect(new URL(roleConfig.home, request.url));
  }

  // Check if the user's role has access to the requested path.
  const isPathAllowed = roleConfig.allowedPaths.some((path) =>
    pathname.startsWith(path)
  );

  if (isPathAllowed) {
    // If the path is allowed, continue.
    return response;
  } else {
    // If the path is not allowed, redirect to their role's home page.
    console.warn(
      `Redirecting user ${user.id} with role ${userRole} from unauthorized path ${pathname} to ${roleConfig.home}`
    );
    return NextResponse.redirect(new URL(roleConfig.home, request.url));
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public assets (e.g. /public folder)
     */
    "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)",
  ],
};
