"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Search, Filter, Car, Bike, FileText, Users, Building2, DollarSign, TrendingUp, Eye } from "lucide-react";
import Link from "next/link";
import { formatAssetType } from "@/lib/format-asset-type";

interface AdminPolicy {
  id: string;
  policyNumber: string;
  accountHolderName: string;
  brokerName: string;
  type: "CAR" | "MOTORCYCLE";
  status: "ACTIVE" | "PENDING" | "EXPIRED" | "DRAFT";
  premium: number;
  startDate: string;
  endDate: string;
  auctionCommission: number;
  lastUpdated: string;
  hasAuction: boolean;
  auctionStatus?: "OPEN" | "CLOSED" | "AWARDED";
}

const mockPolicies: AdminPolicy[] = [
  {
    id: "1",
    policyNumber: "POL-2024-001",
    accountHolderName: "<PERSON> García",
    brokerName: "Broker ABC",
    type: "CAR",
    status: "ACTIVE",
    premium: 450,
    startDate: "2024-01-15",
    endDate: "2025-01-15",
    auctionCommission: 45,
    lastUpdated: "2024-01-15",
    hasAuction: true,
    auctionStatus: "AWARDED"
  },
  {
    id: "2",
    policyNumber: "POL-2024-002",
    accountHolderName: "María López",
    brokerName: "Seguros XYZ",
    type: "MOTORCYCLE",
    status: "PENDING",
    premium: 280,
    startDate: "2024-02-01",
    endDate: "2025-02-01",
    auctionCommission: 28,
    lastUpdated: "2024-01-28",
    hasAuction: true,
    auctionStatus: "OPEN"
  },
  {
    id: "3",
    policyNumber: "POL-2024-003",
    accountHolderName: "Juan Martínez",
    brokerName: "Broker DEF",
    type: "CAR",
    status: "ACTIVE",
    premium: 520,
    startDate: "2024-01-10",
    endDate: "2025-01-10",
    auctionCommission: 52,
    lastUpdated: "2024-01-10",
    hasAuction: true,
    auctionStatus: "AWARDED"
  },
  {
    id: "4",
    policyNumber: "POL-2024-004",
    accountHolderName: "Ana Ruiz",
    brokerName: "Pendiente",
    type: "CAR",
    status: "DRAFT",
    premium: 380,
    startDate: "2024-02-15",
    endDate: "2025-02-15",
    auctionCommission: 0,
    lastUpdated: "2024-02-01",
    hasAuction: false
  },
];

export default function AdminPoliciesPage() {
  const [policies, setPolicies] = useState<AdminPolicy[]>(mockPolicies);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [typeFilter, setTypeFilter] = useState<string>("all");
  const [filteredPolicies, setFilteredPolicies] = useState<AdminPolicy[]>(policies);

  useEffect(() => {
    let filtered = policies;

    if (searchTerm) {
      filtered = filtered.filter(policy => 
        policy.accountHolderName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        policy.brokerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        policy.policyNumber.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter !== "all") {
      filtered = filtered.filter(policy => policy.status === statusFilter);
    }

    if (typeFilter !== "all") {
      filtered = filtered.filter(policy => policy.type === typeFilter);
    }

    setFilteredPolicies(filtered);
  }, [searchTerm, statusFilter, typeFilter, policies]);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "ACTIVE":
        return <Badge className="bg-green-100 text-green-800 border-green-200">Activa</Badge>;
      case "PENDING":
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Pendiente</Badge>;
      case "EXPIRED":
        return <Badge className="bg-red-100 text-red-800 border-red-200">Expirada</Badge>;
      case "DRAFT":
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">Borrador</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getAuctionStatusBadge = (status?: string) => {
    if (!status) return null;
    switch (status) {
      case "OPEN":
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">Subasta Abierta</Badge>;
      case "CLOSED":
        return <Badge className="bg-orange-100 text-orange-800 border-orange-200">Subasta Cerrada</Badge>;
      case "AWARDED":
        return <Badge className="bg-purple-100 text-purple-800 border-purple-200">Subasta Ganada</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const totalPolicies = filteredPolicies.length;
  const activePolicies = filteredPolicies.filter(p => p.status === "ACTIVE").length;
  const totalPremium = filteredPolicies.reduce((sum, policy) => sum + policy.premium, 0);
  const totalCommission = filteredPolicies.reduce((sum, policy) => sum + policy.auctionCommission, 0);
  const openAuctions = filteredPolicies.filter(p => p.auctionStatus === "OPEN").length;

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">
            Administración de Pólizas
          </h1>
          <p className="text-muted-foreground">
            Gestiona todas las pólizas del sistema
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <Link href="/admin/policies/analytics">
              <TrendingUp className="h-4 w-4 mr-2" />
              Análisis
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/admin/policies/reports">
              <FileText className="h-4 w-4 mr-2" />
              Reportes
            </Link>
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Pólizas</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalPolicies}</div>
            <p className="text-xs text-muted-foreground">+10% vs mes anterior</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pólizas Activas</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activePolicies}</div>
            <p className="text-xs text-muted-foreground">+5% vs mes anterior</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Subastas Activas</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{openAuctions}</div>
            <p className="text-xs text-muted-foreground">-2% vs mes anterior</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Primas Totales</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalPremium.toLocaleString()}€</div>
            <p className="text-xs text-muted-foreground">+15% vs mes anterior</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Comisiones</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalCommission.toLocaleString()}€</div>
            <p className="text-xs text-muted-foreground">+15% vs mes anterior</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filtros</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Buscar por titular, corredor o número de póliza..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="rounded-md border border-input bg-background px-3 py-2 text-sm"
              >
                <option value="all">Todos los estados</option>
                <option value="ACTIVE">Activas</option>
                <option value="PENDING">Pendientes</option>
                <option value="EXPIRED">Expiradas</option>
                <option value="DRAFT">Borradores</option>
              </select>
              <select
                value={typeFilter}
                onChange={(e) => setTypeFilter(e.target.value)}
                className="rounded-md border border-input bg-background px-3 py-2 text-sm"
              >
                <option value="all">Todos los tipos</option>
                <option value="CAR">{formatAssetType("CAR")}</option>
                <option value="MOTORCYCLE">{formatAssetType("MOTORCYCLE")}</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Policies List */}
      <div className="space-y-4">
        {filteredPolicies.map((policy) => (
          <Card key={policy.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div className="flex items-start gap-4">
                  <div className="rounded-full bg-primary/10 p-2">
                    {policy.type === "CAR" ? (
                      <Car className="h-5 w-5 text-primary" />
                    ) : (
                      <Bike className="h-5 w-5 text-primary" />
                    )}
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 flex-wrap">
                      <h3 className="font-medium">{policy.accountHolderName}</h3>
                      {getStatusBadge(policy.status)}
                      {policy.hasAuction && getAuctionStatusBadge(policy.auctionStatus)}
                    </div>
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <span>#{policy.policyNumber}</span>
                      <span>•</span>
                      <span>Seguro de {formatAssetType(policy.type)}</span>
                    </div>
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <span>Corredor: {policy.brokerName}</span>
                      <span>•</span>
                      <span>Vigencia: {policy.startDate} - {policy.endDate}</span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-4">
                  <div className="text-right">
                    <div className="text-sm text-muted-foreground">Prima</div>
                    <div className="font-medium">{policy.premium}€</div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm text-muted-foreground">Comisión</div>
                    <div className="font-medium text-green-600">{policy.auctionCommission}€</div>
                  </div>
                  <Button variant="outline" size="sm" asChild>
                    <Link href={`/admin/policies/${policy.id}`}>
                      <Eye className="h-4 w-4 mr-2" />
                      Ver Detalles
                    </Link>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredPolicies.length === 0 && (
        <Card>
          <CardContent className="py-16 text-center">
            <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No se encontraron pólizas</h3>
            <p className="text-muted-foreground">
              {searchTerm || statusFilter !== "all" || typeFilter !== "all"
                ? "No hay pólizas que coincidan con los filtros seleccionados."
                : "No hay pólizas en el sistema."}
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}