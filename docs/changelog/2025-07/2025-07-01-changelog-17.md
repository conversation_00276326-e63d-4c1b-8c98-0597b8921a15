# Changelog - 2025-07-01

## ✨ New Features

- **Cloudflare R2 Integration**: Implemented file uploads to a private Cloudflare R2 bucket for secure storage of policy documents.
  - Added `@aws-sdk/client-s3` dependency.
  - Created a new R2 client utility at `src/lib/r2.ts`.
  - Configured environment variables for R2 credentials.

## ♻️ Refactoring

- **Refactored Policy Extraction API**:
  - Created a new, dedicated API endpoint at `/api/policies/extract`.
  - The endpoint now accepts `multipart/form-data` for more efficient file handling.
  - The old endpoint at `/api/gemini` and its associated library have been deleted.
- **Improved Gemini Prompt**:
  - The prompt sent to the Gemini API is now precisely aligned with the `prisma/schema.prisma` data structure, improving the accuracy and reliability of data extraction.
  - The Gemini model is now configured via the `GEMINI_MODEL` environment variable.

## 🐛 Bug Fixes

- **Fixed Duplicate Toast Notifications**: Corrected the logic for displaying success toasts to ensure it only appears once per extraction.
- **Fixed Duplicate and Infinite API Calls**: Refactored the `FileUpload` component to handle the data extraction API call within a `useEffect` hook. This prevents duplicate calls caused by component re-renders. Additionally, the parent component's handler functions were memoized with `useCallback` to prevent an infinite loop of API requests caused by unstable function references in the `useEffect` dependency array.
---

### Schema-Driven Validation & Data Integrity

- **Configured `prisma-zod-generator`** to automatically generate Zod schemas from the `prisma/schema.prisma` file, ensuring validation logic is always synchronized with the data model.
- **The Policy Extraction API now validates** the JSON response from Gemini against the auto-generated Zod schema. This prevents invalid or incomplete data from reaching the frontend.
- **The Zod schema in the API is now more lenient**, allowing for `null` values. This prevents validation failures when Gemini cannot find a specific piece of information and allows the user to complete the data manually.
- **The `useNewPolicy` hook** has been updated to safely handle potentially null data from the API, providing sensible defaults to ensure the form is always in a valid state.

### Enhanced Frontend Workflow

- **The `PolicyDataForm`** is now a fully controlled component, with its state managed by the parent `NewPolicySteps` component. This allows for reliable, asynchronous population of form data.
- **The data flow is now robust**: `FileUpload` -> `API` -> `useNewPolicy` (transform) -> `NewPolicySteps` (reset form) -> `PolicyDataForm` (display).

### Best Practices

- **Added auto-generated Zod schemas** (`src/lib/zod`) to `.gitignore` to keep the repository clean and prevent merge conflicts.