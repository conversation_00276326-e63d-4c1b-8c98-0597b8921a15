# Introduction

This document outlines the overall project architecture for Zeeguros, including backend systems, shared services, and non-UI specific concerns. Its primary goal is to serve as the guiding architectural blueprint for AI-driven development, ensuring consistency and adherence to chosen patterns and technologies.

**Relationship to Frontend Architecture:**
If the project includes a significant user interface, a separate Frontend Architecture Document will detail the frontend-specific design and MUST be used in conjunction with this document. Core technology stack choices documented herein (see "Tech Stack") are definitive for the entire project, including any frontend components.

## Starter Template or Existing Project

**Status:** Existing Project - Brownfield Development

Zeeguros is an existing Next.js 15+ application that has undergone significant architectural evolution. The project was initially built and has been progressively enhanced to achieve its current state:

- **Foundation:** Next.js 15+ with TypeScript, Tailwind CSS, and Supabase
- **Architecture Evolution:** Transitioned to role-based organization with domain-driven features
- **Current State:** Active development with established patterns and ongoing improvements
- **Development Mode:** Brownfield enhancement and feature completion

## Change Log

| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-01-27 | 2.0 | Corrected architecture document with verified current state | AI Assistant |
| 2025-01-27 | 1.0 | Initial architecture documentation | AI Assistant |
