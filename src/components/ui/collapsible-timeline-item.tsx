'use client';

import * as React from 'react';
import { cn } from '@/lib/utils';
import { Check, Circle, ChevronDown, ChevronRight } from 'lucide-react';
import { formatCurrency } from '@/lib/utils';

interface BidDetail {
  id: string;
  brokerName: string;
  annualPremium?: number;
  createdAt: Date | string;
}

interface CollapsibleTimelineItemProps extends React.HTMLAttributes<HTMLDivElement> {
  status?: 'completed' | 'pending';
  title: string;
  time: string;
  isLast?: boolean;
  bidDetails?: BidDetail[];
}

function formatEventTime(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleDateString("es-ES", {
    day: "numeric",
    month: "short", 
    year: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });
}

const CollapsibleTimelineItem = React.forwardRef<HTMLDivElement, CollapsibleTimelineItemProps>(
  ({ className, status = 'pending', title, time, isLast = false, bidDetails, ...props }, ref) => {
    const [isExpanded, setIsExpanded] = React.useState(false);
    const hasDetails = bidDetails && bidDetails.length > 0;

    const toggleExpanded = () => {
      if (hasDetails) {
        setIsExpanded(!isExpanded);
      }
    };

    return (
      <div
        ref={ref}
        className={cn('relative flex items-start gap-3 pb-6', className)}
        {...props}
      >
        {/* Icon */}
        <div className="relative flex-shrink-0">
          <div
            className={cn(
              'flex h-6 w-6 items-center justify-center rounded-full',
              status === 'completed'
                ? 'bg-green-500 text-white'
                : 'bg-gray-300 text-gray-600'
            )}
          >
            {status === 'completed' ? (
              <Check className="h-3 w-3" />
            ) : (
              <Circle className="h-3 w-3 fill-current" />
            )}
          </div>

          {/* Connector line */}
          {!isLast && (
            <div className="absolute left-3 top-6 h-6 w-0.5 bg-gray-200" />
          )}
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <div 
            className={cn(
              "flex items-center gap-2",
              hasDetails && "cursor-pointer hover:text-green-600"
            )}
            onClick={toggleExpanded}
          >
            <p className="text-sm font-medium text-gray-900">{title}</p>
            {hasDetails && (
              <div className="text-gray-400">
                {isExpanded ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )}
              </div>
            )}
          </div>
          <p className="text-xs text-gray-500 mt-1">{time}</p>

          {/* Expanded details */}
          {hasDetails && isExpanded && (
            <div className="mt-3 space-y-2 border-l-2 border-gray-100 pl-4">
              {bidDetails.map((bid) => (
                <div key={bid.id} className="text-sm text-gray-700">
                  <div className="flex items-center justify-between">
                    <span>
                      <strong>{bid.brokerName}</strong> envió una nueva oferta
                      {bid.annualPremium && (
                        <span className="text-green-600 font-semibold ml-1">
                          por {formatCurrency(bid.annualPremium)} (prima)
                        </span>
                      )}
                    </span>
                    <span className="text-xs text-gray-500 ml-2">
                      {formatEventTime(bid.createdAt)}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    );
  },
);
CollapsibleTimelineItem.displayName = 'CollapsibleTimelineItem';

export { CollapsibleTimelineItem };
export type { BidDetail, CollapsibleTimelineItemProps };