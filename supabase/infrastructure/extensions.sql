-- Supabase Infrastructure: Extensions
-- Enables required PostgreSQL extensions for auction automation
-- This file contains ONLY extension setup - no table creation

-- Enable pg_cron extension for scheduling database jobs
CREATE EXTENSION IF NOT EXISTS pg_cron;

-- Enable pg_net extension for making HTTP requests from database
CREATE EXTENSION IF NOT EXISTS pg_net;

-- Grant necessary permissions for cron jobs
-- Note: In Supabase, these permissions are typically handled automatically
-- but we include them for completeness

-- Log successful extension setup
DO $$
BEGIN
    RAISE NOTICE 'Supabase infrastructure extensions enabled successfully: pg_cron, pg_net';
END $$;
