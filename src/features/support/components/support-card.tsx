import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { PrimaryButton } from "@/components/shared/primary-button";
import { SupportContact, SupportResource } from "../types/support.types";

interface SupportCardProps {
  item: SupportContact | SupportResource;
}

export function SupportCard({ item }: SupportCardProps) {
  const IconComponent = item.icon;
  
  const renderButton = () => {
    if (item.buttonVariant === "default") {
      return (
        <PrimaryButton
          className="w-full"
          asChild
        >
          <a
            href={item.buttonHref}
            target={item.target || "_self"}
            rel={item.target === "_blank" ? "noopener noreferrer" : undefined}
          >
            {item.buttonText}
          </a>
        </PrimaryButton>
      );
    }

    return (
      <Button
        variant={item.buttonVariant || "outline"}
        className="w-full"
        asChild
      >
        <a
          href={item.buttonHref}
          target={item.target || "_self"}
          rel={item.target === "_blank" ? "noopener noreferrer" : undefined}
        >
          {item.buttonText}
        </a>
      </Button>
    );
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <IconComponent className={`h-5 w-5 ${item.buttonVariant === "destructive" ? "text-red-500" : ""}`} />
          {item.title}
        </CardTitle>
        <CardDescription>
          {item.description}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {renderButton()}
      </CardContent>
    </Card>
  );
}