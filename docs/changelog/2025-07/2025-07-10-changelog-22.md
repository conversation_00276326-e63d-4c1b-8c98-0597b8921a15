# Changelog - 2025-07-10

## ✨ New Features

### Policy & Auction Workflow Overhaul

A comprehensive, end-to-end overhaul of the policy and auction system has been completed, implementing the new workflow defined in the `policy-auction-new-workflow-prd.md`.

- **Automated Auction Creation:** Auctions are now automatically created when an administrator approves a policy and marks its status as `RENEW_SOON`.
- **Admin Policy Dashboard:** A new UI at `/admin/policies` allows administrators to view and approve policies, triggering the new auction workflow.
- **Winner Selection UI:** Account holders can now select up to 3 winning bids from a new form on the auction details page for all `CLOSED` auctions.
- **Business Hours Logic:** Auctions now automatically close after 48 business hours (Mon-Fri, 9:00-19:00), with the logic handled by a new `business-hours.ts` utility.
- **Portable Cron Job:** A secure API endpoint and a corresponding GitHub Actions workflow have been created to manage the auto-closure of auctions, designed for easy migration to other schedulers.

## 🏗️ Architectural Improvements

- **Database Naming Convention:** The database schema (`prisma/schema.prisma`) has been refactored to use a consistent `snake_case` naming convention for all tables and fields, improving maintainability.
- **Feature-Based Services:** All new backend logic has been organized into a feature-based directory structure (`src/features`), separating concerns for policies and auctions as per the `refactoring-plan.md`.
- **Singleton Prisma Client:** All new services and actions now correctly use the singleton Prisma client from `src/lib/db.ts`, preventing database connection issues.

## 🐛 Bug Fixes & Refactoring

- **RLS Policy Scripts:** All RLS policy scripts in the `supabase/policies` directory have been updated to use the new `snake_case` table names, resolving the `db:rebuild` script failure.
- **Database Seeding Script:** The `prisma/seed.ts` script was refactored to remove a redundant and erroneous raw SQL query, ensuring the seed process runs reliably.
## 🎨 UI/UX Enhancements

- **Admin Policy Dashboard (`/admin/policies`):**
  - Implemented a new data table using `shadcn/ui` components to display all policies with a `DRAFT` status.
  - Added a "Status" column with color-coded badges for clarity.
  - Included a dropdown menu for each policy, allowing administrators to trigger the `updatePolicyStatus` server action to approve or reject policies.

- **Winner Selection Form (`/auctions/[id]`):**
  - Created a new form that is conditionally rendered on the auction details page only when an auction's status is `CLOSED`.
  - The form displays a list of all bids, with checkboxes allowing the user to select up to three winners.
  - Integrated client-side validation to enforce the 1-to-3 winner selection rule before submission.

- **Auction-Aware Policy Card:**
  - The `PolicyCard` component now fetches and displays the status of any associated auction.
  - The card's call-to-action button is dynamically updated:
    - If an auction is `OPEN` or `CLOSED`, the button text changes to "Ver Subasta" (View Auction) and links directly to the auction details page.
    - Otherwise, it retains its default "Ver Póliza" (View Policy) text and link.