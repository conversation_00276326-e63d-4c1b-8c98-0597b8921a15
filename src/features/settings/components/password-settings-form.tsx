"use client";

import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { createClient } from "@/lib/supabase/client";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/use-toast";
import { Loader2 } from "lucide-react";

const passwordFormSchema = z
  .object({
    current_password: z.string().min(8, {
      message: "La contraseña debe tener al menos 8 caracteres.",
    }),
    new_password: z
      .string()
      .min(8, {
        message: "La contraseña debe tener al menos 8 caracteres.",
      })
      .regex(/[A-Z]/, {
        message: "La contraseña debe contener al menos una letra mayúscula.",
      })
      .regex(/[a-z]/, {
        message: "La contraseña debe contener al menos una letra minúscula.",
      })
      .regex(/[0-9]/, {
        message: "La contraseña debe contener al menos un número.",
      }),
    confirm_password: z.string(),
  })
  .refine((data) => data.new_password === data.confirm_password, {
    message: "Las contraseñas no coinciden.",
    path: ["confirm_password"],
  });

type PasswordFormValues = z.infer<typeof passwordFormSchema>;

interface PasswordSettingsFormProps {
  title?: string;
  description?: string;
}

export function PasswordSettingsForm({
  title = "Cambiar Contraseña",
  description = "Actualiza tu contraseña para mantener tu cuenta segura.",
}: PasswordSettingsFormProps) {
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  const { toast } = useToast();
  const supabase = createClient();

  const passwordForm = useForm<PasswordFormValues>({
    resolver: zodResolver(passwordFormSchema),
    defaultValues: {
      current_password: "",
      new_password: "",
      confirm_password: "",
    },
  });

  const onPasswordSubmit = async (data: PasswordFormValues) => {
    try {
      setIsChangingPassword(true);

      // Update user password
      const { error } = await supabase.auth.updateUser({
        password: data.new_password,
      });

      if (error) {
        throw error;
      }

      // Reset form
      passwordForm.reset({
        current_password: "",
        new_password: "",
        confirm_password: "",
      });

      toast({
        title: "Contraseña actualizada",
        description: "Tu contraseña ha sido actualizada correctamente.",
      });
    } catch (error) {
      console.error("Error updating password:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "No se pudo actualizar tu contraseña. Por favor, inténtalo de nuevo.",
      });
    } finally {
      setIsChangingPassword(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...passwordForm}>
          <form
            id="password-form"
            onSubmit={passwordForm.handleSubmit(onPasswordSubmit)}
            className="space-y-6"
          >
            <FormField
              control={passwordForm.control}
              name="current_password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Contraseña Actual</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="••••••••"
                      type="password"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={passwordForm.control}
              name="new_password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nueva Contraseña</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="••••••••"
                      type="password"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    La contraseña debe tener al menos 8 caracteres,
                    incluyendo una letra mayúscula, una minúscula y un
                    número.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={passwordForm.control}
              name="confirm_password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Confirmar Contraseña</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="••••••••"
                      type="password"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </form>
        </Form>
      </CardContent>
      <CardFooter className="flex justify-end">
        <Button
          type="submit"
          form="password-form"
          disabled={
            isChangingPassword || !passwordForm.formState.isDirty
          }
          variant="default"
        >
          {isChangingPassword && (
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          )}
          {isChangingPassword
            ? "Actualizando..."
            : "Actualizar Contraseña"}
        </Button>
      </CardFooter>
    </Card>
  );
}