# Changelog 37 - 2025-07-31

## ✨ **Feature: Policy Card UI Enhancements**

### **Issue Identified**
Previously, the policy card UI had inconsistencies in displaying information for `DRAFT` and `REJECTED` policies, including duplicate headings and incorrect language for descriptions.

### **Changes Made**

#### **Policy Card (`policy-card.tsx`)**
- Implemented a conditional layout for `REJECTED` status policies, similar to `DRAFT` policies, displaying "Rechazada" and a specific message for rejected documents.
- Modified the action buttons section to ensure that for both `DRAFT` and `REJECTED` statuses, a disabled "Ver detalles" button is displayed, and the "Eliminar" button is shown.
- Hid the "Editar" button for `REJECTED` policies.
- Ensured the general "Ver detalles" button is only shown when the policy is not `DRAFT`, `RENEW_SOON`, `EXPIRED`, or `REJECTED`.
- Resolved duplicate headings for `DRAFT` policies by conditionally hiding the subtitle in the header for `DRAFT` and `REJECTED` statuses.
- Updated the `h3` label for `DRAFT` policies to "Verificación Pendiente" and removed a redundant `h4` element.
- Ensured the description text for `DRAFT` policies is always in Spanish: "Tu póliza está pasando por nuestro proceso de verificación. Tan pronto como la validación esté completa tendrás acceso completo a sus datos."

#### **Policy Status Utilities (`policy-status.ts`)**
- Updated utility functions to correctly handle the `REJECTED` status.

### **Benefits**
1. **Improved UX**: Clearer and more consistent display for `DRAFT` and `REJECTED` policies.
2. **Localization**: All user-facing text for `DRAFT` policies is now correctly in Spanish.
3. **Reduced Redundancy**: Eliminated duplicate headings for a cleaner interface.
4. **Enhanced Functionality**: Correct action button states based on policy status.

### **Files Modified**
- `src/features/policies/components/policy-card.tsx`
- `src/features/policies/utils/policy-status.ts`

### **Architecture Score**
- **Before**: Minor UI inconsistencies and localization issues.
- **After**: Enhanced UI consistency and full localization compliance for policy cards.

This feature ensures that the policy card component now correctly handles `DRAFT` and `REJECTED` policy statuses according to PRD specifications, providing a more polished and user-friendly experience.

## ✨ **Feature: Generalize PolicyDetailsDrawer**

### **Changes Made**
- Moved `PolicyDetailsDrawer` to shared components.
- Added support for both broker and account-holder modes.
- Implemented responsive design improvements.
- Added `REJECTED` status handling in policy utils.
- Updated documentation to reflect implementation status.

### **Files Modified**
- `src/features/policies/components/PolicyDetailsDrawer.tsx` (moved)
- `src/components/shared/PolicyDetailsDrawer.tsx` (new location)
- `src/features/policies/utils/policy-status.ts`

## ✨ **Feature: Implement Policy List API and UI**

### **Changes Made**
- Added new API route for fetching account holder policies with filtering and pagination.
- Created custom hooks for data fetching and client-side filtering.
- Refactored `PolicyList` component to consume real data from API.
- Added loading and error states for better UX.
- Updated documentation to reflect implementation status.

### **Files Modified**
- `src/app/account-holder/policies/page.tsx`
- `src/features/account-holder/policies/components/PolicyList.tsx`
- `src/features/account-holder/policies/hooks/usePolicies.ts`
- `src/app/api/account-holder/policies/route.ts`

## ✨ **Feature: Add Admin Profile and Restructure Asset Relationships**

### **Changes Made**
- Added `AdminProfile` model for administrator users.
- Introduced `PolicyInsuredParty` join table for many-to-many relationship.
- Split `Asset` into generic `Asset` and `VehicleDetails` models.
- Added `AssetType` enum for asset classification.

### **Files Modified**
- `prisma/schema.prisma`

## ✨ **Feature: Rejected Status Styling and Test Pages**

### **Changes Made**
- Implemented rejected status styling in `PolicyStatusBadge` component with red color scheme (`bg-red-100 text-red-800`) for better visual distinction.
- Updated `PolicyCard` component to display "Rechazado" as the title for rejected policies instead of "Sin información del activo".
- Adjusted action buttons for rejected policies to show appropriate disabled states and hide edit functionality.
- Added comprehensive test pages for rejected and expired policy statuses to facilitate development and testing.

### **Benefits**
1. **Enhanced Visual Feedback**: Clear red styling for rejected policies improves user understanding of policy status.
2. **Improved UX**: Consistent title display ("Rechazado") provides clear status indication.
3. **Better Testing**: Dedicated test pages enable easier development and quality assurance.
4. **Status Clarity**: Visual distinction between different policy statuses enhances user experience.

### **Files Modified**
- `src/features/policies/components/policy-status-badge.tsx`
- `src/features/policies/components/policy-card.tsx`
- `src/app/account-holder/policies/test-rejected/page.tsx` (new)
- `src/app/account-holder/policies/test-expired/page.tsx` (new)

### **Architecture Score**
- **Before**: Limited visual distinction for rejected policies and missing test infrastructure.
- **After**: Complete rejected status styling implementation with comprehensive test coverage.

This feature ensures that rejected policies are clearly identifiable through consistent visual styling and appropriate user interface elements, improving the overall user experience when managing policy statuses.

## ✨ **Feature: refactor(database): rename models for better clarity and consistency**

### **Changes Made**
- Rename `BillingAddress` to `BrokerBillingAddress`
- Rename `IdentityVerification` to `BrokerIdentityVerification`
- Rename `Subscription` to `BrokerSubscription`
- Rename `Commission` to `AuctionCommission`
- Update related service files and documentation
- Clean up prisma schema formatting and comments