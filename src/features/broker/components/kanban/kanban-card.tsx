import type {
  ChangeEvent,
  ComponentProps,
  KeyboardEvent,
  Ref,
} from 'react';
import {
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';

import { Button, buttonVariants } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { useDndEvents } from './kanban-accessibility';

/*
Constants
*/

/**
 * Event data transfer types
 * @see https://developer.mozilla.org/en-US/docs/Web/API/DataTransfer
 */
const DATA_TRANSFER_TYPES = {
  CARD: 'kanban-board-card',
};

/*
Card Components
*/

export type KanbanBoardCardProps<T extends { id: string } = { id: string }> = {
  /**
   * A string representing the data to add to the DataTransfer.
   * @see https://developer.mozilla.org/en-US/docs/Web/API/DataTransfer/setData#data
   */
  data: T;
  /**
   * Whether the card is being moved with the keyboard.
   */
  isActive?: boolean;
  /**
   * Click handler for the card.
   */
  onClick?: () => void;
};

const kanbanBoardCardClassNames =
  'rounded-lg border-0 bg-transparent p-0 text-start text-foreground shadow-none mb-2';

export function KanbanBoardCard({
  className,
  data,
  isActive = false,
  onClick,
  ref,
  ...props
}: ComponentProps<'div'> & KanbanBoardCardProps) {
  const [isDragging, setIsDragging] = useState(false);
  const { draggableDescribedById, onDragStart } = useDndEvents();

  const handleClick = (event: React.MouseEvent) => {
    // Only trigger onClick if we're not dragging
    if (!isDragging && onClick) {
      event.preventDefault();
      event.stopPropagation();
      onClick();
    }
  };

  return (
    <div
      aria-describedby={draggableDescribedById}
      className={cn(
        kanbanBoardCardClassNames,
        'inline-flex w-full cursor-grab touch-manipulation flex-col gap-1',
        isDragging
          ? 'cursor-grabbing active:cursor-grabbing'
          : 'group relative',
        isActive && 'rotate-1 transform shadow-lg',
        onClick && 'cursor-pointer hover:shadow-md transition-shadow',
        className,
      )}
      draggable
      onClick={handleClick}
      onDragStart={event => {
        setIsDragging(true);
        event.dataTransfer.effectAllowed = 'move';
        event.dataTransfer.setData(
          DATA_TRANSFER_TYPES.CARD,
          JSON.stringify(data),
        );
        onDragStart(data.id);
      }}
      onDragEnd={() => {
        setIsDragging(false);
      }}
      ref={ref}
      {...props}
    />
  );
}

export function KanbanBoardCardTitle({
  className,
  ref,
  ...props
}: ComponentProps<'h3'>) {
  return (
    <h3 className={cn('text-lg font-medium', className)} ref={ref} {...props} />
  );
}

export function KanbanBoardCardDescription({
  className,
  ref,
  ...props
}: ComponentProps<'p'>) {
  return (
    <p
      className={cn(
        'text-card-foreground text-sm leading-5 whitespace-pre-wrap',
        className,
      )}
      ref={ref}
      {...props}
    />
  );
}

export function KanbanBoardCardTextarea({
  className,
  onChange,
  value,
  ref: externalReference,
  ...props
}: ComponentProps<'textarea'>) {
  const internalReference = useRef<HTMLTextAreaElement | null>(null);

  /**
   * Adjusts the height of the textarea to handle cases where the text exceeds
   * the width of the Textarea and wraps around to the next line.
   */
  const adjustTextareaHeight = () => {
    if (internalReference.current) {
      internalReference.current.style.height = 'auto'; // Reset height to recalculate.
      internalReference.current.style.height = `${internalReference.current.scrollHeight}px`;
    }
  };

  useEffect(() => {
    // When the component mounts, adjust the height of the textarea. This
    // prevents a bug where the text area is too short when the component
    // mounts and has long text.
    adjustTextareaHeight();
  }, []);

  useEffect(() => {
    // When the value is emptied, adjust the height of the textarea. This
    // prevents a bug where the text area is too short when the component
    // is emptied and had long text before being emptied.
    if (value === '') {
      adjustTextareaHeight();
    }
  }, [value]);

  function handleChange(event: ChangeEvent<HTMLTextAreaElement>) {
    onChange?.(event);
    adjustTextareaHeight();
  }

  // Expose the internal ref to the possible external ref.
  useImperativeHandle(externalReference as Ref<HTMLTextAreaElement>, () => internalReference.current!);

  return (
    <Textarea
      className={cn(
        kanbanBoardCardClassNames,
        'min-h-min resize-none overflow-hidden text-sm leading-5',
        className,
      )}
      onChange={handleChange}
      rows={1}
      value={value}
      ref={internalReference}
      {...props}
    />
  );
}

export type KanbanBoardCardButtonGroupProps = {
  disabled?: boolean;
};

export function KanbanBoardCardButtonGroup({
  className,
  disabled = false,
  ref,
  ...props
}: ComponentProps<'div'> & KanbanBoardCardButtonGroupProps) {
  return (
    <div
      ref={ref}
      className={cn(
        'bg-background absolute top-2.5 right-2.5 z-40 hidden items-center',
        !disabled && 'group-focus-within:flex group-hover:flex',
        className,
      )}
      {...props}
    />
  );
}

export type KanbanBoardCardButtonProps = {
  tooltip?: string;
};

/**
 * A button that can be used within a KanbanBoardCard.
 * It's a div under the hood because you shouldn't nest buttons within buttons,
 * and the card is a button.
 */
export function KanbanBoardCardButton({
  className,
  tooltip,
  ref: externalReference,
  ...props
}: ComponentProps<'div'> & KanbanBoardCardButtonProps) {
  const internalReference = useRef<HTMLDivElement | null>(null);

  useImperativeHandle(externalReference as Ref<HTMLDivElement>, () => internalReference.current!);

  // Handler for keydown events to emulate button behavior.
  const handleKeyDown = (event: KeyboardEvent<HTMLDivElement>) => {
    // Check if the pressed key is 'Enter' or 'Space'.
    if (event.key === 'Enter' || event.key === ' ') {
      // Prevent default behavior (like scrolling on Space).
      event.preventDefault();
      // Prevent the event from bubbling up to parent elements.
      event.stopPropagation();

      // Simulate a click on the div.
      internalReference.current?.click();
    }
  };

  const button = (
    <div
      className={cn(
        buttonVariants({ size: 'icon', variant: 'ghost' }),
        'border-border size-5 border hover:cursor-default [&_svg]:size-3.5',
        className,
      )}
      onKeyDown={handleKeyDown}
      role="button"
      tabIndex={0}
      ref={internalReference}
      {...props}
    />
  );

  if (!tooltip) {
    return button;
  }

  return (
    <Tooltip>
      <TooltipTrigger asChild>{button}</TooltipTrigger>

      <TooltipContent align="center" side="bottom">
        {tooltip}
      </TooltipContent>
    </Tooltip>
  );
}