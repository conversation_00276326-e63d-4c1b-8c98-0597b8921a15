'use client'

import { createBrowserClient } from '@supabase/ssr'

/**
 * Creates a browser-side Supabase client for authentication only.
 *
 * SECURITY WARNING: This client should ONLY be used for authentication operations
 * (auth.signIn, auth.signOut, auth.getUser, etc.). All database operations must
 * be performed through server actions to prevent API key exposure.
 *
 * DO NOT use this client for:
 * - Database queries (.from(), .select(), etc.)
 * - Direct table operations (.insert(), .update(), .delete())
 * - Any operation that would expose the anon key to potential attackers
 */
export function createClient() {
  try {
    // Ensure environment variables are defined
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

    if (!supabaseUrl || !supabaseAnonKey) {
      console.error('Missing Supabase environment variables')
      throw new Error('Missing Supabase environment variables')
    }

    const client = createBrowserClient(
      supabaseUrl,
      supabaseA<PERSON>Key
    )

    // Return a proxy that only allows auth operations
    return new Proxy(client, {
      get(target, prop) {
        // Only allow auth operations on the client side
        if (prop === 'auth') {
          return target.auth
        }
        
        // Block database operations
        if (prop === 'from' || prop === 'rpc' || prop === 'storage') {
          throw new Error(
            `Database operation '${String(prop)}' is not allowed on client-side. ` +
            'Use server actions instead for security.'
          )
        }

        return target[prop as keyof typeof target]
      }
    })
  } catch (error) {
    console.error('Error creating Supabase client:', error)
    throw error
  }
}
