import { db } from "@/lib/db";
import { Policy, PolicyStatus } from "@prisma/client";

export const AdminPolicyService = {
  /**
   * Updates the status of a specific policy.
   *
   * @param policyId The ID of the policy to update.
   * @param newStatus The new status to set.
   * @returns The updated policy.
   */
  async updateStatus(policyId: string, newStatus: PolicyStatus): Promise<Policy> {
    return db.policy.update({
      where: { id: policyId },
      data: { status: newStatus },
    });
  },
};