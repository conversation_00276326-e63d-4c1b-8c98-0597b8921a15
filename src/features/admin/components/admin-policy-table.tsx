'use client';

import React, { useState, useTransition } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { MoreHorizontal, CheckCircle, Clock, AlertCircle } from 'lucide-react';
import { Policy, Asset, AccountHolderProfile, PolicyStatus } from '@prisma/client';
import { useToast } from '@/components/ui/use-toast';
import { updatePolicyStatus } from '@/features/admin/actions/policy.actions';

type PolicyWithRelations = Policy & {
  asset: Asset | null;
  accountHolder: AccountHolderProfile | null;
};

interface AdminPolicyTableProps {
  initialPolicies: PolicyWithRelations[];
}

export const AdminPolicyTable: React.FC<AdminPolicyTableProps> = ({
  initialPolicies,
}) => {
  const [policies, setPolicies] = useState(initialPolicies);
  const [isPending, startTransition] = useTransition();
  const { toast } = useToast();

  const handleStatusUpdate = (policyId: string, newStatus: PolicyStatus) => {
    startTransition(async () => {
      try {
        const result = await updatePolicyStatus(policyId, newStatus);
        if (result.success) {
          // Remove the policy from the list as it's no longer a DRAFT
          setPolicies((prevPolicies) =>
            prevPolicies.filter((p) => p.id !== policyId)
          );
          toast({
            title: 'Éxito',
            description: `Póliza actualizada a ${newStatus.toLowerCase()}.`,
          });
        } else {
          toast({
            variant: 'destructive',
            title: 'Error',
            description: result.error || 'Error al actualizar la póliza.',
          });
        }
      } catch (error) {
        toast({
          variant: 'destructive',
          title: 'Error',
          description: 'Ocurrió un error inesperado.',
        });
        console.error(error);
      }
    });
  };

  return (
    <div className="rounded-lg border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Titular</TableHead>
            <TableHead>Vehículo</TableHead>
            <TableHead>Nº Póliza</TableHead>
            <TableHead>Fecha de Carga</TableHead>
            <TableHead>Estado</TableHead>
            <TableHead className="text-right">Acciones</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {policies.length > 0 ? (
            policies.map((policy) => (
              <TableRow key={policy.id}>
                <TableCell>
                  {policy.accountHolder
                    ? `${policy.accountHolder.firstName} ${policy.accountHolder.lastName}`
                    : 'No asignado'}
                </TableCell>
                <TableCell>
                  {policy.asset ? `${policy.asset.brand} ${policy.asset.model}` : 'N/A'}
                </TableCell>
                <TableCell>{policy.policyNumber || 'N/A'}</TableCell>
                <TableCell>
                  {new Date(policy.createdAt).toLocaleDateString('es-ES')}
                </TableCell>
                <TableCell>
                  <Badge variant="outline">Borrador</Badge>
                </TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Abrir menú</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        onClick={() => handleStatusUpdate(policy.id, 'ACTIVE')}
                        disabled={isPending}
                      >
                        <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                        <span>Aprobar (Activa)</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => handleStatusUpdate(policy.id, 'RENEW_SOON')}
                        disabled={isPending}
                      >
                        <Clock className="mr-2 h-4 w-4 text-yellow-500" />
                        <span>Marcar para Renovar</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => handleStatusUpdate(policy.id, 'EXPIRED')}
                        disabled={isPending}
                      >
                        <AlertCircle className="mr-2 h-4 w-4 text-red-500" />
                        <span>Marcar como Expirada</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={6} className="h-24 text-center">
                No hay pólizas en borrador.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
};