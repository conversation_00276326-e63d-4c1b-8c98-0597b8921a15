import { redirect } from "next/navigation";
import { AuthService } from "@/features/auth/services/auth.service";

export default async function Home() {
  // Get user role and redirect to appropriate home route
  // Authentication will be handled by the middleware
  const userRole = await AuthService.getUserRole();
  const homeRoute = AuthService.getHomeRouteForRole(userRole);
  
  redirect(homeRoute);
}
