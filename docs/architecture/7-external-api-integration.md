# 7. External API Integration

## 7.1 Current External Integrations

**Google Gemini API Integration:**
```mermaid
sequenceDiagram
    participant User
    participant Frontend as Next.js Frontend
    participant API as /api/policies/extract
    participant Gemini as Google Gemini API
    participant Bodyguard as Bodyguard Validator
    participant Validator as Zod Schema Validator

    User->>Frontend: Uploads policy document (PDF, JPG, PNG)
    Frontend->>API: POST request with file data
    API->>Gemini: Sends document and prompt for data extraction
    Gemini-->>API: Returns structured JSON data
    
    API->>Bodyguard: Pre-validates raw JSON response
    alt Bodyguard Validation Fails
        Bodyguard-->>API: Returns security/validation error
        API-->>Frontend: Sends 400 Bad Request with error
        Frontend->>User: Displays generic error message
    else Bodyguard Validation Succeeds
        Bodyguard-->>API: Returns sanitized data
        API->>Validator: Transforms and validates with Zod schema
        alt Zod Validation Fails
            Validator-->>API: Returns detailed validation errors
            API-->>Frontend: Sends 400 Bad Request with error details
            Frontend->>User: Displays detailed error message
        else Zod Validation Succeeds
            Validator-->>API: Returns validated data
            API-->>Frontend: Sends 200 OK with extracted JSON data
            Frontend->>User: Displays extracted data in form
        end
    end
```

**Key Integration Features:**
- **AI-Powered Policy Onboarding:** Automatic data extraction from policy documents
- **Robust Data Validation:** Multi-layer validation with graceful error handling
- **Security-First Approach:** Server-side processing with proper sanitization

## 7.2 Integration Enhancement Guidelines

**For New External APIs:**
1. Implement server-side integration patterns only
2. Add comprehensive error handling and validation
3. Follow established security protocols
4. Document integration flows with sequence diagrams
5. Implement proper rate limiting and retry logic

---
