import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";
import { getCurrentUser } from "@/features/auth/utils/server-auth";
import { formatInsurerCompany } from "@/lib/format-insurer";

// Validation schema for query parameters
const auctionsQuerySchema = z.object({
  scope: z.enum(["account-holder"]).optional(),
});

// AuctionSummary type as defined in the story
export type AuctionSummary = {
  id: string;
  identifier: string;
  endsAt: string; // ISO date string
  annualPremium: number;
  currency?: string; // Defaults to 'EUR'
  currentInsurer?: string | null;
  assetDisplayName: string;
  quotesReceived: number;
};

export async function GET(request: NextRequest) {
  try {
    // Authenticate the user
    const user = await getCurrentUser();

    if (!user) {
      return NextResponse.json(
        { error: "No autorizado" },
        { status: 401 }
      );
    }

    // Validate user role - only ACCOUNT_HOLDER can access this endpoint
    if (user.role !== "ACCOUNT_HOLDER") {
      return NextResponse.json(
        { error: "No autorizado" },
        { status: 401 }
      );
    }

    // Parse and validate query parameters
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    const validationResult = auctionsQuerySchema.safeParse(queryParams);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Parámetros de consulta inválidos", details: validationResult.error.errors },
        { status: 400 }
      );
    }

    // Get the user's AccountHolderProfile
    let accountHolderProfile = await db.accountHolderProfile.findUnique({
      where: { userId: user.id },
    });

    // Fallback: create profile if missing and role is ACCOUNT_HOLDER
    if (!accountHolderProfile && user.role === "ACCOUNT_HOLDER") {
      try {
        accountHolderProfile = await db.accountHolderProfile.create({
          data: { userId: user.id }
        });
        console.log(`Created missing AccountHolderProfile for user ${user.id}`);
      } catch (error) {
        console.error(`Failed to create AccountHolderProfile for user ${user.id}:`, error);
        return NextResponse.json(
          { error: "Error al crear perfil de titular de cuenta" },
          { status: 500 }
        );
      }
    }

    if (!accountHolderProfile) {
      return NextResponse.json(
        { error: "Perfil de titular de cuenta no encontrado" },
        { status: 404 }
      );
    }

    // Fetch auctions for the authenticated account holder
    const auctions = await db.auction.findMany({
      where: {
        accountHolderId: accountHolderProfile.id,
      },
      include: {
        policy: {
          include: {
            asset: {
              include: {
                vehicleDetails: true,
              },
            },
            coverages: true,
          },
        },
        bids: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Transform the data to match AuctionSummary type
    const auctionSummaries: AuctionSummary[] = auctions.map((auction) => {
      // Generate auction identifier
      const identifier = `ZEE-AU-${auction.id.slice(-6).toUpperCase()}`;

      // Get annual premium from policy (use policy.premium field)
      const annualPremium = auction.policy.premium?.toNumber() || 0;

      // Get asset display name
      const asset = auction.policy.asset;
      let assetDisplayName = "Activo";
      if (asset && asset.vehicleDetails) {
        const vehicle = asset.vehicleDetails;
        if (asset.assetType === "CAR") {
          assetDisplayName = `${vehicle.brand || ""} ${vehicle.model || ""} (${vehicle.year || ""})`.trim();
        } else if (asset.assetType === "MOTORCYCLE") {
          assetDisplayName = `${vehicle.brand || ""} ${vehicle.model || ""} (${vehicle.year || ""})`.trim();
        }
      } else if (asset && asset.description) {
        assetDisplayName = asset.description;
      }

      // Get current insurer from policy (use insurerCompany field)
      const currentInsurer = auction.policy.insurerCompany ? formatInsurerCompany(auction.policy.insurerCompany) : null;

      // Count quotes received (bids)
      const quotesReceived = auction.bids.length;

      return {
        id: auction.id,
        identifier,
        endsAt: auction.endDate.toISOString(),
        annualPremium,
        currency: "EUR",
        currentInsurer,
        assetDisplayName,
        quotesReceived,
      };
    });

    return NextResponse.json(auctionSummaries, {
      headers: {
        "Cache-Control": "no-store",
      },
    });

  } catch (error) {
    console.error("Error fetching auctions:", error);
    return NextResponse.json(
      { error: "No se pudo cargar la información. Inténtalo de nuevo." },
      { status: 500 }
    );
  }
}
