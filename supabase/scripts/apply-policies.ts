import { Client } from 'pg';
import * as fs from 'fs/promises';
import * as path from 'path';

/**
 * This script applies SQL functions and RLS policies to the database.
 * It's designed to be idempotent, meaning it can be run multiple times without causing errors.
 * It reads all .sql files from the `supabase/functions` and `supabase/policies` directories,
 * and executes them against the database specified in the DATABASE_URL environment variable.
 */
const applyDatabaseConfiguration = async () => {
  // Ensure the DATABASE_URL is set, otherwise the script cannot connect to the DB.
  if (!process.env.DATABASE_URL) {
    console.error('Error: DATABASE_URL environment variable is not set.');
    process.exit(1);
  }

  const client = new Client({
    connectionString: process.env.DATABASE_URL,
  });

  try {
    await client.connect();
    console.log('✅ Connected to the database successfully.');

    /**
     * Applies all .sql files in a directory, used for creating functions.
     */
    const applyFunctions = async (dir: string) => {
      const dirents = await fs.readdir(dir, { withFileTypes: true });
      dirents.sort((a, b) => a.name.localeCompare(b.name));

      for (const dirent of dirents) {
        const fullPath = path.join(dir, dirent.name);
        if (dirent.isDirectory()) {
          await applyFunctions(fullPath);
        } else if (dirent.isFile() && dirent.name.endsWith('.sql')) {
          const sql = await fs.readFile(fullPath, 'utf-8');
          console.log(`   - Applying function: ${dirent.name}`);
          await client.query(sql);
        }
      }
    };

    /**
     * Applies all .sql files in a directory, used for RLS policies.
     */
    const applyPolicies = async (dir: string) => {
      const dirents = await fs.readdir(dir, { withFileTypes: true });
      dirents.sort((a, b) => a.name.localeCompare(b.name));

      for (const dirent of dirents) {
        const fullPath = path.join(dir, dirent.name);
        if (dirent.isDirectory()) {
          await applyPolicies(fullPath);
        } else if (dirent.isFile() && dirent.name.endsWith('.sql')) {
          const sql = await fs.readFile(fullPath, 'utf-8');
          
          // This script now assumes that the SQL files for policies are idempotent,
          // meaning they contain `DROP POLICY IF EXISTS ...` before `CREATE POLICY ...`.
          console.log(`   - Applying script: ${dirent.name}`);
          await client.query(sql);
        }
      }
    };

    // First, apply all SQL functions.
    const functionsDir = path.join(__dirname, 'functions');
    if (await fs.stat(functionsDir).catch(() => false)) {
      console.log('\n--- Applying SQL Functions ---');
      await applyFunctions(functionsDir);
    }

    // Then, apply all RLS policies.
    const policiesDir = path.join(__dirname, 'policies');
    if (await fs.stat(policiesDir).catch(() => false)) {
      console.log('\n--- Applying RLS Policies ---');
      
      // Apply all policies.
      await applyPolicies(policiesDir);
    }

    console.log('\n✅ All SQL configurations applied successfully.');

    // Small delay to allow Supabase to process any pending operations
    await new Promise(resolve => setTimeout(resolve, 100));
  } catch (error) {
    console.error('❌ Error applying SQL files:', error);
    process.exit(1);
  } finally {
    try {
      await client.end();
      console.log('🔌 Database connection closed.');
    } catch (closeError) {
      // Ignore connection close errors - they're often harmless cleanup issues
      console.log('🔌 Database connection closed (with cleanup notice).');
    }
  }
};

applyDatabaseConfiguration();