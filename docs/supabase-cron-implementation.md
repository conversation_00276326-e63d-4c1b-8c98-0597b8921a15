# Comprehensive Auction Notification System Implementation

## Overview

This document describes the implementation of a comprehensive auction lifecycle notification system using Supabase's native cron job capabilities. The system provides complete automation from auction creation to winner notification, including admin notifications, automatic winner selection, and enhanced email services with no manual intervention required.

## Architecture

### Components

1. **pg_cron Extension**: Provides database-native cron job scheduling
2. **pg_net Extension**: Enables HTTP requests from the database
3. **Enhanced Supabase Edge Function**: Handles comprehensive email notifications via Brevo SMTP
4. **Winner Selection Service**: Automatic winner selection algorithm
5. **Enhanced Database Functions**: Custom PL/pgSQL functions for comprehensive auction management
6. **Admin Notification System**: Technical notifications for system administrators

### Cron Jobs

#### Job 1: Auction Expiration (`close-expired-auctions`)
- **Schedule**: Every 5 minutes (`*/5 * * * *`)
- **Purpose**: Automatically close expired auctions
- **Logic**: Updates auction status from `OPEN` to `CLOSED` when `end_date <= NOW()`
- **Implementation**: Direct SQL UPDATE statement

#### Job 2: Comprehensive Auction Notifications (`comprehensive-auction-notifications`)
- **Schedule**: Every 5 minutes (`*/5 * * * *`)
- **Purpose**: Handle complete auction lifecycle notifications
- **Logic**:
  - Detects newly created auctions and sends admin notifications
  - Finds recently closed auctions and triggers comprehensive notification workflow
  - Prevents duplicate notifications with smart database filtering
- **Implementation**: Enhanced PL/pgSQL function with comprehensive event handling

### Notification Workflow

#### Auction Creation Flow
1. **Detection**: Cron job detects newly created auctions (status = 'OPEN')
2. **Admin Notification**: Technical notification sent to system administrators
3. **Logging**: Notification attempt logged in database

#### Auction Closure Flow
1. **Detection**: Cron job detects recently closed auctions (status = 'CLOSED')
2. **Admin Notification**: Technical closure notification sent to administrators
3. **Account Holder Notification**: Auction summary sent to policy holder
4. **Winner Selection**: Automatic algorithm selects top 3 winners based on:
   - Primary criterion (70%): Lowest price offered
   - Secondary criterion (30%): Most comprehensive coverage
5. **Winner Storage**: Winners stored in database with position tracking
6. **Winner Notifications**: Congratulatory emails sent to all 3 winners
7. **Contact Revelation**: Bidirectional contact information sharing
8. **Winner Summary**: Account holder receives table with all winner details

## Database Schema Changes

### New Tables

#### `notification_log`
Tracks notification attempts for monitoring and debugging:

```sql
CREATE TABLE public.notification_log (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    auction_id UUID REFERENCES public.auction(id),
    notification_type TEXT NOT NULL,
    status TEXT NOT NULL, -- 'pending', 'sent', 'failed'
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    sent_at TIMESTAMP WITH TIME ZONE
);
```

### New Functions

#### `trigger_auction_notifications()`
Main function for the notification cron job:
- Finds recently closed auctions
- Calls Edge Function via pg_net
- Handles errors gracefully

#### `close_expired_auctions_manual()`
Manual function for testing auction expiration:
- Returns count and IDs of closed auctions
- Used for testing and emergency situations

#### `trigger_notifications_manual(auction_ids UUID[])`
Manual function for testing notifications:
- Triggers notifications for specific auction IDs
- Used for testing and debugging

## Environment Variables

Add these to your `.env` file:

```bash
# Brevo SMTP for email notifications
BREVO_API_KEY="your_brevo_api_key"
BREVO_SENDER_EMAIL="<EMAIL>"
BREVO_SENDER_NAME="Zeeguros"

# Cron job security (if needed for external calls)
CRON_SECRET="your_secure_random_string"
```

## Deployment

### 1. Enable Extensions

Extensions are automatically enabled via migration:

```sql
CREATE EXTENSION IF NOT EXISTS pg_cron;
CREATE EXTENSION IF NOT EXISTS pg_net;
```

### 2. Deploy Edge Function

Deploy the notification Edge Function to Supabase:

```bash
supabase functions deploy sendAuctionNotification
```

### 3. Run Migrations

Execute the migration files in order:

```bash
# 1. Enable extensions
supabase db push --file supabase/migrations/001_enable_cron_extensions.sql

# 2. Set up auction expiration cron job
supabase db push --file supabase/migrations/002_auction_expiration_cron.sql

# 3. Set up notification cron job
supabase db push --file supabase/migrations/003_notification_cron.sql
```

## Monitoring

### Check Cron Job Status

```sql
-- View all scheduled cron jobs
SELECT jobname, schedule, command, active FROM cron.job;

-- View cron job execution history
SELECT * FROM cron.job_run_details 
ORDER BY start_time DESC 
LIMIT 10;
```

### Monitor Notifications

```sql
-- Check notification log
SELECT * FROM public.notification_log 
ORDER BY created_at DESC 
LIMIT 20;

-- Count notifications by status
SELECT status, COUNT(*) 
FROM public.notification_log 
GROUP BY status;
```

### Manual Testing

```sql
-- Test auction expiration manually
SELECT * FROM public.close_expired_auctions_manual();

-- Test notifications for specific auctions
SELECT * FROM public.trigger_notifications_manual(ARRAY['auction-id-1', 'auction-id-2']);
```

## Quota Considerations

### Supabase Free Plan
- **Database**: 500MB storage, 2GB bandwidth
- **Edge Functions**: 500,000 invocations/month
- **pg_cron**: Available but limited by compute resources

### Supabase Pro Plan
- **Database**: 8GB storage, 250GB bandwidth
- **Edge Functions**: 2,000,000 invocations/month
- **pg_cron**: Better performance with dedicated compute

### Recommendations
- **Free Plan**: Suitable for development and low-volume testing
- **Pro Plan**: Required for production with regular auction activity
- Monitor function invocations to avoid quota limits

## Troubleshooting

### Common Issues

#### 1. Cron Jobs Not Running
```sql
-- Check if extensions are enabled
SELECT extname, extversion FROM pg_extension 
WHERE extname IN ('pg_cron', 'pg_net');

-- Check cron job status
SELECT * FROM cron.job WHERE jobname IN ('close-expired-auctions', 'auction-notifications');
```

#### 2. Edge Function Errors
```sql
-- Check recent HTTP requests
SELECT * FROM net.http_request_queue 
ORDER BY created_at DESC 
LIMIT 10;

-- Check notification log for errors
SELECT * FROM public.notification_log 
WHERE status = 'failed' 
ORDER BY created_at DESC;
```

#### 3. Email Delivery Issues
- Verify Brevo API key is correct
- Check Brevo account quota and status
- Review Edge Function logs in Supabase dashboard

### Debugging Steps

1. **Check Database Logs**: Review Supabase logs for cron job execution
2. **Test Functions Manually**: Use manual trigger functions for testing
3. **Monitor HTTP Requests**: Check pg_net request queue and responses
4. **Verify Environment Variables**: Ensure all required variables are set
5. **Check Email Service**: Test Brevo API connectivity

## Maintenance

### Regular Tasks

1. **Monitor Quota Usage**: Check Supabase dashboard monthly
2. **Review Notification Logs**: Clean up old logs periodically
3. **Test Manual Functions**: Verify system works during maintenance windows
4. **Update Documentation**: Keep this document current with changes

### Cleanup Scripts

```sql
-- Clean up old notification logs (older than 30 days)
DELETE FROM public.notification_log 
WHERE created_at < NOW() - INTERVAL '30 days';

-- Clean up old cron job run details (if accessible)
-- Note: This table is managed by Supabase
```

## Migration from GitHub Actions

The previous GitHub Actions workflow has been completely replaced:

### Removed Components
- `.github/workflows/close-expired-auctions.yml`
- `src/app/api/cron/close-auctions/route.ts`
- `closeExpiredAuctions()` function in auction service

### Benefits of Migration
- **Reliability**: Database-native execution, no external dependencies
- **Performance**: Direct database operations, faster execution
- **Cost**: No GitHub Actions minutes consumed
- **Monitoring**: Better observability with database logs
- **Scalability**: Scales with Supabase infrastructure

## Security Considerations

1. **Function Security**: Edge Functions use Supabase service role key
2. **Database Access**: Functions run with SECURITY DEFINER
3. **HTTP Requests**: pg_net requests are logged and monitored
4. **Email Security**: Brevo API key stored as environment variable
5. **Access Control**: RLS policies protect notification logs

## Quick Reference

### Key Commands

```sql
-- View cron jobs
SELECT jobname, schedule FROM cron.job;

-- Manual auction expiration
SELECT * FROM public.close_expired_auctions_manual();

-- Manual notifications
SELECT * FROM public.trigger_notifications_manual(ARRAY['auction-id']);

-- Check notification status
SELECT status, COUNT(*) FROM public.notification_log GROUP BY status;
```

### Important URLs
- Edge Function: `https://etzouzmoluegjbfikshb.supabase.co/functions/v1/sendAuctionNotification`
- Supabase Dashboard: `https://supabase.com/dashboard/project/etzouzmoluegjbfikshb`

## Support

For issues or questions:
- Check Supabase documentation for pg_cron and pg_net
- Review Edge Function logs in Supabase dashboard
- Monitor notification_log table for delivery status
- Contact development team for system-specific issues
