# Auction Cards Styling Improvements

- **Fixed Ghost Padding**: Eliminated the unwanted bottom padding that appeared when dragging auction cards. This was achieved by removing the margin from the card components themselves and applying it to the parent list item in the Kanban board, ensuring the drag preview is clean while preserving the layout.
- **Adjusted Card Spacing**: Fine-tuned the vertical spacing between cards in the Kanban columns for a more compact and readable layout, reducing the margin from `mb-4` to `mb-1`.
- **Disabled Drag Highlight**: Removed the distracting green border that would appear on columns and around cards during drag-and-drop operations, resulting in a cleaner and less intrusive user experience.
- **Improved Card Padding and Spacing**: Enhanced the visual balance and consistency of the "Participando" card by:
  - Adding `mb-3` to the header for better separation.
  - Wrapping main content elements in a `space-y-2` container for consistent vertical spacing.
  - Adding `mt-4` to the bidding info section for improved separation from the content above.

## Overview

This update focused on improving the consistency and correctness of the auction card components, specifically addressing issues with the `LostAuctionCard`.

## Changes

- **Fixed Duplicate Component Definition**: Resolved a critical error in `lost-auction-card.tsx` where the `LostAuctionCard` component was defined twice, causing build failures.
- **Standardized Card Styling**: The visual appearance of the `LostAuctionCard` has been updated to match the design of the `AvailableAuctionCard`. This creates a more consistent and predictable user interface across the different auction states.

These changes ensure that the "Perdidas" auction cards are now visually aligned with the "Disponibles" cards, providing a more seamless user experience.

## Changes Made

### Nested Interactive Element Fixes
- Introduced `isNestedInInteractiveElement` prop to `AvailableAuctionCard`, `ParticipatingAuctionCard`, `LostAuctionCard`, `WonAuctionCard`, and `ConfirmedAuctionCard` components.
- Conditionally render action buttons as `div` elements instead of `button` elements when `isNestedInInteractiveElement` is `true` to prevent nested interactive elements and resolve hydration errors.
- Modified `KanbanBoardCard` component in <mcfile name="kanban.tsx" path="src/components/kanban.tsx"></mcfile> from a `button` to a `div` to further prevent nested button issues.
- Updated <mcfile name="page.tsx" path="src/app/broker/auctions/page.tsx"></mcfile> to pass `isNestedInInteractiveElement={true}` to all relevant auction card components when rendered within the Kanban board.

### Typography Unification
- Updated shared typography constants in `auction-card-utils.ts` to ensure consistent styling across all auction cards
- Applied uniform text styling using `text-sm text-muted-foreground font-medium` for:
  - Asset type labels (e.g., "Seguro de coche")
  - Coverage type labels (e.g., "Seguro Todo Riesgo")
  - Premium values
  - Client names
- Standardized action button font sizes to `text-xs` across all auction cards (`WonAuctionCard`, `ConfirmedAuctionCard`, `LostAuctionCard`) to match the "PUJAR" button in `AvailableAuctionCard`.

### Won Auction Card Updates
- Added AUCTION_CARD_TYPOGRAPHY import to `won-auction-card.tsx`
- Updated all text elements to use shared typography constants
- Improved bid amount styling consistency
- Fixed emoji rendering issues in the bid amount (💰) and client name (👤) sections

### AuctionBid Comparison Section Improvements
In `participating-auction-card.tsx`:
- Made bid comparison section more compact
- Removed redundant money emoji
- Reduced padding and spacing:
  - Changed padding from `p-3` to `px-3 py-2`
  - Reduced column gap from `gap-3` to `gap-2`
  - Decreased bottom margin from `mb-4` to `mb-3`
- Improved information hierarchy:
  - Moved amount to top position
  - Placed labels below amounts
  - Made both amount and label text more compact
  - Simplified text styling for better consistency

### Button Styling Improvements (Notion-inspired)

### Button Text Centering

- **Centered Button Text**: Applied the `text-center` utility class to action buttons across multiple auction cards to ensure the text is horizontally centered. This improves UI alignment and consistency.
  - `available-auction-card.tsx`: Centered "PUJAR" button.
  - `participating-auction-card.tsx`: Centered "PUJAR DE NUEVO" button.
  - `won-auction-card.tsx`: Centered "CONTACTAR" button.
  - `confirmed-auction-card.tsx`: Centered "GESTIONAR CLIENTE" button.

### Bug Fixes

- **Resolved Hydration Mismatch Error**: Fixed a React hydration error in `participating-auction-card.tsx`. The error was caused by client-side state (`isLocalDragging`) creating a mismatch with the server-rendered HTML.
  - The fix introduces a `hasMounted` state, ensuring that client-side-only logic (like drag-and-drop effects) runs only after the component has successfully mounted, thus preventing the error.
- Redesigned all action buttons across auction cards to match Notion's more subtle style:
  - Reduced vertical padding from `py-3` to `py-2` for shorter buttons
  - Decreased horizontal padding from `px-4` to `px-3` for more compact appearance
  - Changed text size from `text-base`/`text-sm` to `text-xs` for a lighter look
  - Switched from `rounded-lg` to `rounded-md` for softer corners
  - Changed font weight from `font-semibold` to `font-medium` for subtler text
  - Reduced focus ring offset from `focus:ring-offset-2` to `focus:ring-offset-1`
- Applied these changes consistently to all auction card buttons:
  - "GESTIONAR CLIENTE" in `confirmed-auction-card.tsx`
  - "CONTACTAR" in `won-auction-card.tsx`
  - "PUJAR" in `available-auction-card.tsx`
  - "PUJAR DE NUEVO" in `participating-auction-card.tsx`

## Visual Impact
- More consistent appearance across all auction states
- Reduced card heights for better space efficiency
- Improved readability with uniform text styles
- Cleaner, more professional look with removal of redundant emojis
- More elegant, less overwhelming buttons that follow Notion's minimalist design principles
