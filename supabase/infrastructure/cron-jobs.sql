-- Supabase Infrastructure: Cron Jobs
-- Sets up automated auction management and notification jobs
-- This file contains ONLY cron job scheduling - no table or function creation

-- Remove any existing cron jobs to avoid duplicates
SELECT cron.unschedule('close-expired-auctions');
SELECT cron.unschedule('comprehensive-auction-notifications');

-- Cron Job 1: Auction Expiration
-- Automatically close expired auctions every 5 minutes
-- Uses end_date with proper timezone conversion (end_date calculated using working hours business logic)
SELECT cron.schedule(
    'close-expired-auctions',           -- Job name
    '*/5 * * * *',                      -- Cron expression: every 5 minutes
    $$
    UPDATE public.auction
    SET status = 'CLOSED'::auction_state,
        updated_at = NOW()
    WHERE status = 'OPEN'::auction_state
      AND end_date <= (NOW() AT TIME ZONE 'Europe/Madrid')::timestamp
      AND status != 'CLOSED'::auction_state;
    $$
);

-- Cron Job 2: Comprehensive Auction Notifications
-- Handle all auction lifecycle notifications every 5 minutes
SELECT cron.schedule(
    'comprehensive-auction-notifications',    -- Job name
    '*/5 * * * *',                           -- Cron expression: every 5 minutes
    'SELECT public.trigger_comprehensive_auction_notifications();'
);

-- Comments for documentation are handled in the success notice below

-- Log successful cron job setup
DO $$
BEGIN
    RAISE NOTICE 'Supabase infrastructure cron jobs scheduled successfully: auction expiration with timezone handling and comprehensive notifications (every 5 minutes)';
END $$;
