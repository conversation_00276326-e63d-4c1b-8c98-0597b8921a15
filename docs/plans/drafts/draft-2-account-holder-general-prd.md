#  Account Holder General PRD

## 1. Overview
This document aims to describe and synthesize the requirements for the pending features in the Account Holder platform. The platform is a web application that allows account holders to manage their insurance accounts, view their policies, and interact with the policy auctions.

## 2. Scope
This PRD covers the following features:
- Account management (registration, login, forgot password)
- Auction interactions
- Notifications (email)

### 3. Pending Features

#### 3.1 Account management (registration, login, forgot password)
- **Hardening Sign-up:** We should validate first if there are already users with same email, phone number in the platform. If so, we should not allow the user to sign up. If so, we should also give a general feedback that not leak any information about the existence of the user.
- **Privacy & Security:** We should store in the db as a boolean the user decision to Accept terms & condition before sign-up.
- **Sign-up form:** The current phone input is not storing correctly the Phone number in the supabases auth.user when the user is being created. It's only being created in the public.user table.
- **Sign-up form:** The displayname attribute in the auth.users table is not being sync correctly with the display_name attribute in the public.user table.
- **Forgot password:** The current "Reenviar correo" feature should have the same behavior as "Reenviar correo de confirmación" from the sign-up process. It allow users to re-send the confirmation email but the UI gives a feedback blocking the button and a counter of 60s to ask for a new confirmation email.
- **Forgot password:** The current feature should allow user by email to recover their accounts, but do not allow not existing emails to recover their accounts. When user is able to change their password, the system should notify them about the change by email.
- **UX&UI:** Align text and buttons with the rest of the platform. 
- **Sign-up, Login, recover-password email templates:** We should have a professional and consistent email template for the sign-up, login, and recover-password process.

#### 3.2 Auction Creation flow

##### 3.2.1 Auction creation when user upload a policy
- The user should be able to create an auction when they upload a policy, the admin verify it manually, inserts the values and update the status to "RENEW_SOON" and if it's 60 days or less before the expiration date (endDate in the prisma schema model), system will create the auction itself with a duration of 48 hours. 
- **Notification:** The system should trigger a notification email to the user to notify the auction has been created. 
- **Error handling:** If the policy is expired,canceled or active the system should not create the auction.

##### 3.2.2 Policy-card when status is "ACTIVE" and then mutates to"RENEW_SOON"
- **Policy-card mutation:** The policy-cards with status "**ACTIVE**" should mutate to "**RENEW_SOON**" when the policy is 60 days or less before the expiration date and this should be a process automatically. 

- The system should send a notification email to the user about the upcoming expiration date for the policy and encourage them to create an auction. The CTA button of "Renovar" in the policy-card should allows user to see drawer with the details and a visible button to create an auction. When the auction is created, thie CTA button from the policy-card should change to "Ver detalles" and open the drawer details and the button inside should change label to "Ver subasta" and redirect user to the auction details page.

#### 3.3 Auction Details View
- **Auction details view per policy:** Broadly speaking, it should be a complete full view of the auction details for an auction.
The user should be able to view the auction details when they click on the "Ver detalles" button in the auction-summary-card. The auction details should include the following sections:
  - **Auction summary:** This section should include the basic information about the auction, such as the auction identifier, the start date, the end date, the remaining time to end the auction, the current state.
   - **Auction FAQ:** This should be a resume of what the AccountHolder should expect or do in the current state of the Auction, as we have different states like "OPEN", "CLOSED", "SIGNED_POLICY".
  - **Current Policy:** This section should include a button to open the drawer with the policy details. We could rehuse the current PolicyDetailsDrawer.tsx component and shows the details of the linked policy to the auction.
  - **Auction quotes list:** This section should include the list of quotes/offers the auction is being receiving from the brokers. It should sort them by quote amount in descending order. The system should notify the account holder by email when it has a new quote available. 
  This list should handle pagination and show 5 quotes per page. When we click on the details, we could also rehuse the Drawer component to shows the details of the quotes.

#### 3.3.1 Auction quotes details Drawer
  Each quote should include:
      - **Broker details:** name (only "Nombre de pila" or firstname from broker), insurancecompany name, location (Comunidad Autonoma)
    - Quote amount premium per year
    - Quote date
    - Quote included coverages

#### 3.4 Auction Closed (up to 3 brokers) flow
- This flow is only available when the Auction state is equals to "CLOSED" in the frontend is know as "Subasta cerrada" and the auction has at least 1 quote.
- Brokers who previously participated in the auction were charged a 10% withholding fee equivalent to the current policy premium thanks to Stripe.The top three (lowest price + highest coverage) will be charged this fee and will have the right to view the Account Holder's contact information.
- Brokers who did not win will have this fee waived. 
- The AccountHolder will be able to see the full contact information of the brokers that have won the auction. 
- The AccountHolder will not be able to see the rest of non-winner options.
- The AccountHolder will only see the quotes who won, with the same component for the Auction quotes details Drawer:
  - **Broker details:** firstname, lastname, insurance company name, location (Comunidad Autonoma)
  - **Contact details:** phone number, email.
  - Quote amount premium per year
  - Quote date
  - Quote included coverages
- The Auction FAQ should include a section about the winner selection flow. When it explain them in this step "¡Ha terminado la subasta! Conoce las mejores ofertas personalizadas para ti"
- The system should send a notification by email to the Brokers who won the auction.
- The system should send a notification by email to the AccountHolder to notify the Auction has been closed and encourage to discover the winner/winners.

#### 3.4.1 Auction Closed signed contract flow
- This flow is only available when the Auction state is equals to "CLOSED" in the frontend is know as "Subasta Cerrada" and the auction has at least 1 quote.
- The user will have a checkbox to pick who was the broker that they signed the contract.
- The quote that the user has selected will be marked as "IS_SIGNED_CONTRACT" true in the db. We should manage this as an boolean. 
- The user should also have a "UPLOAD FILE" feature to upload the Policy of the new insurance contract.

#### 3.5 Auction Signed Contract flow
- When the AccountHolder confirms the broker with whom he/she has signed and uploaded the new policy.
- It should appear a new section in the auction details called "Signed Contract" and it will reference to the new signed Policy, if the user wants to see the details, it will link to the PolicyDetailsDrawer to see all the related data.
- The state for the auction changes to "SIGNED_POLICY".
- The system should send a notification by email to the AccountHolder to notify the Auction has been signed and thank you for trust in Zeeguros.

#### 3.6 Auction Canceled flow
- The AccountHolder will be able to cancel an auction when the state is "OPEN" and the auction has no bids from brokers.
- The system should send a notification by email to the AccountHolder to notify the Auction has been canceled.

#### 3.6 Auction Canceled flow
- The AccountHolder will be able to cancel an auction when the state is "OPEN" and the auction has no bids from brokers.
- The system should send a notification by email to the AccountHolder to notify the Auction has been canceled.

##### 3.7 Auction Expired flow
- When the auction duration time is over and it doesn't have a any quotes available. The auction state should mutate to the "EXPIRED".
- The system should send a notification by email to the AccountHolder to notify the Auction has finished, but there was not luck this time. Encourage them to try again!