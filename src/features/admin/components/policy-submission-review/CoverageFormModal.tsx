"use client";

import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Coverage, GuaranteeType } from "@/features/admin/hooks/usePolicySubmissionReview";
import { useEffect } from "react";

interface CoverageFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (coverage: Coverage) => void;
  coverage: Coverage | null;
}

const coverageSchema = z.object({
  type: z.nativeEnum(GuaranteeType),
  customName: z.string().optional(),
  limit: z.coerce.number().optional(),
  deductible: z.coerce.number().optional(),
  description: z.string().optional(),
}).refine(
  (data) => {
    if (data.type === GuaranteeType.OTHER) {
      return !!data.customName && data.customName.length > 0;
    }
    return true;
  },
  {
    message: "El nombre de la cobertura es requerido para el tipo 'Otra'.",
    path: ["customName"],
  }
);

const guaranteeTypeLabels: { [key in GuaranteeType]: string } = {
  OTHER: "Otra",
  ADVANCE_COMPENSATION: "Adelanto de Indemnización",
  ASSISTIVE_EQUIPMENT_RENTAL: "Alquiler de Equipamiento Asistencial",
  COLLISION_WITH_ANIMALS: "Colisión con Animales Cinegéticos",
  DRIVER_ACCIDENTS: "Accidentes del Conductor",
  EXTRAORDINARY_RISKS_PERSONS: "Riesgos Extraordinarios (Personas)",
  EXTRAORDINARY_RISKS_VEHICLE: "Riesgos Extraordinarios (Vehículo)",
  FINES_MANAGEMENT: "Gestión de Multas",
  FIRE: "Incendio",
  GLASS_BREAKAGE: "Rotura de Lunas",
  HANDYMAN_SERVICE: "Servicio de Manitas",
  IMMOBILIZATION: "Inmovilización del Vehículo",
  LEGAL_DEFENSE: "Defensa Jurídica",
  LEGAL_REPRESENTATION_EXTENSION: "Ampliación de Defensa Jurídica",
  LICENSE_SUSPENSION: "Retirada de Carnet",
  LICENSE_SUSPENSION_SUBSIDY: "Subsidio por Retirada de Carnet",
  LOAD_LIABILITY: "Responsabilidad de la Carga",
  LOST_KEYS: "Pérdida de Llaves",
  MANDATORY_LIABILITY: "Responsabilidad Civil Obligatoria",
  PERSONAL_BELONGINGS: "Pertenencias Personales",
  PSYCHOLOGICAL_ASSISTANCE: "Asistencia Psicológica",
  REPATRIATION: "Repatriación",
  THEFT: "Robo",
  TOTAL_LOSS_DAMAGE: "Pérdida Total por Daños",
  TOTAL_LOSS_FIRE: "Pérdida Total por Incendio",
  TOTAL_LOSS_THEFT: "Pérdida Total por Robo",
  TOWING_FROM_KM0: "Remolque desde KM 0",
  TRAVEL_ASSISTANCE: "Asistencia en Viaje",
  VEHICLE_DAMAGE: "Daños Propios del Vehículo",
  VEHICLE_REPLACEMENT: "Vehículo de Sustitución",
  VOLUNTARY_LIABILITY: "Responsabilidad Civil Voluntaria",
  WEATHER_DAMAGE: "Daños por Fenómenos Meteorológicos",
  TYRE_DAMAGE: "Daños en Neumáticos",
  MOTORCYCLE_GEAR: "Equipamiento de Motorista",
  NON_STANDARD_ACCESSORIES: "Accesorios no de Serie",
  PET_INJURY: "Lesiones de Mascotas",
  PASSENGER_ACCIDENTS: "Accidentes de Pasajeros",
  GAP_COVERAGE: "Cobertura GAP",
  PARALYZATION_COMPENSATION: "Indemnización por Paralización",
  CARGO_LIABILITY: "Responsabilidad Civil de la Carga",
  UNAUTHORIZED_USE: "Uso no Autorizado",
  ERROR_REFUELING: "Error de Repostaje",
  CHARGING_CABLE: "Cable de Carga",
};

export function CoverageFormModal({
  isOpen,
  onClose,
  onSave,
  coverage,
}: CoverageFormModalProps) {
  const form = useForm<Coverage>({
    resolver: zodResolver(coverageSchema),
    defaultValues: coverage || {
      type: GuaranteeType.OTHER,
      customName: "",
      limit: 0,
      deductible: 0,
      description: "",
    },
  });

  useEffect(() => {
    form.reset(coverage || {
      type: GuaranteeType.OTHER,
      customName: "",
      limit: 0,
      deductible: 0,
      description: "",
    });
  }, [coverage, form]);

  const onSubmit = (data: Coverage) => {
    onSave(data);
    onClose();
  };

  const selectedType = form.watch("type");

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            {coverage ? "Editar Cobertura" : "Añadir Nueva Cobertura"}
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tipo de Cobertura</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecciona un tipo" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {Object.entries(guaranteeTypeLabels)
                        .sort(([, a], [, b]) => {
                          if (a === "Otra") return -1;
                          if (b === "Otra") return 1;
                          return a.localeCompare(b, "es");
                        })
                        .map(([key, label]) => (
                          <SelectItem key={key} value={key}>
                            {label}
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {selectedType === GuaranteeType.OTHER && (
              <FormField
                control={form.control}
                name="customName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nombre de la Cobertura Personalizada</FormLabel>
                    <FormControl>
                      <Input {...field} value={field.value ?? ""} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <FormField
              control={form.control}
              name="limit"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Límite / Suma Asegurada (€)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      {...field}
                      value={field.value ?? ""}
                      onChange={(e) => {
                        const value = parseFloat(e.target.value);
                        field.onChange(isNaN(value) ? "" : value);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="deductible"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Franquicia (€)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      {...field}
                      value={field.value ?? ""}
                      onChange={(e) => {
                        const value = parseFloat(e.target.value);
                        field.onChange(isNaN(value) ? "" : value);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Descripción (Opcional)</FormLabel>
                  <FormControl>
                    <Input {...field} value={field.value ?? ""} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button type="button" variant="ghost" onClick={onClose}>
                Cancelar
              </Button>
              <Button type="submit" variant="default">Guardar</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}