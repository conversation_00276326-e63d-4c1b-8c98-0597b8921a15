-- Enable required extensions for cron jobs
-- This migration enables pg_cron and pg_net extensions for automated auction management

-- Enable pg_cron extension for scheduling database jobs
CREATE EXTENSION IF NOT EXISTS pg_cron;

-- Enable pg_net extension for making HTTP requests from database
CREATE EXTENSION IF NOT EXISTS pg_net;

-- Grant necessary permissions for cron jobs
-- Note: In Supabase, these permissions are typically handled automatically
-- but we include them for completeness

-- Verify extensions are enabled
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'pg_cron') THEN
        RAISE EXCEPTION 'pg_cron extension is not enabled';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'pg_net') THEN
        RAISE EXCEPTION 'pg_net extension is not enabled';
    END IF;
    
    RAISE NOTICE 'Both pg_cron and pg_net extensions are successfully enabled';
END $$;
