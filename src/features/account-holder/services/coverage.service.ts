import { db } from "@/lib/db";
import type { Coverage, Prisma } from "@prisma/client";

export const CoverageService = {
  // Create a new coverage
  async create(data: Prisma.CoverageCreateInput): Promise<Coverage> {
    return db.coverage.create({
      data,
      include: {
        policy: true
      }
    });
  },

  // Get a coverage by ID
  async getById(id: string): Promise<Coverage | null> {
    return db.coverage.findUnique({
      where: { id },
      include: {
        policy: {
          include: {
            accountHolder: true,
            broker: true,
            asset: true
          }
        }
      }
    });
  },

  // Get coverages by policy ID
  async getByPolicyId(policyId: string): Promise<Coverage[]> {
    return db.coverage.findMany({
      where: { policyId },
      orderBy: {
        type: 'asc'
      }
    });
  },

  // Update a coverage
  async update(id: string, data: Prisma.CoverageUpdateInput): Promise<Coverage> {
    return db.coverage.update({
      where: { id },
      data,
      include: {
        policy: true
      }
    });
  },

  // Delete a coverage
  async delete(id: string): Promise<Coverage> {
    return db.coverage.delete({
      where: { id }
    });
  },

  // List coverages with pagination and optional filters
  async list(params: {
    skip?: number;
    take?: number;
    where?: Prisma.CoverageWhereInput;
    orderBy?: Prisma.CoverageOrderByWithRelationInput;
  }): Promise<{ coverages: Coverage[]; total: number }> {
    const { skip = 0, take = 10, where, orderBy } = params;
    
    const [coverages, total] = await Promise.all([
      db.coverage.findMany({
        skip,
        take,
        where,
        orderBy,
        include: {
          policy: {
            include: {
              accountHolder: true,
              broker: true,
              asset: true
            }
          }
        }
      }),
      db.coverage.count({ where })
    ]);

    return { coverages, total };
  },

  // Get default coverages template
  async getDefaultCoverages(): Promise<Partial<Coverage>[]> {
    return [
      { type: 'MANDATORY_LIABILITY' },
      { type: 'VOLUNTARY_LIABILITY' },
      { type: 'LEGAL_DEFENSE' },
      { type: 'DRIVER_ACCIDENTS' },
      { type: 'TRAVEL_ASSISTANCE' },
      { type: 'GLASS_BREAKAGE' },
      { type: 'FIRE' },
      { type: 'THEFT' },
      { type: 'TOTAL_LOSS_THEFT' },
      { type: 'TOTAL_LOSS_DAMAGE' },
      { type: 'TOTAL_LOSS_FIRE' },
      { type: 'VEHICLE_DAMAGE' },
      { type: 'EXTRAORDINARY_RISKS_PERSONS' },
      { type: 'EXTRAORDINARY_RISKS_VEHICLE' }
    ];
  }
};