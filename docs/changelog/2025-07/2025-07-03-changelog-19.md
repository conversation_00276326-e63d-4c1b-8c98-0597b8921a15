# Changelog - 2025-07-03

## 🚀 Features

-   **Policy Validation Questionnaire:** Created a comprehensive questionnaire to define and validate the business rules for the "Añadir Nuevas Pólizas" form. This document (`docs/plans/policy-validation-questionnaire.md`) will be used to determine which fields are required, which need to be anonymized for brokers, and what conditional logic is necessary.

-   **Updated `README.md`:**
    -   Refined the project description and key features to better align with the current codebase.
    -   Added a new Mermaid diagram to illustrate the core data model relationships between `User`, `Policy`, `Asset`, `Auction`, and other key entities.
    -   Updated the "AI Data Extraction Flow" diagram to include the "Bodyguard" validation step, providing a more accurate representation of the two-stage data validation process.
    -   Improved the "Database Schema" section with a more detailed description of the core models based on the `prisma/schema.prisma` file.

-   **Formatted Validation Questionnaire:**
    -   Updated the policy validation questionnaire to use markdown-compatible checkboxes (`- [ ]`).
    -   Added dividers and improved spacing to enhance readability and visual structure, matching the requested format.
    -   Included a summary at the top of the document detailing the total number of fields to be validated.
-   **Architectural Plan for MVP:**
    -   Created a comprehensive architectural plan (`docs/plans/mvp-architecture-final.md`) detailing the multi-tenant, role-based system for the MVP.
    -   The plan includes a full monetization strategy using Stripe for Broker subscriptions and per-deal commissions.
    -   Defined the complete data model, including new `Auction`, `AuctionBid`, `BrokerSubscription`, and `AuctionCommission` tables, and a detailed `AuctionStatus` state machine.
    -   Updated all diagrams to be syntactically correct and easy to follow.

-   **Database & Schema Update:**
    -   Modified the `prisma/schema.prisma` file to align with the new architectural plan, adding the `ADMIN` role and all necessary models and enums for the auction and monetization lifecycle.
    -   Successfully reset the development database and applied the new migrations.
    -   Regenerated the Prisma Client to make the new, type-safe data layer accessible to the application.

-   **RBAC Middleware:**
    -   Implemented and consolidated the Role-Based Access Control (RBAC) middleware in `src/utils/supabase/middleware.ts`.
    -   The middleware now protects all application routes based on user roles (`ADMIN`, `BROKER`, `ACCOUNT_HOLDER`), redirecting unauthorized users to a dedicated page.
    -   Created a comprehensive seed script (`prisma/seed.ts`) to facilitate end-to-end testing of the RBAC implementation.

### 11. Schema Refactoring and Cleanup

*   **Renamed Role**: The `CUSTOMER` role was renamed to `ACCOUNT_HOLDER` across the Prisma schema and all relevant codebase files (e.g., services, components, hooks) to ensure consistent terminology.
*   **Removed Models**: The `TelematicsDevice`, `PolicyClause`, `PolicyConsent`, and `ExtractionFailureLog` models were safely removed from `prisma/schema.prisma` as they are no longer needed.
*   **Removed Enum**: The `OwnerRelation` enum was removed from `prisma/schema.prisma`.
*   **Database Reset and Migration**: All previous database migrations were deleted, the database was reset, and a new migration was created to reflect the updated and cleaned-up Prisma schema.