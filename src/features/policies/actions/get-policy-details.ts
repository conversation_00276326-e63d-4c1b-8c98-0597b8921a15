"use server";

import { db } from "@/lib/db";
import { Asset, AccountHolderProfile, Coverage } from "@prisma/client";
import { transformPolicyInsuredPartiesData } from "../utils/insured-party-transformer";

export interface PolicyDetailsResponse {
  id: string;
  assetId: string | null;
  accountHolderId: string | null;
  policyNumber: string | null;
  status: string | null;
  startDate: Date | null;
  endDate: Date | null;
  premium: number | null;
  productName: string | null;
  asset: Asset | null;
  accountHolder: AccountHolderProfile | null;
  coverages: Coverage[];
  insuredParties: Array<{
    id: string;
    fullName: string;
    firstName?: string;
    lastName?: string;
    identification: string;
    role: string;
  }>;
}

export async function getPolicyDetails(policyId: string): Promise<PolicyDetailsResponse | { error: string }> {
  try {
    const policy = await db.policy.findUnique({
      where: {
        id: policyId,
      },
      include: {
        asset: true,
        accountHolder: true,
        coverages: true,
        insuredParties: {
          include: {
            insuredParty: true
          }
        }
      },
    });

    if (!policy) {
      return { error: "Policy not found" };
    }

    return {
      id: policy.id,
      assetId: policy.assetId,
      accountHolderId: policy.accountHolderId,
      policyNumber: policy.policyNumber,
      status: policy.status,
      startDate: policy.startDate,
      endDate: policy.endDate,
      premium: policy.premium ? parseFloat(policy.premium.toString()) : null,
      productName: policy.productName,
      asset: policy.asset,
      accountHolder: policy.accountHolder,
      coverages: policy.coverages,
      insuredParties: policy.insuredParties ? transformPolicyInsuredPartiesData(policy.insuredParties) : [],
    };
  } catch (error) {
    console.error("Error fetching policy details:", error);
    return { error: "Failed to fetch policy details" };
  }
}