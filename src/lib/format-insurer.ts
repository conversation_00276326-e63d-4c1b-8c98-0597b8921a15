import { InsurerCompany } from '@prisma/client';

/**
 * Formats InsurerCompany enum values into proper Spanish display names
 * with correct capitalization and accents.
 * 
 * @param insurerCompany - The InsurerCompany enum value
 * @returns Formatted Spanish display name
 */
export function formatInsurerCompany(insurerCompany: InsurerCompany | string): string {
  const formatMap: Record<InsurerCompany, string> = {
    MAPFRE: 'Mapfre',
    ALLIANZ: 'Allianz',
    AXA: 'AXA',
    GENERALI: 'Generali',
    SANTALUCIA: 'Santalucía',
    MUTUA_MADRILENA: 'Mutua Madrileña',
    DIRECT_SEGUROS: 'Direct Seguros',
    LINEA_DIRECTA: 'Línea Directa',
    REALE_SEGUROS: 'Reale Seguros',
    ZURICH: 'Zurich',
    CATALANA_OCCIDENTE: 'Catalana Occidente',
    DKV: 'DKV',
    FIATC: 'Fiatc',
    HELVETIA: 'Helvetia',
    PLUS_ULTRA: 'Plus Ultra',
    AEGON: 'Aegon',
    QUALITAS_AUTO: 'Qualitas Auto',
    BALOISE: 'Baloise',
    PELAYO: 'Pelayo',
    MMT_SEGUROS: 'MMT Seguros',
    NATIONALE_NEDERLANDEN: 'Nationale Nederlanden',
    LIBERTY_SEGUROS: 'Liberty Seguros',
    ADESLAS: 'Adeslas',
    ASISA: 'Asisa',
    SANITAS: 'Sanitas',
    CASER: 'Caser',
    OCASO: 'Ocaso',
    ARAG: 'Arag',
    EUROP_ASSISTANCE: 'Europ Assistance',
    INTERMUTUAS: 'Intermutuas',
    MGS_SEGUROS: 'MGS Seguros',
    SEGURCAIXA_ADESLAS: 'SegurCaixa Adeslas',
    VERTI: 'Verti',
    GENESIS: 'Genesis',
    OTRAS: 'Otras'
  };

  return formatMap[insurerCompany as InsurerCompany] || insurerCompany;
}

/**
 * Gets all available insurer companies as formatted display options
 * for use in select components or dropdowns.
 * 
 * @returns Array of objects with value (enum) and label (formatted Spanish name)
 */
export function getInsurerCompanyOptions() {
  return Object.values(InsurerCompany).map(value => ({
    value,
    label: formatInsurerCompany(value)
  }));
}