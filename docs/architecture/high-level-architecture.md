# High Level Architecture

## Technical Summary

<PERSON><PERSON><PERSON><PERSON> implements a role-based monolithic architecture using Next.js 15+ with App Router and server actions, organized around business domains rather than technical concerns. The system employs server-side API routes for all database operations to ensure security, with Supabase providing managed PostgreSQL and authentication services. The architecture prioritizes type safety through TypeScript, maintains clear separation between user roles (<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Account Holder), and follows domain-driven design principles for scalable feature development.

## High Level Overview

**Architectural Style:** Monolithic application with role-based organization
**Repository Structure:** Monorepo with domain-driven feature organization  
**Service Architecture:** Single Next.js application with API routes and Server actions.
**User Interaction Flow:** Role-based dashboards → Domain-specific features → Server-side API operations
**Key Architectural Decisions:**
- Role-based routing structure for clear user experience separation
- Server-side only database operations for enhanced security
- Domain-driven feature organization for maintainable code structure
- TypeScript-first development for type safety and developer experience

---
