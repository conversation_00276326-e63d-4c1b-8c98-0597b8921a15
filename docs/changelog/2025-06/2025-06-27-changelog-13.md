# Changelog - 2025-06-27

## Refactoring

- **Complete Removal of Internationalization (i18n) System**: Executed a comprehensive refactoring to eliminate the application's internationalization (i18n) system in favor of a Spanish-only user interface. This change simplifies the codebase, improves performance by removing a layer of abstraction, and aligns with the convention of keeping user-facing content exclusively in Spanish.

  The process involved several key steps:
  1.  **Component-Level Refactoring**: Systematically scanned and updated all components that used the `useTranslation` hook. Translation keys (e.g., `t('key')`) were replaced with hardcoded Spanish strings in components such as [`AppSidebar`](src/components/app-sidebar.tsx:1), [`DashboardHeader`](src/app/(dashboard)/_components/dashboard-header.tsx:1), [`PolicyReview`](src/app/policies/new-policy/_components/PolicyReview.tsx:1), and [`PolicyDataForm`](src/app/policies/new-policy/_components/PolicyDataForm.tsx:1).
  2.  **Removal of Language Switcher**: The `LanguageSwitcher` component, which allowed users to change languages, was identified and deleted. Its usage within the [`DashboardHeader`](src/app/(dashboard)/_components/dashboard-header.tsx:1) was also removed.
  3.  **Dependency and Artifact Cleanup**: All i18n-related dependencies (`i18next`, `react-i18next`, `i18next-browser-languagedetector`, `i18next-http-backend`) were uninstalled from [`package.json`](package.json:1). All associated artifact files, including the configuration file, the provider, and the translation resource files (`public/locales/`), were permanently deleted.
  4.  **Root Layout Adjustment**: The `I18nProvider` was removed from the root layout ([`src/app/layout.tsx`](src/app/layout.tsx:1)) to complete the decoupling.

  This refactoring ensures that the entire user interface is now consistently in Spanish, while the developer-facing code remains in English, adhering to the project's dual-language convention.
## Fixes

- **PolicyReview Component**: Corrected a bug in the [`PolicyReview`](src/app/policies/new-policy/_components/PolicyReview.tsx:25) component that was causing a TypeScript error. The component was attempting to access `effectiveDate` and `expirationDate` from the `policyData` object, but the `PolicyData` type defines these properties as `startDate` and `endDate`. The code has been updated to use the correct property names, ensuring data is displayed correctly and resolving the type error.

- **Policy Data Model and UI Refactoring**: Conducted a comprehensive technical review of the "Create New Policy" UI and performed a significant refactoring to align the frontend with the backend data model.
  - **Schema Normalization**: Normalized the insurer data by creating a new `Insurer` model and replacing the `insurerName` string with a foreign key (`insurerId`) in the `Policy` model. Added a `@unique` constraint to `policyNumber`.
  - **Currency Precision**: Updated all monetary fields (`premium`, `deductibleAmount`, `amount`, `insuredAmount`) from `Float` to `Decimal` in the [`prisma/schema.prisma`](prisma/schema.prisma:1) to ensure accurate currency handling.
  - **UI and Form Logic**:
    - Refactored the "Forma de Pago" field in [`PolicyDataForm.tsx`](src/app/policies/new-policy/_components/PolicyDataForm.tsx:1) to use `paymentPeriod` instead of `paymentMethod`, ensuring alignment with the `PaymentPeriod` enum in the schema.
    - Enhanced the Zod validation schema to coerce date strings and validate that the policy `endDate` is after the `startDate`.
    - Updated the "Prima" input to correctly parse decimal values regardless of whether a comma or dot is used.
    - Explicitly added the Euro symbol (€) to the "Prima" label for clarity.
  - **"Partes Aseguradas" UI/UX Overhaul**:
    - Refactored the "Partes Aseguradas" section to be AI-first, optimizing the workflow for data population from a Gemini API call.
    - Updated the Gemini API prompt to extract a nested `insuredParties` array, aligning the AI output with the new multi-party data model.
    - Implemented a responsive, mobile-first UI for managing insured parties, featuring a summary view and an "Add/Edit Party" modal.
    - Added full "Edit" functionality to the `AddPartyModal`, allowing users to correct or update AI-extracted data.
    - Corrected multiple styling and layout issues, including tab bar wrapping and active state colors, to ensure a polished and consistent user experience across all devices.
  - **Insured Party Schema Refactoring**:
    - Restructured the data model to better support profile reuse. Created a new `Person` model to store core individual data (`fullName`, `dni`, etc.).
    - The `InsuredParty` model was refactored to act as a linking table between `Policy` and `Person`, storing only the `role` for that specific policy. This eliminates data duplication and enables the "Search Existing Profile" feature.