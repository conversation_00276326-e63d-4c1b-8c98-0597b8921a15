-- BrokerProfile policies: self and ADMIN
DROP POLICY IF EXISTS "broker_profile_select_own" ON public.broker_profile;
CREATE POLICY "broker_profile_select_own"
ON public.broker_profile
FOR SELECT
USING (
  auth.uid() = user_id OR public.get_user_role() = 'ADMIN'
);

DROP POLICY IF EXISTS "broker_profile_insert_self" ON public.broker_profile;
CREATE POLICY "broker_profile_insert_self"
ON public.broker_profile
FOR INSERT
WITH CHECK (
  auth.uid() = user_id OR public.get_user_role() = 'ADMIN'
);

DROP POLICY IF EXISTS "broker_profile_update_own" ON public.broker_profile;
CREATE POLICY "broker_profile_update_own"
ON public.broker_profile
FOR UPDATE
USING (
  auth.uid() = user_id OR public.get_user_role() = 'ADMIN'
)
WITH CHECK (
  auth.uid() = user_id OR public.get_user_role() = 'ADMIN'
);