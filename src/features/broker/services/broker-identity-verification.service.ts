import { db } from "@/lib/db";
import type { BrokerIdentityVerification, Prisma } from "@prisma/client";

export const IdentityVerificationService = {
  // Create a new identity verification
  async create(data: Prisma.BrokerIdentityVerificationCreateInput): Promise<BrokerIdentityVerification> {
    return db.brokerIdentityVerification.create({
      data,
      include: {
        broker: true
      }
    });
  },

  // Get an identity verification by ID
  async getById(id: string): Promise<BrokerIdentityVerification | null> {
    return db.brokerIdentityVerification.findUnique({
      where: { id },
      include: {
        broker: {
          include: {
            user: true
          }
        }
      }
    });
  },

  // Get identity verification by broker ID
  async getByBrokerId(brokerId: string): Promise<BrokerIdentityVerification | null> {
    return db.brokerIdentityVerification.findUnique({
      where: { brokerId },
      include: {
        broker: {
          include: {
            user: true
          }
        }
      }
    });
  },

  // Update an identity verification
  async update(id: string, data: Prisma.BrokerIdentityVerificationUpdateInput): Promise<BrokerIdentityVerification> {
    return db.brokerIdentityVerification.update({
      where: { id },
      data,
      include: {
        broker: true
      }
    });
  },

  // Delete an identity verification
  async delete(id: string): Promise<BrokerIdentityVerification> {
    return db.brokerIdentityVerification.delete({
      where: { id }
    });
  },

  // Verify identity documents and update status
  async verify(id: string): Promise<BrokerIdentityVerification> {
    return db.brokerIdentityVerification.update({
      where: { id },
      data: {
        verifiedAt: new Date(),
        broker: {
          update: {
            kycStatus: 'VERIFIED'
          }
        }
      },
      include: {
        broker: true
      }
    });
  },

  // Reject identity documents and update status
  async reject(id: string): Promise<BrokerIdentityVerification> {
    return db.brokerIdentityVerification.update({
      where: { id },
      data: {
        broker: {
          update: {
            kycStatus: 'REJECTED'
          }
        }
      },
      include: {
        broker: true
      }
    });
  }
};