import { db } from "@/lib/db";
import type { User, Prisma, Role } from "@prisma/client";

export const UserService = {
  // Create a new user
  async create(data: Prisma.UserCreateInput): Promise<User> {
    return db.user.create({
      data,
      include: {
        accountHolder: true,
        brokerProfile: true,
        policies: true,
        auctions: true
      }
    });
  },

  // Get a user by ID
  async getById(id: string): Promise<User | null> {
    return db.user.findUnique({
      where: { id },
      include: {
        accountHolder: true,
        brokerProfile: {
          include: {
            brokerBillingAddress: true,
      brokerIdentityVerification: true
          }
        },
        policies: {
          include: {
            asset: true,
            coverages: true
          }
        },
        auctions: {
          include: {
            asset: true,
            bids: true
          }
        }
      }
    });
  },

  // Get a user by email
  async getByEmail(email: string): Promise<User | null> {
    return db.user.findUnique({
      where: { email },
      include: {
        accountHolder: true,
        brokerProfile: {
          include: {
            brokerBillingAddress: true,
      brokerIdentityVerification: true
          }
        }
      }
    });
  },

  // Update a user
  async update(id: string, data: Prisma.UserUpdateInput): Promise<User> {
    return db.user.update({
      where: { id },
      data,
      include: {
        accountHolder: true,
        brokerProfile: true
      }
    });
  },

  // Delete a user
  async delete(id: string): Promise<User> {
    return db.user.delete({
      where: { id }
    });
  },

  // List users with pagination and optional filters
  async list(params: {
    skip?: number;
    take?: number;
    where?: Prisma.UserWhereInput;
    orderBy?: Prisma.UserOrderByWithRelationInput;
  }): Promise<{ users: User[]; total: number }> {
    const { skip = 0, take = 10, where, orderBy } = params;
    
    const [users, total] = await Promise.all([
      db.user.findMany({
        skip,
        take,
        where,
        orderBy,
        include: {
          accountHolder: true,
          brokerProfile: {
            include: {
              brokerBillingAddress: true,
      brokerIdentityVerification: true
            }
          }
        }
      }),
      db.user.count({ where })
    ]);

    return { users, total };
  },

  // Get users by role
  async getByRole(role: Role): Promise<User[]> {
    return db.user.findMany({
      where: { role },
      include: {
        accountHolder: true,
        brokerProfile: {
          include: {
            brokerBillingAddress: true,
      brokerIdentityVerification: true
          }
        }
      }
    });
  }
};