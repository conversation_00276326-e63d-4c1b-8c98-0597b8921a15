# 8.1 Project Structure Overview

```
zee-next-app/
├── .ai/                          # AI development logs
│   └── debug-log.md              # Debug information
├── .bmad-core/                   # BMAD system configuration
│   ├── agent-teams/              # Agent team configurations
│   ├── agents/                   # Individual agent definitions
│   ├── bmad-core/                # Core BMAD documentation
│   ├── checklists/               # Development checklists
│   ├── core-config.yaml          # Core BMAD configuration
│   ├── data/                     # BMAD knowledge base
│   ├── enhanced-ide-development-workflow.md  # IDE workflow guide
│   ├── install-manifest.yaml     # Installation manifest
│   ├── tasks/                    # BMAD task definitions
│   ├── templates/                # Document templates
│   ├── user-guide.md             # BMAD user guide
│   ├── utils/                    # BMAD utilities
│   ├── workflows/                # BMAD workflows
│   └── working-in-the-brownfield.md  # Brownfield development guide
├── .env.example                  # Environment variables template
├── .eslintrc.json                # ESLint configuration
├── .github/                      # GitHub configuration
│   └── workflows/                # GitHub Actions workflows
│       └── close-expired-auctions.yml  # Auction cleanup workflow
├── .gitignore                    # Git ignore rules
├── .prettierrc.json              # Prettier configuration
├── .trae/                        # Trae IDE configuration
│   └── rules/                    # Trae agent rules
├── README.md                     # Project documentation
├── components.json               # Shadcn/ui configuration
├── docs/                         # Project documentation
│   ├── Archived/                 # Archived documentation
│   │   ├── diagrams/             # Archived diagrams
│   │   ├── plans/                # Archived plans
│   │   ├── rules/                # Archived rules
│   │   └── samples/              # Archived samples
│   ├── architecture/             # Architecture documentation
│   │   ├── 2-enhancement-scope.md           # Enhancement scope
│   │   ├── 3-tech-stack-alignment.md        # Technology stack details
│   │   ├── 4-data-models-and-schema.md      # Data models
│   │   ├── 5-component-architecture.md      # Component architecture
│   │   ├── 6-api-design-and-integration.md  # API design
│   │   ├── 7-external-api-integration.md    # External API integration
│   │   ├── 8-source-tree-integration.md     # Source tree integration
│   │   ├── 9-infrastructure-and-deployment-integration.md  # Infrastructure
│   │   ├── 10-coding-standards-and-conventions.md  # Development standards
│   │   ├── 11-testing-strategy.md           # Testing strategy
│   │   ├── 12-security-integration.md       # Security integration
│   │   ├── 13-checklist-results-report.md   # Checklist results
│   │   ├── 14-next-steps.md                 # Next steps
│   │   ├── 15-conclusion.md                 # Conclusion
│   │   ├── high-level-architecture.md       # High-level architecture
│   │   ├── index.md                         # Architecture index
│   │   └── introduction.md                  # Architecture introduction
│   ├── architecture.md                      # Main architecture document
│   ├── changelog/                            # Version history
│   │   ├── 2025-04/                         # April 2025 changes
│   │   ├── 2025-06/                         # June 2025 changes
│   │   └── 2025-07/                         # July 2025 changes
│   ├── plans/                               # Development plans
│   │   ├── account-holder-journey-enhancement-prd/  # Account holder PRD
│   │   └── account-holder-journey-enhancement-prd.md  # Account holder PRD
│   └── stories/                             # User stories (BMAD)
│       └── 1.1.secure-server-side-api-foundation.md  # API foundation story
├── next.config.js                           # Next.js configuration
├── package-lock.json                        # Dependency lock file
├── package.json                             # Dependencies and scripts
├── postcss.config.js                        # PostCSS configuration
├── prisma/                                  # Database schema and migrations
│   ├── schema.prisma                        # Database schema definition
│   └── seed.ts                              # Database seeding script
├── public/                                  # Static assets
│   ├── apple-icon.svg                       # Apple touch icon
│   ├── icon.svg                             # App icon
│   ├── logo-short-dark.svg                  # Dark logo
│   └── logo-short-light.svg                 # Light logo
├── src/                                     # Source code (Screaming Architecture)
│   ├── app/                                 # Next.js App Router (Role-based organization)
│   │   ├── (public)/                        # Public routes (landing, auth)
│   │   │   ├── auth/                        # Authentication pages
│   │   │   ├── landing/                     # Landing page
│   │   │   └── layout.tsx                   # Public layout
│   │   ├── _components/                     # App-level shared components
│   │   │   └── providers/                   # React providers
│   │   │       └── query-client-provider.tsx  # TanStack Query provider
│   │   ├── account-holder/                  # Account holder role routes
│   │   │   ├── dashboard/                   # Account holder dashboard
│   │   │   ├── policies/                    # Policy management
│   │   │   ├── auctions/                    # Auction creation/management
│   │   │   ├── settings/                    # Account settings
│   │   │   ├── support/                     # Support pages
│   │   │   └── layout.tsx                   # Account holder layout
│   │   ├── admin/                           # Admin role routes
│   │   │   ├── dashboard/                   # Admin dashboard
│   │   │   ├── policies/                    # Policy oversight
│   │   │   ├── auctions/                    # Auction management
│   │   │   ├── settings/                    # System settings
│   │   │   ├── support/                     # Support management
│   │   │   └── layout.tsx                   # Admin layout
│   │   ├── broker/                          # Broker role routes
│   │   │   ├── dashboard/                   # Broker dashboard
│   │   │   ├── crm/                         # Customer relationship management
│   │   │   ├── portfolio/                   # Portfolio management
│   │   │   ├── policies/                    # Policy views
│   │   │   ├── auctions/                    # Auction participation (includes Kanban)
│   │   │   ├── settings/                    # Broker settings
│   │   │   ├── support/                     # Support access
│   │   │   └── layout.tsx                   # Broker layout
│   │   ├── api/                             # API routes (server-side only)
│   │   │   ├── auth/                        # Authentication endpoints
│   │   │   ├── policies/                    # Policy CRUD operations
│   │   │   ├── auctions/                    # Auction operations
│   │   │   ├── users/                       # User management
│   │   │   └── upload/                      # File upload endpoints
│   │   ├── layout.tsx                       # Root layout
│   │   └── page.tsx                         # Root page
│   ├── components/                          # Reusable UI components
│   │   ├── shared/                          # Shared application components
│   │   │   ├── app-sidebar.tsx              # Application sidebar
│   │   │   ├── countdown.tsx                # Countdown component
│   │   │   ├── dashboard-layout-client.tsx  # Dashboard layout
│   │   │   ├── drawer.tsx                   # Drawer component
│   │   │   ├── metric-card.tsx              # Metric display card
│   │   │   ├── nav-main.tsx                 # Main navigation
│   │   │   ├── page-layout.tsx              # Page layout wrapper
│   │   │   ├── price-indicator.tsx          # Price indicator
│   │   │   ├── primary-button.tsx           # Primary button
│   │   │   ├── z-badge.tsx                  # Custom badge
│   │   │   └── z-card.tsx                   # Custom card
│   │   └── ui/                              # Generic shadcn/ui components (35 components)
│   │       ├── accordion.tsx                # Accordion component
│   │       ├── alert-dialog.tsx             # Alert dialog
│   │       ├── alert.tsx                    # Alert component
│   │       ├── avatar.tsx                   # Avatar component
│   │       ├── badge.tsx                    # Badge component
│   │       ├── breadcrumb.tsx               # Breadcrumb navigation
│   │       ├── button.tsx                   # Button component
│   │       ├── calendar.tsx                 # Calendar component
│   │       ├── card.tsx                     # Card component
│   │       ├── checkbox.tsx                 # Checkbox component
│   │       ├── collapsible.tsx              # Collapsible component
│   │       ├── command.tsx                  # Command palette
│   │       ├── context-menu.tsx             # Context menu
│   │       ├── dialog.tsx                   # Dialog component
│   │       ├── dropdown-menu.tsx            # Dropdown menu
│   │       ├── file-upload.tsx              # File upload component
│   │       ├── form.tsx                     # Form components
│   │       ├── hover-card.tsx               # Hover card
│   │       ├── input-otp.tsx                # OTP input
│   │       ├── input.tsx                    # Input component
│   │       ├── label.tsx                    # Label component
│   │       ├── navigation-menu.tsx          # Navigation menu
│   │       ├── phone-input.tsx              # Phone input
│   │       ├── popover.tsx                  # Popover component
│   │       ├── progress.tsx                 # Progress bar
│   │       ├── radio-group.tsx              # Radio group
│   │       ├── scroll-area.tsx              # Scroll area
│   │       ├── select.tsx                   # Select component
│   │       ├── separator.tsx                # Separator component
│   │       ├── sheet.tsx                    # Sheet component
│   │       ├── sidebar.tsx                  # Sidebar component
│   │       ├── skeleton.tsx                 # Skeleton loader
│   │       ├── switch.tsx                   # Switch component
│   │       ├── table.tsx                    # Table component
│   │       ├── tabs.tsx                     # Tabs component
│   │       ├── textarea.tsx                 # Textarea component
│   │       ├── toast.tsx                    # Toast notifications
│   │       ├── toggle-group.tsx             # Toggle group
│   │       ├── toggle.tsx                   # Toggle component
│   │       ├── tooltip.tsx                  # Tooltip component
│   │       └── use-toast.ts                 # Toast hook
│   ├── features/                            # Business domain organization
│   │   ├── account-holder/                  # Account holder specific features
│   │   │   ├── components/                  # Account holder components
│   │   │   ├── hooks/                       # Account holder hooks
│   │   │   ├── services/                    # Account holder services
│   │   │   └── types/                       # Account holder types
│   │   ├── admin/                           # Admin specific features
│   │   │   ├── components/                  # Admin components
│   │   │   ├── hooks/                       # Admin hooks
│   │   │   ├── services/                    # Admin services
│   │   │   └── types/                       # Admin types
│   │   ├── auctions/                        # Auction domain logic
│   │   │   ├── actions/                     # Server actions
│   │   │   ├── components/                  # Auction components
│   │   │   ├── hooks/                       # Auction hooks
│   │   │   ├── services/                    # Auction services
│   │   │   └── types/                       # Auction types
│   │   ├── auth/                            # Authentication domain
│   │   │   ├── components/                  # Auth components
│   │   │   ├── hooks/                       # Auth hooks
│   │   │   ├── services/                    # Auth services
│   │   │   └── types/                       # Auth types
│   │   ├── broker/                          # Broker specific features
│   │   │   ├── components/                  # Broker components
│   │   │   │   ├── kanban/                  # Kanban board components (Screaming Architecture)
│   │   │   │   │   ├── index.tsx            # Kanban exports
│   │   │   │   │   ├── kanban-accessibility.tsx  # Accessibility features
│   │   │   │   │   ├── kanban-board.tsx     # Main board component
│   │   │   │   │   ├── kanban-card.tsx      # Card component
│   │   │   │   │   ├── kanban-column.tsx    # Column component
│   │   │   │   │   └── kanban-header.tsx    # Header component
│   │   │   │   ├── auction-card-base.tsx    # Base auction card
│   │   │   │   ├── available-auction-card.tsx  # Available auction card
│   │   │   │   ├── bid-placement-form.tsx   # Bid placement form
│   │   │   │   ├── broker-auction-tabs.tsx  # Auction tabs
│   │   │   │   ├── client-card.tsx          # Client card
│   │   │   │   ├── commission-payment-modal.tsx  # Commission modal
│   │   │   │   ├── completed-auction-card.tsx  # Completed auction card
│   │   │   │   ├── confirmed-auction-card.tsx  # Confirmed auction card
│   │   │   │   ├── lost-auction-card.tsx    # Lost auction card
│   │   │   │   ├── participating-auction-card.tsx  # Participating auction card
│   │   │   │   └── won-auction-card.tsx     # Won auction card
│   │   │   ├── hooks/                       # Broker hooks
│   │   │   ├── services/                    # Broker services
│   │   │   │   ├── billing-address.service.ts  # Billing address service
│   │   │   │   ├── broker-profile.service.ts   # Broker profile service
│   │   │   │   └── identity-verification.service.ts  # Identity verification
│   │   │   ├── types/                       # Broker types
│   │   │   │   └── auction.ts               # Auction types
│   │   │   └── utils/                       # Broker utilities
│   │   │       └── auction-card-utils.ts    # Auction card utilities
│   │   ├── policies/                        # Policy domain logic
│   │   │   ├── actions/                     # Server actions
│   │   │   ├── components/                  # Policy components
│   │   │   ├── hooks/                       # Policy hooks
│   │   │   ├── services/                    # Policy services
│   │   │   └── types/                       # Policy types
│   │   ├── settings/                        # Settings domain logic
│   │   │   ├── components/                  # Settings components
│   │   │   ├── hooks/                       # Settings hooks
│   │   │   ├── services/                    # Settings services
│   │   │   └── types/                       # Settings types
│   │   └── support/                         # Support domain logic
│   │       ├── components/                  # Support components
│   │       ├── hooks/                       # Support hooks
│   │       ├── services/                    # Support services
│   │       └── types/                       # Support types
│   ├── lib/                                 # Pure infrastructure layer
│   │   ├── supabase/                        # Supabase configuration
│   │   │   ├── client.ts                    # Client-side Supabase (auth only)
│   │   │   └── server.ts                    # Server-side Supabase
│   │   ├── db.ts                            # Database client singleton
│   │   ├── r2.ts                            # Cloudflare R2 configuration
│   │   ├── use-media-query.ts               # Media query hook
│   │   └── utils.ts                         # Generic utilities only
│   ├── middleware.ts                        # Next.js middleware
│   └── styles/                              # Global styles
│       └── globals.css                      # Global CSS
├── supabase/                                # Supabase configuration
│   ├── apply-policies.ts                    # RLS policy application
│   ├── functions/                           # Database functions
│   │   ├── 01_get_user_role.sql             # User role function
│   │   └── 02_sync_user_profile.sql         # Profile sync function
│   └── policies/                            # Row Level Security policies
│       └── User/                            # User-related policies
├── tailwind.config.ts                       # Tailwind CSS configuration
└── tsconfig.json                            # TypeScript configuration
```
