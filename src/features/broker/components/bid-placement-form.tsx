"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Loader2, Euro, Shield, Clock, Users, TrendingDown, CheckCircle } from "lucide-react"
import { AvailableAuction } from "../types/auction"
import { cn } from "@/lib/utils"

// Zod schema for bid form validation
const bidFormSchema = z.object({
  bidAmount: z
    .string()
    .min(1, "El monto de la oferta es requerido")
    .refine((val) => !isNaN(Number(val)) && Number(val) > 0, "Debe ser un número válido mayor a 0")
    .refine((val) => Number(val) >= 50, "El monto mínimo es €50")
    .refine((val) => Number(val) <= 10000, "El monto máximo es €10,000"),
  coverageImprovements: z
    .string()
    .min(10, "Describe al menos 10 caracteres sobre las mejoras de cobertura")
    .max(500, "Máximo 500 caracteres"),
  proposedPremium: z
    .string()
    .min(1, "La prima propuesta es requerida")
    .refine((val) => !isNaN(Number(val)) && Number(val) > 0, "Debe ser un número válido mayor a 0")
    .refine((val) => Number(val) >= 100, "La prima mínima es €100")
    .refine((val) => Number(val) <= 50000, "La prima máxima es €50,000"),
  message: z
    .string()
    .max(1000, "Máximo 1000 caracteres")
    .optional(),
})

type BidFormData = z.infer<typeof bidFormSchema>

interface BidPlacementFormProps {
  auction: AvailableAuction
  onSubmit: (data: BidFormData) => Promise<void>
  onCancel: () => void
  isSubmitting?: boolean
}

export function BidPlacementForm({ 
  auction, 
  onSubmit, 
  onCancel, 
  isSubmitting = false 
}: BidPlacementFormProps) {
  const [submitError, setSubmitError] = useState<string | null>(null)

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    watch,
    reset
  } = useForm<BidFormData>({
    resolver: zodResolver(bidFormSchema),
    mode: "onChange",
    defaultValues: {
      bidAmount: "",
      coverageImprovements: "",
      proposedPremium: "",
      message: "",
    }
  })

  const bidAmount = watch("bidAmount")
  const proposedPremium = watch("proposedPremium")

  const handleFormSubmit = async (data: BidFormData) => {
    try {
      setSubmitError(null)
      await onSubmit(data)
      reset()
    } catch (error) {
      setSubmitError("Error al enviar la oferta. Inténtalo nuevamente.")
    }
  }

  // Calculate savings for the client
  const currentPremium = auction.currentPremium
  const savings = proposedPremium && currentPremium 
    ? currentPremium - Number(proposedPremium)
    : 0

  return (
    <div className="space-y-6">
      {/* Auction Details Header */}
      <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-lg flex items-center gap-2">
                <Shield className="w-5 h-5 text-blue-600" />
                {auction.assetType === "CAR" ? "Seguro de Coche" : "Seguro de Moto"} - {auction.coverageType}
              </CardTitle>
              <CardDescription className="text-sm text-gray-600">
                Cliente: {auction.clientName} • Póliza actual: €{auction.currentPremium}/año
              </CardDescription>
            </div>
            <div className="text-right">
              <div className="flex items-center gap-2 text-sm text-gray-500">
                <Clock className="w-4 h-4" />
                {auction.timeRemaining}
              </div>
              <div className="flex items-center gap-2 text-sm text-blue-600">
                <Users className="w-4 h-4" />
                {auction.participantCount} participantes
              </div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Current Competition */}
      {auction.participantCount > 0 && (
        <Card className="border-amber-200 bg-amber-50">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-amber-800">
              Competencia Actual
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Participantes:</span>
                <div className="font-semibold text-blue-600">{auction.participantCount}</div>
              </div>
              <div>
                <span className="text-gray-600">Tu posición:</span>
                <Badge variant="outline" className="ml-2">
                  Nuevo participante
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* AuctionBid Form */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Hacer una Oferta</CardTitle>
          <CardDescription>
            Completa los detalles de tu propuesta para participar en esta subasta
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
            {/* AuctionBid Amount */}
            <div className="space-y-2">
              <Label htmlFor="bidAmount" className="text-sm font-medium">
                Monto de la Oferta *
              </Label>
              <div className="relative">
                <Euro className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="bidAmount"
                  type="number"
                  step="0.01"
                  placeholder="0.00"
                  className={cn(
                    "pl-10",
                    errors.bidAmount ? "border-red-300 focus:border-red-500 focus:ring-red-500" : ""
                  )}
                  {...register("bidAmount")}
                />
              </div>
              {errors.bidAmount && (
                <p className="text-sm text-red-600">{errors.bidAmount.message}</p>
              )}
              <p className="text-xs text-gray-500">
                Monto que cobrarás por gestionar esta póliza
              </p>
            </div>

            {/* Proposed Premium */}
            <div className="space-y-2">
              <Label htmlFor="proposedPremium" className="text-sm font-medium">
                Prima Propuesta para el Cliente *
              </Label>
              <div className="relative">
                <Euro className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="proposedPremium"
                  type="number"
                  step="0.01"
                  placeholder="0.00"
                  className={cn(
                    "pl-10",
                    errors.proposedPremium ? "border-red-300 focus:border-red-500 focus:ring-red-500" : ""
                  )}
                  {...register("proposedPremium")}
                />
              </div>
              {errors.proposedPremium && (
                <p className="text-sm text-red-600">{errors.proposedPremium.message}</p>
              )}
              {savings > 0 && (
                <div className="flex items-center gap-2 text-sm text-green-600">
                  <TrendingDown className="w-4 h-4" />
                  Ahorro para el cliente: €{savings.toFixed(2)}/año
                </div>
              )}
              {savings < 0 && (
                <div className="flex items-center gap-2 text-sm text-red-600">
                  <TrendingDown className="w-4 h-4" />
                  Aumento: €{Math.abs(savings).toFixed(2)}/año
                </div>
              )}
            </div>

            {/* Coverage Improvements */}
            <div className="space-y-2">
              <Label htmlFor="coverageImprovements" className="text-sm font-medium">
                Mejoras de Cobertura *
              </Label>
              <Textarea
                id="coverageImprovements"
                placeholder="Describe las mejoras específicas que ofreces sobre la póliza actual..."
                className={cn(
                  "min-h-[100px] resize-none",
                  errors.coverageImprovements ? "border-red-300 focus:border-red-500 focus:ring-red-500" : ""
                )}
                {...register("coverageImprovements")}
              />
              {errors.coverageImprovements && (
                <p className="text-sm text-red-600">{errors.coverageImprovements.message}</p>
              )}
              <p className="text-xs text-gray-500">
                Explica qué ventajas adicionales ofreces vs. la póliza actual
              </p>
            </div>

            {/* Optional Message */}
            <div className="space-y-2">
              <Label htmlFor="message" className="text-sm font-medium">
                Mensaje Adicional (Opcional)
              </Label>
              <Textarea
                id="message"
                placeholder="Mensaje personalizado para el cliente..."
                className="min-h-[80px] resize-none"
                {...register("message")}
              />
              {errors.message && (
                <p className="text-sm text-red-600">{errors.message.message}</p>
              )}
            </div>

            {/* Submit Error */}
            {submitError && (
              <Alert className="border-red-200 bg-red-50">
                <AlertDescription className="text-red-800">
                  {submitError}
                </AlertDescription>
              </Alert>
            )}

            <Separator />

            {/* Form Actions */}
            <div className="flex items-center justify-between">
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isSubmitting}
              >
                Cancelar
              </Button>
              
              <div className="flex items-center gap-3">
                {isValid && bidAmount && proposedPremium && (
                  <div className="flex items-center gap-2 text-sm text-green-600">
                    <CheckCircle className="w-4 h-4" />
                    Lista para enviar
                  </div>
                )}
                
                <Button
                  type="submit"
                  disabled={!isValid || isSubmitting}
                  className="min-w-[140px]"
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Enviando...
                    </>
                  ) : (
                    <>
                      <Shield className="w-4 h-4 mr-2" />
                      Enviar Oferta
                    </>
                  )}
                </Button>
              </div>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Tips */}
      <Card className="bg-blue-50 border-blue-200">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium text-blue-800">
            💡 Consejos para una Oferta Exitosa
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• Ofrece un precio competitivo pero rentable</li>
            <li>• Destaca mejoras específicas de cobertura</li>
            <li>• Personaliza tu mensaje para conectar con el cliente</li>
            <li>• Responde rápidamente - las subastas terminan pronto</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  )
}