"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card";
import { translatePartyRole } from "@/features/policies/utils/translations";
import { PartyRole } from "@prisma/client";
import { Edit, Trash2 } from "lucide-react";

interface InsuredPartiesAccordionProps {
  fields: any[];
  onEdit: (index: number) => void;
  onRemove: (index: number) => void;
}

export function InsuredPartiesAccordion({
  fields,
  onEdit,
  onRemove,
}: InsuredPartiesAccordionProps) {
  return (
    <div className="space-y-4">
      {(!fields || fields.length === 0) && (
        <p className="text-sm text-muted-foreground">
          Aún no se han añadido partes. Haga clic en 'Añadir Parte' para comenzar.
        </p>
      )}
      {fields?.map((field, index) => (
        <Card key={field.id}>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between p-6">
            <div className="flex-1">
              <p className="text-base font-semibold">
                {field.fullName}
                <span className="text-sm font-normal text-muted-foreground ml-2">({field.dni})</span>
              </p>
              <div className="text-sm text-muted-foreground mt-1">
                <span className="font-medium">Roles:</span> {(field.roles as PartyRole[])?.map((role) => translatePartyRole(role)).join(", ")}
              </div>
            </div>
            <div className="flex items-center space-x-2 self-end mt-2 sm:mt-0">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => onEdit(index)}
              >
                <Edit className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => onRemove(index)}
                className="text-red-500 hover:text-red-600"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
}