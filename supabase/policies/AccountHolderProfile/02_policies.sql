-- Allow the trigger function (SECURITY DEFINER) to insert rows regardless of user RLS
DROP POLICY IF EXISTS "account_holder_profile_select_own" ON public.account_holder_profile;
CREATE POLICY "account_holder_profile_select_own"
ON public.account_holder_profile
FOR SELECT
USING (
  auth.uid() = user_id OR public.get_user_role() = 'ADMIN'
);

DROP POLICY IF EXISTS "account_holder_profile_insert_self" ON public.account_holder_profile;
CREATE POLICY "account_holder_profile_insert_self"
ON public.account_holder_profile
FOR INSERT
WITH CHECK (
  auth.uid() = user_id OR public.get_user_role() = 'ADMIN'
);

DROP POLICY IF EXISTS "account_holder_profile_update_own" ON public.account_holder_profile;
CREATE POLICY "account_holder_profile_update_own"
ON public.account_holder_profile
FOR UPDATE
USING (
  auth.uid() = user_id OR public.get_user_role() = 'ADMIN'
)
WITH CHECK (
  auth.uid() = user_id OR public.get_user_role() = 'ADMIN'
);