/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  typescript: {
    // !! WARN !!
    // Dangerously allow production builds to successfully complete even if
    // your project has type errors.
    // This is controlled by the NODE_ENV environment variable.
    // !! WARN !!
    ignoreBuildErrors: true,
  },
  // swcMinify is now enabled by default in Next.js 15
  // i18n is now configured in app/[locale] directories in App Router
  eslint: {
    ignoreDuringBuilds: true,
  },
};

module.exports = nextConfig;
