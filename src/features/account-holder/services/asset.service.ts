import { db } from "@/lib/db";
import type { Asset, Prisma } from "@prisma/client";

export const AssetService = {
  // Create a new asset
  async create(data: Prisma.AssetCreateInput): Promise<Asset> {
    return db.asset.create({
      data,
      include: {
        policies: true,
        insuredParties: true
      }
    });
  },

  // Get a asset by ID
  async getById(id: string): Promise<Asset | null> {
    return db.asset.findUnique({
      where: { id },
      include: {
        policies: true,
        insuredParties: true
      }
    });
  },

  // Get a asset by license plate
  async getByLicensePlate(licensePlate: string): Promise<Asset | null> {
    return db.asset.findUnique({
      where: { licensePlate },
      include: {
        policies: true
      }
    });
  },

  // Get a asset by chassis number
  async getByChassisNumber(chassisNumber: string): Promise<Asset | null> {
    return db.asset.findUnique({
      where: { chassisNumber },
      include: {
        policies: true
      }
    });
  },

  // Update a asset
  async update(id: string, data: Prisma.AssetUpdateInput): Promise<Asset> {
    return db.asset.update({
      where: { id },
      data,
      include: {
        policies: true,
        insuredParties: true
      }
    });
  },

  // Delete a asset
  async delete(id: string): Promise<Asset> {
    return db.asset.delete({
      where: { id }
    });
  },

  // List assets with pagination and optional filters
  async list(params: {
    skip?: number;
    take?: number;
    where?: Prisma.AssetWhereInput;
    orderBy?: Prisma.AssetOrderByWithRelationInput;
  }): Promise<{ assets: Asset[]; total: number }> {
    const { skip = 0, take = 10, where, orderBy } = params;
    
    const [assets, total] = await Promise.all([
      db.asset.findMany({
        skip,
        take,
        where,
        orderBy,
        include: {
          policies: true,
          insuredParties: true
        }
      }),
      db.asset.count({ where })
    ]);

    return { assets, total };
  }
};