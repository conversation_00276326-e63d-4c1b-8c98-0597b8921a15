# 8.3 Integration Guidelines for New Features

**Pre-Development Checklist:**
1. **Acknowledge Current Architecture:** Confirm understanding of 100% Screaming Architecture compliance
2. **Identify Domain AND Role:** State both business domain and target user role
3. **Locate Relevant Files:** Explore current structure using established patterns
4. **Consult Schema:** Review `prisma/schema.prisma` for data model understanding
5. **Declare Intent:** Formulate clear plan maintaining role-based organization

**File Placement Rules:**
- **Role-specific pages:** `src/app/{role}/{domain}/`
- **Domain features:** `src/features/{domain}/`
- **Shared components:** `src/components/shared/`
- **Infrastructure:** `src/lib/`
