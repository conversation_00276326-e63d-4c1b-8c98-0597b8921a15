// Define a clear type for user roles for type safety.
export type Role = 'ADMIN' | 'BROKER' | 'ACCOUNT_HOLDER';

// Define the structure for each role's configuration.
export interface RoleConfig {
  home: string; // The default page for a user with this role.
  allowedPaths: string[]; // The URL prefixes this role can access.
}

// Master configuration for role-based access control.
// This object is the single source of truth for all role-based rules.
export const ROLES_CONFIG: Record<Role, RoleConfig> = {
  ADMIN: {
    home: '/admin/policies',
    allowedPaths: ['/admin'],
  },
  BROKER: {
    home: '/broker/policies',
    allowedPaths: ['/broker'],
  },
  ACCOUNT_HOLDER: {
    home: '/account-holder/policies',
    allowedPaths: ['/account-holder', '/support'],
  },
};