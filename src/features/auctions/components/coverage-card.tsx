"use client";

import { cn } from "@/lib/utils";

interface CoverageCardProps {
  title: string;
  limit: number | null;
  description: string;
  className?: string;
}

export function CoverageCard({ title, limit, description, className }: CoverageCardProps) {
  const formatLimit = (limit: number | null) => {
    if (limit === null) return "";
    return `€${limit.toLocaleString('es-ES')}`;
  };

  return (
    <div className={cn(
      "bg-white border border-gray-200 rounded-xl p-4 shadow-sm hover:shadow-md transition-shadow",
      className
    )}>
      <div className="flex items-center gap-3 mb-2">
        <div className="w-2 h-2 bg-[#3AE386] rounded-full flex-shrink-0 mt-1"></div>
        <h4 className="font-semibold text-base text-gray-900 leading-tight">{title}</h4>
      </div>
      {limit !== null && (
        <p className="text-gray-600 pl-4 mb-2">
          <span className="text-sm text-gray-500">Límite de cobertura: </span>
          <span className="text-sm font-semibold text-gray-900">{formatLimit(limit)}</span>
        </p>
      )}
      <div className="text-sm text-gray-600 leading-relaxed pl-4">
        {description}
      </div>
    </div>
  );
}