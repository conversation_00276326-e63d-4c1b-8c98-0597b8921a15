import { 
  Mail, 
  MessageCircle, 
  FileText, 
  TrendingUp, 
  Shield, 
  Users, 
  Code, 
  AlertTriangle,
  Settings,
  Database
} from "lucide-react";
import { SupportConfig } from "../types/support.types";

export const supportConfig: SupportConfig = {
  contacts: [
    // Account Holder Contacts
    {
      id: "email-account-holder",
      title: "Correo Electrónico",
      description: "Envíanos un correo y te responderemos en 24 horas.",
      icon: Mail,
      buttonText: "<EMAIL>",
      buttonHref: "mailto:<EMAIL>",
      buttonVariant: "default",
      roles: ["ACCOUNT_HOLDER"]
    },
    {
      id: "whatsapp-account-holder",
      title: "WhatsApp",
      description: "Chatea con nosotros de lunes a viernes de 9:00 a 18:00.",
      icon: MessageCircle,
      buttonText: "Chatear Ahora",
      buttonHref: "https://wa.me/***********",
      buttonVariant: "default",
      target: "_blank",
      roles: ["ACCOUNT_HOLDER"]
    },
    
    // Broker Contacts
    {
      id: "email-broker",
      title: "Correo Electrónico",
      description: "Soporte técnico especializado para brokers.",
      icon: Mail,
      buttonText: "<EMAIL>",
      buttonHref: "mailto:<EMAIL>",
      buttonVariant: "default",
      roles: ["BROKER"]
    },
    {
      id: "whatsapp-broker",
      title: "WhatsApp Brokers",
      description: "Canal directo para brokers certificados.",
      icon: MessageCircle,
      buttonText: "Chatear Ahora",
      buttonHref: "https://wa.me/***********",
      buttonVariant: "default",
      target: "_blank",
      roles: ["BROKER"]
    },
    
    // Admin Contacts
    {
      id: "critical-admin",
      title: "Soporte Crítico",
      description: "Canal prioritario para incidencias críticas del sistema.",
      icon: AlertTriangle,
      buttonText: "<EMAIL>",
      buttonHref: "mailto:<EMAIL>",
      buttonVariant: "destructive",
      roles: ["ADMIN"]
    },
    {
      id: "tech-admin",
      title: "Soporte Técnico",
      description: "Consultas técnicas y de desarrollo.",
      icon: Mail,
      buttonText: "<EMAIL>",
      buttonHref: "mailto:<EMAIL>",
      buttonVariant: "default",
      roles: ["ADMIN"]
    },
    {
      id: "slack-admin",
      title: "Slack Interno",
      description: "Canal de comunicación interno del equipo.",
      icon: MessageCircle,
      buttonText: "Abrir Slack",
      buttonHref: "https://zeeguros.slack.com",
      buttonVariant: "default",
      target: "_blank",
      roles: ["ADMIN"]
    }
  ],
  
  resources: [
    // Admin Resources
    {
      id: "system-monitoring-admin",
      title: "Monitoreo Sistema",
      description: "Dashboards y métricas del sistema.",
      icon: Database,
      buttonText: "Ver Métricas",
      buttonVariant: "outline",
      roles: ["ADMIN"]
    },
    {
      id: "api-docs-admin",
      title: "Documentación API",
      description: "Documentación técnica completa de la API.",
      icon: Code,
      buttonText: "Ver Docs",
      buttonVariant: "outline",
      roles: ["ADMIN"]
    },
    {
      id: "configuration-admin",
      title: "Configuración",
      description: "Guías de configuración del sistema.",
      icon: Settings,
      buttonText: "Ver Guías",
      buttonVariant: "outline",
      roles: ["ADMIN"]
    },
    {
      id: "security-admin",
      title: "Seguridad",
      description: "Protocolos de seguridad y auditoria.",
      icon: Shield,
      buttonText: "Ver Protocolos",
      buttonVariant: "outline",
      roles: ["ADMIN"]
    },
    {
      id: "system-logs-admin",
      title: "Logs del Sistema",
      description: "Acceso a logs y auditoría del sistema.",
      icon: FileText,
      buttonText: "Ver Logs",
      buttonVariant: "outline",
      roles: ["ADMIN"]
    },
    {
      id: "database-admin",
      title: "Base de Datos",
      description: "Herramientas de gestión de base de datos.",
      icon: Database,
      buttonText: "Acceder DB",
      buttonVariant: "outline",
      roles: ["ADMIN"]
    }
  ]
};