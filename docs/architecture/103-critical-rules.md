# 10.3 Critical Rules

- **Server-Side Database Access Only:** NEVER use client-side Supabase for database operations. All CRUD operations must use server-side API routes to prevent anonymous API key exposure.
- **Spanish UI Text:** All user-facing text (labels, buttons, titles, tooltips, placeholders, error messages) must be written in Spanish.
- **English Code:** All code (variable names, function names, class names, components, database models, schema, file names, comments) must be written in English.
- **Role-Based Organization:** Place domain-specific components, hooks, or services in `src/features/{domainName}/` and role-specific UI pages in `src/app/{role}/{domain}/`.
- **Single Prisma Client:** Always import the singleton Prisma client from `src/lib/db.ts`. Never create new instances.
- **TypeScript Compliance:** All code must compile without TypeScript errors. Do not rely on `ignoreBuildErrors` flag.
- **Security First:** Never expose sensitive data in logs, error messages, or client-side code. All authentication must be validated server-side.
