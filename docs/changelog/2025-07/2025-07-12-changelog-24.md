# Changelog - 2025-07-12

## Architectural Refactoring: In Progress

This changelog documents the ongoing execution of the `strategic-architectural-plan.md`.

### Completed Tasks:

*   **[Phase 0] Foundational Structure**:
    *   Created all role-based directories (`/features/auth`, `/features/broker`, etc.).
    *   Established the secure data flow pattern by refactoring existing `auth` services and actions into the new structure.
*   **[Phase 1] `auth` Domain Refactoring**:
    *   Fully refactored the `auth` domain, moving all related components, hooks, actions, and services into `src/features/auth`.
    *   Updated all import paths across the application to reflect the new `auth` structure.
*   **[Phase 1] Hook Consolidation**:
    *   Consolidated the redundant `use-mobile` hooks into a single, robust `useMediaQuery` hook.
    *   Updated all components to use the new hook.
*   **[Phase 1] Service Relocation**:
    *   Moved `identity-verification.service.ts` to `src/features/broker/services`.
    *   Moved `account-holder-profile.service.ts` to `src/features/account-holder/services`.
*   **[Phase 1] `admin` Domain Refactoring**:
    *   Moved the policy submission review components to `src/features/admin/components/policy-submission-review`.
    *   Moved and renamed the `useNewPolicy` hook to `src/features/admin/hooks/usePolicySubmissionReview.ts`.
    *   Updated all related imports.
*   **[Phase 1] Full Domain Review**:
    *   **Admin**: [DONE] Completed a full review of all components, hooks, services, and utils to ensure alignment.
    *   **Broker**: [DONE] Completed a full review of all components, hooks, services, and utils to ensure alignment.
    *   **Account Holder**: [DONE] Completed a full review of all components, hooks, services, and utils to ensure alignment.
    *   **Auctions**: [DONE] Completed a full review of all components, hooks, services, and utils to ensure alignment.
    *   **Broker Domain**: Completed a full review of the `broker` domain.
        *   Refactored the Broker CRM page (`src/app/(dashboard)/broker/crm/page.tsx`) to use the centralized `useUser` hook, removing redundant data-fetching logic.
        *   Created `CompletedAuctionCard` to display completed auction results.
        *   Created `BrokerAuctionTabs` to encapsulate the tabbed view logic.
    *   **Shared & UI Components**:
        *   Relocated `ZBadge` from `ui` to `src/components/shared/z-badge.tsx` as a globally shared component.
        *   Relocated `ZPriceIndicator` from `ui` to `src/components/shared/price-indicator.tsx`.
        *   Split the monolithic `z-card.tsx` into a reusable base component (`src/components/shared/z-card.tsx`) and multiple domain-specific variants:
            *   `AuctionCard` -> `src/features/auctions/components/auction-card.tsx`
            *   `ClientCard` -> `src/features/broker/components/client-card.tsx`
            *   `PolicyCard` -> `src/features/policies/components/policy-card.tsx`
            *   `MetricCard` -> `src/components/shared/metric-card.tsx`
    *   **Domain-Specific Logic Relocation**:
        *   **Auctions**: Moved `z-countdown.tsx` from `ui` to `src/features/auctions/components/countdown.tsx`.
        *   **Policies**: Moved policy-related server actions (`getPolicyDetails.ts`, `policies.ts`) from `src/lib/actions` to `src/features/policies/actions/`. Corrected multiple TypeScript errors related to outdated `Policy` model assumptions, ensuring all function signatures and service calls are now type-safe and aligned with the current `prisma/schema.prisma`.
    *   **Code Cleanup**:
        *   Removed the obsolete `src/components/policies` directory.
        *   Removed original `z-card.tsx`, `z-badge.tsx`, `z-countdown.tsx`, and `z-price-indicator.tsx` files from `src/components/ui`.
        *   Removed original action files from `src/lib/actions`.
        *   Corrected all TypeScript errors and updated import paths across the application to reflect the new structure.
    *   **New Policy Feature**:
        *   Relocated all components from `src/app/policies/new-policy/_components` to `src/features/policies/components/new-policy`.
        *   Refactored the `PolicyStepper` to encapsulate all state management and business logic, simplifying the `new-policy` page into a container component.
    *   **Policies Page**:
        *   Relocated the `UserDataProvider` from `src/app/policies/_components` to `src/features/auth/components`.
        *   Refactored the `PoliciesPage` by extracting all business logic into a new `PolicyList` component in `src/features/policies/components`.
        *   Simplified the `PoliciesPage` to be a clean container component.
        *   Updated the `PoliciesLayout` to use the new `UserDataProvider` location.
        *   Deleted the obsolete `src/app/policies/_components` directory.

*   **Post-Refactoring Bug Fixes**:
    *   Corrected multiple broken import paths across the application that were introduced during the refactoring.
        *   Fixed imports for `AuthLoginForm`, `ForgotPasswordForm`, `ResetPasswordForm`, and `SignUpForm` in their respective pages.
        *   Fixed imports for `NavUser` and `extractUserName` in the `app-sidebar` and various page layouts (`auctions`, `support`, `settings`).
        *   Fixed imports for `AuthService`, `ROLES_CONFIG`, and the Supabase `createClient` in the `auth` domain's actions and services.
    *   Relocated the domain-specific `TipBox` component from the generic `components/ui` directory to `features/policies/components` to better align with the screaming architecture.
*   **[Phase 1] `policies` Domain Redistribution**:
    *   Dismantled the `features/policies` directory, redistributing its contents into the `account-holder` and `admin` domains to align with the role-based architecture.
    *   **Account Holder**:
        *   Moved all policy creation, updating, and viewing logic to `src/features/account-holder/actions` and `src/features/account-holder/services`.
        *   Moved all related components (`PolicyList`, `PolicyCard`, `PolicyStepper`, etc.) to `src/features/account-holder/components`.
    *   **Admin**:
        *   Moved the `updatePolicyStatus` action to `src/features/admin/actions`.
        *   Created a new `AdminPolicyService` to handle admin-specific policy operations.
    *   **Auctions**:
        *   Corrected the `AuctionService` to use the correct `Auction` model from the Prisma schema.
        *   Added a `createAuctionFromPolicy` function to the `AuctionService`.
    *   **Cleanup**:
        *   Updated all import paths across the application to reflect the new structure.
        *   The `src/features/policies` directory is now empty and ready for deletion.
