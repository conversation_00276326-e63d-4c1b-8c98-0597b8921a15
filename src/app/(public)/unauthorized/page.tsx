import { <PERSON>ada<PERSON> } from "next";
import Link from "next/link";
import { AlertCircle } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

export const metadata: Metadata = {
  title: "Acceso no autorizado | Zeeguros",
  description: "No tienes permiso para acceder a esta página",
};

export default function UnauthorizedPage() {
  return (
    <div className="flex min-h-svh flex-col items-center justify-center gap-6 bg-background p-6 md:p-10">
      <div className="w-full max-w-md text-center">
        <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-destructive/10 mb-4">
          <AlertCircle className="h-6 w-6 text-destructive" />
        </div>
        <h1 className="text-2xl font-bold mb-2">Acceso no autorizado</h1>
        <p className="text-muted-foreground mb-6">
          No tienes permiso para acceder a esta página. Si crees que esto es un error, 
          por favor contacta con soporte.
        </p>
        <div className="flex flex-col gap-2">
          <Button asChild>
            <Link href="/dashboard">Ir al Dashboard</Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/login">Volver al inicio de sesión</Link>
          </Button>
        </div>
      </div>
    </div>
  );
}
