/**
 * Brevo Email Service
 * Handles email notifications for auction events using Brevo SMTP API
 */

export interface EmailRecipient {
  email: string;
  name?: string;
}

export interface EmailTemplate {
  subject: string;
  htmlContent: string;
  textContent?: string;
}

export interface AuctionClosedEmailData {
  auctionId: string;
  policyNumber: string;
  assetDisplayName: string;
  totalBids: number;
  closedAt: string;
  accountHolderName: string;
}

export interface NewBidEmailData {
  auctionId: string;
  policyNumber: string;
  assetDisplayName: string;
  bidAmount: number;
  brokerName: string;
  timeRemaining: string;
  accountHolderName: string;
}

export interface AdminNotificationData {
  auctionId: string;
  policyNumber: string;
  policyId: string;
  assetDisplayName: string;
  eventType: 'AUCTION_CREATED' | 'AUCTION_CLOSED' | 'WINNERS_SELECTED' | 'SYSTEM_ERROR';
  executionTimestamp: string;
  processingDuration?: number;
  totalBids: number;
  participantCount: number;
  winnerSelectionCriteria?: string;
  errorDetails?: string;
  troubleshootingInfo?: string;
  systemMetrics?: Record<string, any>;
}

export interface WinnerNotificationData {
  auctionId: string;
  policyNumber: string;
  assetDisplayName: string;
  winningPosition: number; // 1, 2, or 3
  winningBidAmount: number;
  brokerName: string;
  accountHolderContact: {
    name: string;
    email: string;
    phone?: string;
  };
}

export interface AccountHolderWinnersData {
  auctionId: string;
  policyNumber: string;
  assetDisplayName: string;
  accountHolderName: string;
  winners: Array<{
    position: number;
    brokerName: string;
    bidAmount: number;
    contact: {
      name: string;
      email: string;
      phone?: string;
    };
  }>;
}

export class BrevoEmailService {
  private apiKey: string;
  private senderEmail: string;
  private senderName: string;
  private baseUrl = 'https://api.brevo.com/v3';

  constructor() {
    this.apiKey = process.env.BREVO_API_KEY || '';
    this.senderEmail = process.env.BREVO_SENDER_EMAIL || '<EMAIL>';
    this.senderName = process.env.BREVO_SENDER_NAME || 'Zeeguros';

    if (!this.apiKey) {
      throw new Error('BREVO_API_KEY environment variable is required');
    }
  }

  /**
   * Send email using Brevo SMTP API
   */
  private async sendEmail(
    to: EmailRecipient[],
    template: EmailTemplate,
    replyTo?: EmailRecipient
  ): Promise<{ success: boolean; messageId?: string; error?: string }> {
    try {
      const payload = {
        sender: {
          name: this.senderName,
          email: this.senderEmail,
        },
        to: to.map(recipient => ({
          email: recipient.email,
          name: recipient.name || recipient.email,
        })),
        subject: template.subject,
        htmlContent: template.htmlContent,
        textContent: template.textContent || this.stripHtml(template.htmlContent),
        replyTo: replyTo ? {
          email: replyTo.email,
          name: replyTo.name || replyTo.email,
        } : undefined,
      };

      const response = await fetch(`${this.baseUrl}/smtp/email`, {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'api-key': this.apiKey,
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Brevo API error: ${response.status} - ${errorData.message || 'Unknown error'}`);
      }

      const result = await response.json();
      return {
        success: true,
        messageId: result.messageId,
      };
    } catch (error) {
      console.error('Failed to send email via Brevo:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Strip HTML tags for text content
   */
  private stripHtml(html: string): string {
    return html.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();
  }

  /**
   * Send auction closed notification to account holder
   */
  async sendAuctionClosedNotification(
    recipient: EmailRecipient,
    data: AuctionClosedEmailData
  ): Promise<{ success: boolean; messageId?: string; error?: string }> {
    const template: EmailTemplate = {
      subject: `Tu subasta ha finalizado - ${data.policyNumber}`,
      htmlContent: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background-color: #3ea050; color: white; padding: 20px; text-align: center;">
            <h1 style="margin: 0;">Subasta Finalizada</h1>
          </div>
          
          <div style="padding: 30px; background-color: #f9f9f9;">
            <h2 style="color: #333;">Hola ${data.accountHolderName},</h2>
            
            <p style="font-size: 16px; line-height: 1.6; color: #555;">
              Tu subasta para la póliza <strong>${data.policyNumber}</strong> ha finalizado.
            </p>
            
            <div style="background-color: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="color: #3ea050; margin-top: 0;">Detalles de la Subasta</h3>
              <ul style="list-style: none; padding: 0;">
                <li style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Activo:</strong> ${data.assetDisplayName}</li>
                <li style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Total de ofertas:</strong> ${data.totalBids}</li>
                <li style="padding: 8px 0;"><strong>Finalizada el:</strong> ${data.closedAt}</li>
              </ul>
            </div>
            
            <p style="font-size: 16px; line-height: 1.6; color: #555;">
              ${data.totalBids > 0 
                ? 'Puedes revisar las ofertas recibidas y seleccionar hasta 3 ganadores en tu panel de control.'
                : 'No se recibieron ofertas para esta subasta. Puedes crear una nueva subasta si lo deseas.'
              }
            </p>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${process.env.NEXT_PUBLIC_SITE_URL}/account-holder/auctions/${data.auctionId}" 
                 style="background-color: #3ea050; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
                Ver Subasta
              </a>
            </div>
            
            <p style="font-size: 14px; color: #777; margin-top: 30px;">
              Si tienes alguna pregunta, no dudes en <NAME_EMAIL>
            </p>
          </div>
          
          <div style="background-color: #333; color: white; padding: 20px; text-align: center; font-size: 12px;">
            <p style="margin: 0;">© 2025 Zeeguros. Todos los derechos reservados.</p>
          </div>
        </div>
      `,
    };

    return this.sendEmail([recipient], template);
  }

  /**
   * Send new bid notification to account holder
   */
  async sendNewBidNotification(
    recipient: EmailRecipient,
    data: NewBidEmailData
  ): Promise<{ success: boolean; messageId?: string; error?: string }> {
    const template: EmailTemplate = {
      subject: `Nueva oferta recibida - ${data.policyNumber}`,
      htmlContent: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background-color: #3ea050; color: white; padding: 20px; text-align: center;">
            <h1 style="margin: 0;">Nueva Oferta Recibida</h1>
          </div>
          
          <div style="padding: 30px; background-color: #f9f9f9;">
            <h2 style="color: #333;">Hola ${data.accountHolderName},</h2>
            
            <p style="font-size: 16px; line-height: 1.6; color: #555;">
              Has recibido una nueva oferta para tu subasta de la póliza <strong>${data.policyNumber}</strong>.
            </p>
            
            <div style="background-color: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="color: #3ea050; margin-top: 0;">Detalles de la Oferta</h3>
              <ul style="list-style: none; padding: 0;">
                <li style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Activo:</strong> ${data.assetDisplayName}</li>
                <li style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Broker:</strong> ${data.brokerName}</li>
                <li style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Oferta:</strong> €${data.bidAmount.toFixed(2)}</li>
                <li style="padding: 8px 0;"><strong>Tiempo restante:</strong> ${data.timeRemaining}</li>
              </ul>
            </div>
            
            <p style="font-size: 16px; line-height: 1.6; color: #555;">
              Puedes revisar todas las ofertas y hacer seguimiento de tu subasta en tu panel de control.
            </p>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${process.env.NEXT_PUBLIC_SITE_URL}/account-holder/auctions/${data.auctionId}" 
                 style="background-color: #3ea050; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
                Ver Subasta
              </a>
            </div>
            
            <p style="font-size: 14px; color: #777; margin-top: 30px;">
              Si tienes alguna pregunta, no dudes en <NAME_EMAIL>
            </p>
          </div>
          
          <div style="background-color: #333; color: white; padding: 20px; text-align: center; font-size: 12px;">
            <p style="margin: 0;">© 2025 Zeeguros. Todos los derechos reservados.</p>
          </div>
        </div>
      `,
    };

    return this.sendEmail([recipient], template);
  }

  /**
   * Send admin auction notification
   */
  async sendAdminAuctionNotification(
    data: AdminNotificationData
  ): Promise<{ success: boolean; messageId?: string; error?: string }> {
    const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
    const adminDashboardUrl = process.env.ADMIN_DASHBOARD_URL || 'https://admin.zeeguros.com';

    const eventTypeLabels = {
      'AUCTION_CREATED': 'AUCTION_CREATED',
      'AUCTION_CLOSED': 'AUCTION_CLOSED',
      'WINNERS_SELECTED': 'WINNERS_SELECTED',
      'SYSTEM_ERROR': 'SYSTEM_ERROR'
    };

    const template: EmailTemplate = {
      subject: `[ZEEGUROS-ADMIN] [${eventTypeLabels[data.eventType]}] - Auction ${data.auctionId} - Policy ${data.policyNumber}`,
      htmlContent: `
        <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 800px; margin: 0 auto; background-color: #f8f9fa;">
          <div style="background-color: #dc3545; color: white; padding: 20px; text-align: center;">
            <h1 style="margin: 0; font-size: 24px;">ZEEGUROS ADMIN NOTIFICATION</h1>
            <h2 style="margin: 10px 0 0 0; font-size: 18px; font-weight: normal;">${eventTypeLabels[data.eventType]}</h2>
          </div>

          <div style="background-color: white; padding: 30px; border-left: 4px solid #dc3545;">
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 30px;">
              <div>
                <h3 style="color: #495057; margin: 0 0 10px 0; font-size: 16px;">Auction Details</h3>
                <p style="margin: 5px 0; color: #6c757d;"><strong>Auction ID:</strong> ${data.auctionId}</p>
                <p style="margin: 5px 0; color: #6c757d;"><strong>Policy ID:</strong> ${data.policyId}</p>
                <p style="margin: 5px 0; color: #6c757d;"><strong>Policy Number:</strong> ${data.policyNumber}</p>
                <p style="margin: 5px 0; color: #6c757d;"><strong>Asset:</strong> ${data.assetDisplayName}</p>
              </div>
              <div>
                <h3 style="color: #495057; margin: 0 0 10px 0; font-size: 16px;">System Metrics</h3>
                <p style="margin: 5px 0; color: #6c757d;"><strong>Total Bids:</strong> ${data.totalBids}</p>
                <p style="margin: 5px 0; color: #6c757d;"><strong>Participants:</strong> ${data.participantCount}</p>
                <p style="margin: 5px 0; color: #6c757d;"><strong>Timestamp:</strong> ${data.executionTimestamp}</p>
                ${data.processingDuration ? `<p style="margin: 5px 0; color: #6c757d;"><strong>Duration:</strong> ${data.processingDuration}ms</p>` : ''}
              </div>
            </div>

            ${data.winnerSelectionCriteria ? `
              <div style="background-color: #e9ecef; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
                <h3 style="color: #495057; margin: 0 0 10px 0; font-size: 16px;">Winner Selection Criteria</h3>
                <p style="margin: 0; color: #6c757d;">${data.winnerSelectionCriteria}</p>
              </div>
            ` : ''}

            ${data.errorDetails ? `
              <div style="background-color: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
                <h3 style="color: #721c24; margin: 0 0 10px 0; font-size: 16px;">Error Details</h3>
                <p style="margin: 0; color: #721c24;">${data.errorDetails}</p>
                ${data.troubleshootingInfo ? `<p style="margin: 10px 0 0 0; color: #721c24;"><strong>Troubleshooting:</strong> ${data.troubleshootingInfo}</p>` : ''}
              </div>
            ` : ''}

            <div style="text-align: center; margin: 30px 0;">
              <a href="${adminDashboardUrl}/auctions/${data.auctionId}"
                 style="background-color: #dc3545; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">
                View in Admin Dashboard
              </a>
            </div>

            ${data.systemMetrics ? `
              <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 20px;">
                <h3 style="color: #495057; margin: 0 0 10px 0; font-size: 16px;">System Metrics</h3>
                <pre style="background-color: #e9ecef; padding: 10px; border-radius: 3px; font-size: 12px; overflow-x: auto;">${JSON.stringify(data.systemMetrics, null, 2)}</pre>
              </div>
            ` : ''}
          </div>

          <div style="background-color: #6c757d; color: white; padding: 15px; text-align: center; font-size: 12px;">
            <p style="margin: 0;">Zeeguros Admin System - Automated Notification</p>
            <p style="margin: 5px 0 0 0;">Generated at ${new Date().toISOString()}</p>
          </div>
        </div>
      `,
    };

    return this.sendEmail([{ email: adminEmail, name: 'Zeeguros Admin' }], template);
  }

  /**
   * Send winner notification to broker
   */
  async sendWinnerNotification(
    recipient: EmailRecipient,
    data: WinnerNotificationData
  ): Promise<{ success: boolean; messageId?: string; error?: string }> {
    const positionLabels = { 1: '1er', 2: '2do', 3: '3er' };
    const positionLabel = positionLabels[data.winningPosition as keyof typeof positionLabels] || `${data.winningPosition}º`;

    const template: EmailTemplate = {
      subject: `¡Felicidades! Has ganado una subasta - ${data.policyNumber}`,
      htmlContent: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background-color: #3ea050; color: white; padding: 20px; text-align: center;">
            <h1 style="margin: 0;">¡Felicidades!</h1>
            <h2 style="margin: 10px 0 0 0; font-weight: normal;">Has sido seleccionado como ganador</h2>
          </div>

          <div style="background-color: white; padding: 30px;">
            <div style="background-color: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 5px; margin-bottom: 25px; text-align: center;">
              <h3 style="color: #155724; margin: 0 0 10px 0;">Posición: ${positionLabel} Lugar</h3>
              <p style="color: #155724; margin: 0; font-size: 18px; font-weight: bold;">Oferta Ganadora: €${data.winningBidAmount.toFixed(2)}</p>
            </div>

            <h3 style="color: #333; margin: 0 0 15px 0;">Detalles de la Subasta</h3>
            <p style="margin: 5px 0; color: #555;"><strong>Póliza:</strong> ${data.policyNumber}</p>
            <p style="margin: 5px 0; color: #555;"><strong>Activo:</strong> ${data.assetDisplayName}</p>
            <p style="margin: 5px 0; color: #555;"><strong>Tu posición:</strong> ${positionLabel} lugar</p>

            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 25px 0;">
              <h3 style="color: #333; margin: 0 0 15px 0;">Información de Contacto del Titular</h3>
              <p style="margin: 5px 0; color: #555;"><strong>Nombre:</strong> ${data.accountHolderContact.name}</p>
              <p style="margin: 5px 0; color: #555;"><strong>Email:</strong> ${data.accountHolderContact.email}</p>
              ${data.accountHolderContact.phone ? `<p style="margin: 5px 0; color: #555;"><strong>Teléfono:</strong> ${data.accountHolderContact.phone}</p>` : ''}
            </div>

            <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 25px 0;">
              <h4 style="color: #856404; margin: 0 0 10px 0;">Próximos Pasos</h4>
              <ul style="color: #856404; margin: 0; padding-left: 20px;">
                <li>El titular de la póliza se pondrá en contacto contigo directamente</li>
                <li>Prepara tu propuesta detallada de cobertura</li>
                <li>Mantén disponible toda la documentación necesaria</li>
                <li>Responde de manera oportuna a las consultas del cliente</li>
              </ul>
            </div>

            <div style="text-align: center; margin: 30px 0;">
              <a href="${process.env.NEXT_PUBLIC_SITE_URL}/broker/auctions/${data.auctionId}"
                 style="background-color: #3ea050; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
                Ver Detalles de la Subasta
              </a>
            </div>

            <p style="font-size: 14px; color: #777; margin-top: 30px;">
              Si tienes alguna pregunta, no dudes en <NAME_EMAIL>
            </p>
          </div>

          <div style="background-color: #333; color: white; padding: 20px; text-align: center; font-size: 12px;">
            <p style="margin: 0;">© 2025 Zeeguros. Todos los derechos reservados.</p>
          </div>
        </div>
      `,
    };

    return this.sendEmail([recipient], template);
  }

  /**
   * Send winners summary notification to account holder
   */
  async sendAccountHolderWinnersNotification(
    recipient: EmailRecipient,
    data: AccountHolderWinnersData
  ): Promise<{ success: boolean; messageId?: string; error?: string }> {
    const winnersTable = data.winners.map((winner, index) => `
      <tr style="border-bottom: 1px solid #dee2e6;">
        <td style="padding: 15px; text-align: center; font-weight: bold; color: #3ea050;">${winner.position}º</td>
        <td style="padding: 15px;">${winner.brokerName}</td>
        <td style="padding: 15px; text-align: right; font-weight: bold;">€${winner.bidAmount.toFixed(2)}</td>
        <td style="padding: 15px;">
          <div style="font-size: 14px;">
            <div><strong>${winner.contact.name}</strong></div>
            <div style="color: #6c757d;">${winner.contact.email}</div>
            ${winner.contact.phone ? `<div style="color: #6c757d;">${winner.contact.phone}</div>` : ''}
          </div>
        </td>
      </tr>
    `).join('');

    const template: EmailTemplate = {
      subject: `Ganadores seleccionados para tu subasta - ${data.policyNumber}`,
      htmlContent: `
        <div style="font-family: Arial, sans-serif; max-width: 700px; margin: 0 auto;">
          <div style="background-color: #3ea050; color: white; padding: 20px; text-align: center;">
            <h1 style="margin: 0;">Ganadores Seleccionados</h1>
            <h2 style="margin: 10px 0 0 0; font-weight: normal;">Tu subasta ha finalizado</h2>
          </div>

          <div style="background-color: white; padding: 30px;">
            <p style="font-size: 16px; color: #333; margin-bottom: 25px;">
              Hola ${data.accountHolderName},
            </p>

            <p style="font-size: 16px; line-height: 1.6; color: #555; margin-bottom: 25px;">
              El sistema ha seleccionado automáticamente los 3 mejores ganadores para tu subasta de <strong>${data.assetDisplayName}</strong>
              (Póliza: ${data.policyNumber}) basándose en el precio más bajo y la cobertura más completa.
            </p>

            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 25px 0;">
              <h3 style="color: #333; margin: 0 0 20px 0; text-align: center;">Ganadores Seleccionados</h3>
              <table style="width: 100%; border-collapse: collapse; background-color: white; border-radius: 5px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <thead>
                  <tr style="background-color: #3ea050; color: white;">
                    <th style="padding: 15px; text-align: center;">Posición</th>
                    <th style="padding: 15px; text-align: left;">Broker</th>
                    <th style="padding: 15px; text-align: right;">Oferta</th>
                    <th style="padding: 15px; text-align: left;">Contacto</th>
                  </tr>
                </thead>
                <tbody>
                  ${winnersTable}
                </tbody>
              </table>
            </div>

            <div style="background-color: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 5px; margin: 25px 0;">
              <h4 style="color: #155724; margin: 0 0 15px 0;">¿Qué sigue ahora?</h4>
              <ul style="color: #155724; margin: 0; padding-left: 20px; line-height: 1.6;">
                <li>Los ganadores han sido notificados automáticamente</li>
                <li>Puedes contactar directamente con cualquiera de los 3 ganadores</li>
                <li>Compara sus propuestas y elige la que mejor se adapte a tus necesidades</li>
                <li>Los datos de contacto están disponibles arriba para tu comodidad</li>
              </ul>
            </div>

            <div style="text-align: center; margin: 30px 0;">
              <a href="${process.env.NEXT_PUBLIC_SITE_URL}/account-holder/auctions/${data.auctionId}"
                 style="background-color: #3ea050; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
                Ver Detalles Completos
              </a>
            </div>

            <p style="font-size: 14px; color: #777; margin-top: 30px;">
              Si tienes alguna pregunta sobre el proceso o necesitas ayuda, no dudes en <NAME_EMAIL>
            </p>
          </div>

          <div style="background-color: #333; color: white; padding: 20px; text-align: center; font-size: 12px;">
            <p style="margin: 0;">© 2025 Zeeguros. Todos los derechos reservados.</p>
          </div>
        </div>
      `,
    };

    return this.sendEmail([recipient], template);
  }

  /**
   * Test email connectivity
   */
  async testConnection(): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await fetch(`${this.baseUrl}/account`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'api-key': this.apiKey,
        },
      });

      if (!response.ok) {
        throw new Error(`Brevo API test failed: ${response.status}`);
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }
}

// Export singleton instance
export const brevoEmailService = new BrevoEmailService();
