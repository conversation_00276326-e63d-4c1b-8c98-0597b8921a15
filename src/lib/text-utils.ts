/**
 * Normalizes text for accent-insensitive search
 * Converts accented characters to their base form and lowercases the text
 * Example: "<PERSON>" -> "ma<PERSON>", "<PERSON>" -> "jose"
 */
export function normalizeForSearch(text: string): string {
  return text
    .toLowerCase()
    .normalize('NFD') // Decompose accented characters
    .replace(/[\u0300-\u036f]/g, '') // Remove accent marks
    .trim();
}

/**
 * Checks if a search term matches text in an accent-insensitive way
 * Both the search term and target text are normalized before comparison
 */
export function matchesSearch(text: string, searchTerm: string): boolean {
  const normalizedText = normalizeForSearch(text);
  const normalizedSearch = normalizeForSearch(searchTerm);
  return normalizedText.includes(normalizedSearch);
}