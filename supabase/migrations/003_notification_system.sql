-- Comprehensive Auction Notification System
-- Handles all auction lifecycle notifications including:
-- - Auction creation notifications (admin)
-- - Auction closure notifications (admin + account holder + automatic winner selection)
-- - System error notifications and comprehensive logging

-- Create notification log table to track all notification attempts
CREATE TABLE IF NOT EXISTS public.notification_log (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    auction_id UUID REFERENCES public.auction(id),
    notification_type TEXT NOT NULL,
    status TEXT NOT NULL CHECK (status IN ('pending', 'sent', 'failed')),
    recipient_email TEXT,
    recipient_type TEXT CHECK (recipient_type IN ('account_holder', 'broker', 'admin', 'system')),
    message_id TEXT,
    error_message TEXT,
    processing_duration INTEGER,
    notification_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    sent_at TIMESTAMP WITH TIME ZONE
);

-- Add constraint for notification types
ALTER TABLE public.notification_log 
ADD CONSTRAINT notification_log_notification_type_check 
CHECK (notification_type IN (
    'auction_closed',
    'new_bid_received', 
    'auction_winner_selected',
    'auction_started',
    'admin_auction_created',
    'admin_auction_closed',
    'admin_winners_selected',
    'admin_system_error',
    'winner_notification',
    'account_holder_winners'
));

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_notification_log_auction_id ON public.notification_log(auction_id);
CREATE INDEX IF NOT EXISTS idx_notification_log_status ON public.notification_log(status);
CREATE INDEX IF NOT EXISTS idx_notification_log_created_at ON public.notification_log(created_at);
CREATE INDEX IF NOT EXISTS idx_notification_log_recipient_email ON public.notification_log(recipient_email);
CREATE INDEX IF NOT EXISTS idx_notification_log_recipient_type ON public.notification_log(recipient_type);
CREATE INDEX IF NOT EXISTS idx_notification_log_notification_type ON public.notification_log(notification_type);
CREATE INDEX IF NOT EXISTS idx_notification_log_sent_at ON public.notification_log(sent_at);

-- Enable RLS on notification log
ALTER TABLE public.notification_log ENABLE ROW LEVEL SECURITY;

-- RLS policies for different user types
CREATE POLICY "Admin can view all notification logs" ON public.notification_log
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public."User" u 
            WHERE u.id = auth.uid() AND u.role = 'ADMIN'
        )
    );

CREATE POLICY "Account holders can view their notifications" ON public.notification_log
    FOR SELECT USING (
        recipient_type = 'account_holder' AND 
        recipient_email = (
            SELECT u.email FROM public."User" u 
            WHERE u.id = auth.uid()
        )
    );

CREATE POLICY "Brokers can view their notifications" ON public.notification_log
    FOR SELECT USING (
        recipient_type = 'broker' AND 
        recipient_email = (
            SELECT u.email FROM public."User" u 
            WHERE u.id = auth.uid()
        )
    );

-- Function to log notifications (for use by Edge Functions)
CREATE OR REPLACE FUNCTION public.log_notification(
    p_auction_id UUID,
    p_notification_type TEXT,
    p_recipient_email TEXT,
    p_recipient_type TEXT,
    p_status TEXT DEFAULT 'pending',
    p_message_id TEXT DEFAULT NULL,
    p_error_message TEXT DEFAULT NULL,
    p_processing_duration INTEGER DEFAULT NULL,
    p_notification_data JSONB DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    log_id UUID;
BEGIN
    INSERT INTO public.notification_log (
        auction_id,
        notification_type,
        recipient_email,
        recipient_type,
        status,
        message_id,
        error_message,
        processing_duration,
        notification_data,
        created_at,
        sent_at
    ) VALUES (
        p_auction_id,
        p_notification_type,
        p_recipient_email,
        p_recipient_type,
        p_status,
        p_message_id,
        p_error_message,
        p_processing_duration,
        p_notification_data,
        NOW(),
        CASE WHEN p_status = 'sent' THEN NOW() ELSE NULL END
    )
    RETURNING id INTO log_id;
    
    RETURN log_id;
END;
$$;

-- Function to get notification statistics (for admin dashboard)
CREATE OR REPLACE FUNCTION public.get_notification_stats(
    p_start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW() - INTERVAL '30 days',
    p_end_date TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
RETURNS TABLE(
    notification_type TEXT,
    recipient_type TEXT,
    total_sent INTEGER,
    total_failed INTEGER,
    success_rate NUMERIC
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        nl.notification_type,
        nl.recipient_type,
        COUNT(CASE WHEN nl.status = 'sent' THEN 1 END)::INTEGER as total_sent,
        COUNT(CASE WHEN nl.status = 'failed' THEN 1 END)::INTEGER as total_failed,
        CASE 
            WHEN COUNT(*) > 0 THEN 
                ROUND((COUNT(CASE WHEN nl.status = 'sent' THEN 1 END)::NUMERIC / COUNT(*)::NUMERIC) * 100, 2)
            ELSE 0
        END as success_rate
    FROM public.notification_log nl
    WHERE nl.created_at >= p_start_date 
      AND nl.created_at <= p_end_date
    GROUP BY nl.notification_type, nl.recipient_type
    ORDER BY nl.notification_type, nl.recipient_type;
END;
$$;

-- Note: Configuration is handled by Edge Function secrets, no database config needed

-- Comprehensive function to handle all auction notification events
CREATE OR REPLACE FUNCTION public.trigger_comprehensive_auction_notifications()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    closed_auction_ids UUID[];
    created_auction_ids UUID[];
    edge_function_url TEXT;
    request_payload JSONB;
    response_id BIGINT;
    service_role_key TEXT;
BEGIN
    -- Edge Function URL (hardcoded since Edge Function has all necessary secrets)
    edge_function_url := 'https://etzouzmoluegjbfikshb.supabase.co/functions/v1/sendAuctionNotification';
    -- Service role key is not needed - Edge Function handles authentication internally

    -- Find auctions that were created in the last 5 minutes (for admin notifications)
    SELECT ARRAY_AGG(id) INTO created_auction_ids
    FROM public.auction
    WHERE status = 'OPEN'::auction_state
      AND created_at >= NOW() - INTERVAL '5 minutes'
      AND created_at <= NOW()
      -- Only notify for auctions that haven't been notified yet
      AND NOT EXISTS (
          SELECT 1 FROM public.notification_log nl
          WHERE nl.auction_id = auction.id
            AND nl.notification_type = 'admin_auction_created'
            AND nl.status = 'sent'
      );

    -- Find auctions that were closed in the last 5 minutes
    SELECT ARRAY_AGG(id) INTO closed_auction_ids
    FROM public.auction
    WHERE status = 'CLOSED'::auction_state
      AND updated_at >= NOW() - INTERVAL '5 minutes'
      AND updated_at <= NOW()
      -- Only notify for auctions that haven't been notified yet
      AND NOT EXISTS (
          SELECT 1 FROM public.notification_log nl
          WHERE nl.auction_id = auction.id
            AND nl.notification_type = 'auction_closed'
            AND nl.status = 'sent'
      );

    -- Process auction creation notifications (admin only)
    IF created_auction_ids IS NOT NULL AND array_length(created_auction_ids, 1) > 0 THEN
        -- Log the notification attempt
        INSERT INTO public.notification_log (
            auction_id,
            notification_type,
            recipient_email,
            recipient_type,
            status,
            notification_data
        )
        SELECT
            unnest(created_auction_ids),
            'admin_auction_created',
            '<EMAIL>',
            'admin',
            'pending',
            jsonb_build_object('trigger_time', NOW(), 'auction_count', array_length(created_auction_ids, 1))
        ;

        -- Prepare the request payload for auction creation
        request_payload := jsonb_build_object(
            'type', 'auction_created',
            'auctionIds', to_jsonb(created_auction_ids)
        );

        -- Make HTTP request to Edge Function
        BEGIN
            SELECT net.http_post(
                url := edge_function_url,
                headers := jsonb_build_object(
                    'Content-Type', 'application/json'
                ),
                body := request_payload
            ) INTO response_id;

            -- Update notification log with success
            UPDATE public.notification_log
            SET status = 'sent', sent_at = NOW()
            WHERE auction_id = ANY(created_auction_ids)
              AND notification_type = 'admin_auction_created'
              AND status = 'pending';

        EXCEPTION WHEN OTHERS THEN
            -- Update notification log with failure
            UPDATE public.notification_log
            SET status = 'failed', error_message = SQLERRM
            WHERE auction_id = ANY(created_auction_ids)
              AND notification_type = 'admin_auction_created'
              AND status = 'pending';

            RAISE NOTICE 'Failed to send auction creation notifications: %', SQLERRM;
        END;
    END IF;

    -- Process auction closure notifications (comprehensive)
    IF closed_auction_ids IS NOT NULL AND array_length(closed_auction_ids, 1) > 0 THEN
        -- Log the notification attempt
        INSERT INTO public.notification_log (
            auction_id,
            notification_type,
            recipient_email,
            recipient_type,
            status,
            notification_data
        )
        SELECT
            unnest(closed_auction_ids),
            'auction_closed',
            'system',
            'system',
            'pending',
            jsonb_build_object('trigger_time', NOW(), 'auction_count', array_length(closed_auction_ids, 1))
        ;

        -- Prepare the request payload for auction closure
        request_payload := jsonb_build_object(
            'type', 'auction_closed',
            'auctionIds', to_jsonb(closed_auction_ids)
        );

        -- Make HTTP request to Edge Function
        BEGIN
            SELECT net.http_post(
                url := edge_function_url,
                headers := jsonb_build_object(
                    'Content-Type', 'application/json'
                ),
                body := request_payload
            ) INTO response_id;

            -- Update notification log with success
            UPDATE public.notification_log
            SET status = 'sent', sent_at = NOW()
            WHERE auction_id = ANY(closed_auction_ids)
              AND notification_type = 'auction_closed'
              AND status = 'pending';

        EXCEPTION WHEN OTHERS THEN
            -- Update notification log with failure
            UPDATE public.notification_log
            SET status = 'failed', error_message = SQLERRM
            WHERE auction_id = ANY(closed_auction_ids)
              AND notification_type = 'auction_closed'
              AND status = 'pending';

            RAISE NOTICE 'Failed to send auction closure notifications: %', SQLERRM;
        END;
    END IF;

    -- Log the cron job execution
    IF created_auction_ids IS NOT NULL OR closed_auction_ids IS NOT NULL THEN
        RAISE NOTICE 'Comprehensive auction notifications triggered: % created, % closed',
            COALESCE(array_length(created_auction_ids, 1), 0),
            COALESCE(array_length(closed_auction_ids, 1), 0);
    END IF;

EXCEPTION WHEN OTHERS THEN
    -- Log any unexpected errors
    INSERT INTO public.notification_log (
        notification_type,
        recipient_email,
        recipient_type,
        status,
        error_message,
        notification_data
    ) VALUES (
        'admin_system_error',
        '<EMAIL>',
        'admin',
        'failed',
        'Cron job error: ' || SQLERRM,
        jsonb_build_object('error_time', NOW(), 'function', 'trigger_comprehensive_auction_notifications')
    );

    RAISE NOTICE 'Critical error in auction notification cron job: %', SQLERRM;
END;
$$;

-- Remove any existing notification cron jobs to avoid duplicates
SELECT cron.unschedule('auction-notifications');
SELECT cron.unschedule('comprehensive-auction-notifications');

-- Schedule the comprehensive auction notification cron job
-- Runs every 5 minutes to check for new auction events
SELECT cron.schedule(
    'comprehensive-auction-notifications',    -- Job name
    '*/5 * * * *',                           -- Cron expression: every 5 minutes
    'SELECT public.trigger_comprehensive_auction_notifications();'
);

-- Create a manual trigger function for testing
CREATE OR REPLACE FUNCTION public.trigger_notifications_manual(
    auction_ids UUID[] DEFAULT NULL,
    notification_type TEXT DEFAULT 'auction_closed'
)
RETURNS TABLE(success BOOLEAN, message TEXT, auction_count INTEGER)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    target_auction_ids UUID[];
    edge_function_url TEXT;
    request_payload JSONB;
    response_id BIGINT;
    service_role_key TEXT;
BEGIN
    -- If no auction IDs provided, find recent auctions based on type
    IF auction_ids IS NULL THEN
        IF notification_type = 'auction_created' THEN
            SELECT ARRAY_AGG(id) INTO target_auction_ids
            FROM public.auction
            WHERE status = 'OPEN'::auction_state
              AND created_at >= NOW() - INTERVAL '1 hour'
            LIMIT 10;
        ELSE
            SELECT ARRAY_AGG(id) INTO target_auction_ids
            FROM public.auction
            WHERE status = 'CLOSED'::auction_state
              AND updated_at >= NOW() - INTERVAL '1 hour'
            LIMIT 10;
        END IF;
    ELSE
        target_auction_ids := auction_ids;
    END IF;

    -- Check if we have auctions to process
    IF target_auction_ids IS NULL OR array_length(target_auction_ids, 1) = 0 THEN
        RETURN QUERY SELECT false, 'No auctions found for notification type: ' || notification_type, 0;
        RETURN;
    END IF;

    -- Edge Function URL (hardcoded since Edge Function has all necessary secrets)
    edge_function_url := 'https://etzouzmoluegjbfikshb.supabase.co/functions/v1/sendAuctionNotification';

    -- Prepare request payload
    request_payload := jsonb_build_object(
        'type', notification_type,
        'auctionIds', to_jsonb(target_auction_ids)
    );

    -- Make HTTP request
    BEGIN
        SELECT net.http_post(
            url := edge_function_url,
            headers := jsonb_build_object(
                'Content-Type', 'application/json'
            ),
            body := request_payload
        ) INTO response_id;

        RETURN QUERY SELECT true, 'Notifications triggered successfully', array_length(target_auction_ids, 1);

    EXCEPTION WHEN OTHERS THEN
        RETURN QUERY SELECT false, 'Failed to trigger notifications: ' || SQLERRM, array_length(target_auction_ids, 1);
    END;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.log_notification TO authenticated;
GRANT EXECUTE ON FUNCTION public.log_notification TO service_role;
GRANT EXECUTE ON FUNCTION public.get_notification_stats TO authenticated;
GRANT EXECUTE ON FUNCTION public.trigger_comprehensive_auction_notifications TO authenticated;
GRANT EXECUTE ON FUNCTION public.trigger_notifications_manual TO authenticated;

-- Add comments for documentation
COMMENT ON TABLE public.notification_log IS
'Comprehensive notification log supporting all auction lifecycle notifications including admin notifications, winner selections, and account holder summaries. Tracks all email notifications sent through the Brevo service.';

COMMENT ON FUNCTION public.log_notification IS
'Logs notification attempts with comprehensive metadata for monitoring and debugging. Used by Edge Functions to track email delivery status.';

COMMENT ON FUNCTION public.get_notification_stats IS
'Provides notification statistics for admin dashboard monitoring. Returns success rates and delivery metrics by notification and recipient type.';

COMMENT ON FUNCTION public.trigger_comprehensive_auction_notifications IS
'Automated cron job function that handles comprehensive auction lifecycle notifications including admin notifications for auction creation and closure, automatic winner selection, and system error handling.';

COMMENT ON FUNCTION public.trigger_notifications_manual IS
'Manual trigger function for testing comprehensive auction notifications. Supports both auction creation and closure notification types.';

-- Log the successful creation of the comprehensive notification system
DO $$
BEGIN
    RAISE NOTICE 'Comprehensive auction notification system created successfully. Includes notification logging, cron job scheduling, and manual testing functions.';
END $$;
