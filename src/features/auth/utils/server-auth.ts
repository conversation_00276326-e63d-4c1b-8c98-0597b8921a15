import { createServerAdminClient } from '@/lib/supabase/server-admin'
import { createServerClient, type CookieOptions } from '@supabase/ssr'
import { Role } from '@prisma/client'
import { redirect } from 'next/navigation'
import { NextRequest, NextResponse } from 'next/server'

/**
 * Server-side authentication utilities for secure user validation.
 * These utilities ensure proper type safety and role validation.
 */

export interface AuthenticatedUser {
  id: string
  email: string
  role: Role
  phone?: string
}

/**
 * Gets the current user from the server-side session with proper type safety.
 * Returns null if no user is found or if the user data is invalid.
 */
export async function getCurrentUser(): Promise<AuthenticatedUser | null> {
  try {
    const supabase = await createServerAdminClient()
    
    const { data: { user }, error } = await supabase.auth.getUser()
    
    if (error || !user) {
      return null
    }

    // Validate user metadata with proper type safety
    const userRole = user.user_metadata?.role as string | undefined
    
    if (!userRole || !isValidRole(userRole)) {
      console.error(`Invalid or missing role for user ${user.id}:`, userRole)
      return null
    }

    return {
      id: user.id,
      email: user.email!,
      role: userRole as Role,
      phone: user.phone || undefined
    }
  } catch (error) {
    console.error('Error getting current user:', error)
    return null
  }
}

/**
 * Type guard to validate if a string is a valid Role
 */
export function isValidRole(role: string): role is Role {
  return ['ADMIN', 'BROKER', 'ACCOUNT_HOLDER'].includes(role)
}

/**
 * Requires authentication and returns the user, redirecting to login if not authenticated.
 */
export async function requireAuth(): Promise<AuthenticatedUser> {
  const user = await getCurrentUser()
  
  if (!user) {
    redirect('/login')
  }
  
  return user
}

/**
 * Requires a specific role and returns the user, redirecting appropriately if not authorized.
 */
export async function requireRole(requiredRole: Role): Promise<AuthenticatedUser> {
  const user = await requireAuth()
  
  if (user.role !== requiredRole) {
    // Redirect to appropriate home page based on user's actual role
    const roleHomeMap: Record<Role, string> = {
      ADMIN: '/admin/dashboard',
      BROKER: '/broker/auctions',
      ACCOUNT_HOLDER: '/account-holder/policies'
    }
    
    redirect(roleHomeMap[user.role] || '/unauthorized')
  }
  
  return user
}

/**
 * Checks if the user has access to a specific role's functionality.
 * Returns the user if authorized, null otherwise.
 */
export async function hasRoleAccess(allowedRoles: Role[]): Promise<AuthenticatedUser | null> {
  const user = await getCurrentUser()
  
  if (!user || !allowedRoles.includes(user.role)) {
    return null
  }
  
  return user
}

/**
 * Validates session and returns user info for middleware usage
 */
export async function validateSession(): Promise<{
  user: AuthenticatedUser | null
  isValid: boolean
}> {
  try {
    const user = await getCurrentUser()
    return {
      user,
      isValid: user !== null
    }
  } catch (error) {
    console.error('Session validation error:', error)
    return {
      user: null,
      isValid: false
    }
  }
}
/**
 * Get the current user from the request/response context for middleware
 * This function handles cookie management and user authentication in middleware
 */
export async function getServerUser(request: NextRequest, response: NextResponse) {
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      cookies: {
        get(name: string) {
          return request.cookies.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          request.cookies.set({ name, value, ...options });
          response.cookies.set({ name, value, ...options });
        },
        remove(name: string, options: CookieOptions) {
          request.cookies.set({ name, value: '', ...options });
          response.cookies.set({ name, value: '', ...options });
        },
      },
    }
  );

  const { data: { user } } = await supabase.auth.getUser();
  return user;
}