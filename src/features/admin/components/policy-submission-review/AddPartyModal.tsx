"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import { useForm, useFieldArray, useFormContext } from "react-hook-form";
import { PolicyData } from "@/features/admin/hooks/usePolicySubmissionReview";
import { useState } from "react";
import { PartyRole } from "@prisma/client";
import { PlusCircle } from "lucide-react";

interface AddPartyModalProps {
  onAdd: (data: any) => void;
  onEdit: (data: any) => void;
  editingParty: any;
  editingIndex: number | null;
  setEditingIndex: (index: number | null) => void;
}

export function AddPartyModal({
  onAdd,
  onEdit,
  editingParty,
  editingIndex,
  setEditingIndex,
}: AddPartyModalProps) {
  const [profileSource, setProfileSource] = useState("create_new");
  const [isPolicyHolder, setIsPolicyHolder] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const { register, handleSubmit, reset, watch, setValue } = useForm({
    defaultValues: editingParty || {},
  });

  const onSubmit = (data: any) => {
    const roles = Object.entries(data.roles)
      .filter(([, value]) => value)
      .map(([key]) => key.replace("role_", "").toUpperCase());

    const partyData = { ...data, roles };

    if (editingIndex !== null) {
      onEdit(partyData);
    } else {
      onAdd(partyData);
    }
    reset();
    setEditingIndex(null);
    setIsModalOpen(false);
  };

  const isEditing = editingIndex !== null;

  return (
    <Dialog open={isModalOpen || isEditing} onOpenChange={(open) => {
      if (!open) {
        setEditingIndex(null);
      }
      setIsModalOpen(open);
    }}>
      <DialogTrigger asChild>
        <Button
          onClick={() => setIsModalOpen(true)}
          variant="outline"
          size="sm"
        >
          <PlusCircle className="mr-2 h-4 w-4" />
          Añadir parte
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-lg overflow-y-auto max-h-[90vh]">
        <DialogHeader>
          <DialogTitle>{isEditing ? "Editar Parte" : "Añadir Nueva Parte"}</DialogTitle>
          <DialogDescription>
            {isEditing ? "Actualiza los detalles de la parte." : "Añade una nueva parte asegurada a la póliza."}
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="grid gap-4 py-4">
            {!isEditing && (
              <RadioGroup
                defaultValue="create_new"
                onValueChange={(value: string) => setProfileSource(value)}
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="search_existing" id="search_existing" />
                  <Label htmlFor="search_existing">Buscar Perfil Existente</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="create_new" id="create_new" />
                  <Label htmlFor="create_new">Crear Nuevo Perfil</Label>
                </div>
              </RadioGroup>
            )}

            {profileSource === "search_existing" && (
              <div>
                <Label htmlFor="searchProfile">Buscar por nombre o DNI</Label>
                <Input id="searchProfile" />
              </div>
            )}

            {profileSource === "create_new" && (
              <div className="space-y-4">
                <div>
                  <Label htmlFor="fullname">Nombre Completo</Label>
                  <Input id="fullname" {...register("fullName", { required: true })} />
                </div>
                <div>
                  <Label htmlFor="dni">DNI/NIF/NIE</Label>
                  <Input id="dni" {...register("dni", { required: true })} />
                </div>
                <div>
                  <Label htmlFor="gender">Género</Label>
                  <select
                    id="gender"
                    {...register("gender", { required: true })}
                    className="flex h-10 w-full items-center justify-between rounded-md border border-input bg-transparent px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  >
                    <option value="Masculino">Masculino</option>
                    <option value="Femenino">Femenino</option>
                  </select>
                </div>
                <div>
                  <Label>Asignar Roles</Label>
                  <div className="space-y-1">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="role_policy_holder"
                        {...register("roles.policy_holder")}
                        onCheckedChange={(checked: boolean) => setIsPolicyHolder(checked)}
                      />
                      <Label htmlFor="role_policy_holder">Tomador</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="role_owner" {...register("roles.owner")} />
                      <Label htmlFor="role_owner">Propietario</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="role_main_driver" {...register("roles.main_driver")} />
                      <Label htmlFor="role_main_driver">Conductor Principal</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="role_additional_driver"
                        {...register("roles.additional_driver")}
                      />
                      <Label htmlFor="role_additional_driver">
                        Conductor Adicional
                      </Label>
                    </div>
                  </div>
                </div>
                {isPolicyHolder && (
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="address">Dirección</Label>
                      <Input id="address" {...register("address", { required: true })} />
                    </div>
                    <div>
                      <Label htmlFor="postalCode">Código Postal</Label>
                      <Input id="postalCode" {...register("postalCode", { required: true })} />
                    </div>
                    <div>
                      <Label htmlFor="regionName">Provincia / Región</Label>
                      <Input id="regionName" {...register("regionName", { required: true })} />
                    </div>
                    <div>
                      <Label htmlFor="country">País</Label>
                      <Input id="country" {...register("country", { required: true })} />
                    </div>
                  </div>
                )}
                {!isEditing && (
                  <div className="flex items-center space-x-2">
                    <Checkbox id="saveProfile" defaultChecked {...register("saveProfile")} />
                    <Label htmlFor="saveProfile">
                      Guardar este perfil para uso futuro
                    </Label>
                  </div>
                )}
              </div>
            )}
          </div>
          <DialogFooter>
            <Button type="button" variant="ghost" onClick={() => {
              reset();
              setEditingIndex(null);
              setIsModalOpen(false);
            }}>
              Cancelar
            </Button>
            <Button type="submit" variant="default">{isEditing ? "Actualizar parte" : "Guardar parte"}</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}