"use client";
import { Separator } from "@/components/ui/separator";
import { ReactNode, useState, useEffect } from "react";
import { cn } from "@/lib/utils";

interface PageLayoutProps {
  title: string;
  description: string;
  children: ReactNode;
  className?: string;
  isSticky?: boolean;
}

export function PageLayout({ title, description, children, className = "", isSticky = false }: PageLayoutProps) {
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    if (!isSticky) return;

    const handleScroll = () => {
      setIsScrolled(window.scrollY > 0);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, [isSticky]);

  const headerContent = (
    <div>
      <h1 className="text-2xl font-semibold tracking-tight">{title}</h1>
      <p className="text-muted-foreground">{description}</p>
    </div>
  );

  if (isSticky) {
    return (
      <div className={cn("space-y-6", className)}>
        <div
          className={cn(
            "sticky top-0 z-10 bg-white py-4 transition-shadow duration-300",
            isScrolled && "shadow-md"
          )}
        >
          {title && headerContent}
          {title && <Separator className="my-6" />}
        </div>
        <div className="pt-2">{children}</div>
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {title && headerContent}
      {title && <Separator className="my-6" />}
      {children}
    </div>
  );
}