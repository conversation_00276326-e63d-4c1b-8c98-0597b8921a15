/**
 * Working Hours Calculation Utility
 * 
 * Handles auction duration calculations based on business hours:
 * - Monday to Friday: 06:00 - 23:00 (17 hours per day)
 * - Timezone: Europe/Madrid
 * - Weekends and non-working hours are excluded from duration calculations
 */

// Business hours configuration
const WORKING_HOURS = {
  START_HOUR: 6,  // 06:00
  END_HOUR: 24,   // 24:00 (midnight) - working until end of 23:xx hour
  HOURS_PER_DAY: 18, // 24 - 6 = 18 hours (06:00-23:59)
  TIMEZONE: 'Europe/Madrid'
} as const;

/**
 * Checks if a given date falls on a working day (Monday-Friday)
 * DEPRECATED: Use isWorkingDayInMadrid instead
 */
function isWorkingDay(date: Date): boolean {
  return isWorkingDayInMadrid(date);
}

/**
 * Checks if a given time falls within working hours (06:00-23:00)
 * DEPRECATED: Use isWorkingHourInMadrid instead
 */
function isWorkingHour(date: Date): boolean {
  return isWorkingHourInMadrid(date);
}

/**
 * Gets Madrid time information for a given UTC date
 * Returns both the Madrid time components and the corresponding UTC date
 */
function getMadridTimeInfo(utcDate: Date) {
  const madridTimeString = utcDate.toLocaleString('sv-SE', {
    timeZone: 'Europe/Madrid'
  }); // Format: "2025-08-18 10:00:00"
  
  const [datePart, timePart] = madridTimeString.split(' ');
  
  if (!datePart || !timePart) {
    throw new Error(`Invalid Madrid time string format: ${madridTimeString}`);
  }
  
  const [year, month, day] = datePart.split('-').map(Number);
  const [hour, minute, second] = timePart.split(':').map(Number);
  
  if (year === undefined || month === undefined || day === undefined || 
      hour === undefined || minute === undefined) {
    throw new Error(`Failed to parse Madrid time components from: ${madridTimeString}`);
  }
  
  return {
    year,
    month: month - 1, // JavaScript months are 0-based
    day,
    hour,
    minute,
    second: second || 0
  };
}

/**
 * Checks if a UTC date falls on a working day in Madrid timezone
 */
function isWorkingDayInMadrid(utcDate: Date): boolean {
  const madridDay = new Date(utcDate.toLocaleString('en-US', { timeZone: 'Europe/Madrid' })).getDay();
  return madridDay >= 1 && madridDay <= 5; // Monday = 1, Friday = 5
}

/**
 * Checks if a UTC date falls within working hours in Madrid timezone
 */
function isWorkingHourInMadrid(utcDate: Date): boolean {
  const madrid = getMadridTimeInfo(utcDate);
  if (madrid.hour === undefined) {
    return false;
  }
  return madrid.hour >= WORKING_HOURS.START_HOUR && madrid.hour < WORKING_HOURS.END_HOUR;
}

/**
 * Gets the UTC time that corresponds to a specific Madrid time
 */
function getMadridTimeAsUTC(year: number, month: number, day: number, hour: number, minute: number = 0, second: number = 0): Date {
  // Create a date string in Madrid timezone
  const madridDateString = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}T${String(hour).padStart(2, '0')}:${String(minute).padStart(2, '0')}:${String(second).padStart(2, '0')}`;
  
  // Parse as if it were UTC, then adjust for Madrid timezone
  const tempDate = new Date(madridDateString + 'Z');
  
  // Get what this time would be in Madrid
  const madridTime = tempDate.toLocaleString('sv-SE', { timeZone: 'Europe/Madrid' });
  const utcTime = tempDate.toISOString().slice(0, 19).replace('T', ' ');
  
  // Calculate the offset
  const madridMs = new Date(madridTime + 'Z').getTime();
  const utcMs = new Date(utcTime + 'Z').getTime();
  const offset = madridMs - utcMs;
  
  // Apply the reverse offset to get the correct UTC time
  return new Date(tempDate.getTime() - offset);
}

/**
 * Gets the next working day start time in UTC (Monday-Friday at 06:00 Madrid time)
 */
function getNextWorkingDayStartUTC(utcDate: Date): Date {
  const madrid = getMadridTimeInfo(utcDate);
  let nextDay = new Date(utcDate);
  nextDay.setUTCDate(nextDay.getUTCDate() + 1);
  
  // Find the next working day
  while (!isWorkingDayInMadrid(nextDay)) {
    nextDay.setUTCDate(nextDay.getUTCDate() + 1);
  }
  
  // Get the Madrid date components for the next working day
  const nextMadrid = getMadridTimeInfo(nextDay);
  
  if (nextMadrid.year === undefined || nextMadrid.month === undefined || nextMadrid.day === undefined) {
    throw new Error('Failed to get Madrid time components for next working day');
  }
  
  // Return UTC time that corresponds to 06:00 Madrid time on that day
  return getMadridTimeAsUTC(nextMadrid.year, nextMadrid.month, nextMadrid.day, WORKING_HOURS.START_HOUR);
}

/**
 * Gets the working day end time in UTC (23:00 Madrid time) for a given date
 */
function getWorkingDayEndUTC(utcDate: Date): Date {
  const madrid = getMadridTimeInfo(utcDate);
  
  if (madrid.year === undefined || madrid.month === undefined || madrid.day === undefined) {
    throw new Error('Failed to get Madrid time components for working day end');
  }
  
  return getMadridTimeAsUTC(madrid.year, madrid.month, madrid.day, WORKING_HOURS.END_HOUR);
}

/**
 * Gets the working day start time in UTC (06:00 Madrid time) for a given date
 */
function getWorkingDayStartUTC(utcDate: Date): Date {
  const madrid = getMadridTimeInfo(utcDate);
  
  if (madrid.year === undefined || madrid.month === undefined || madrid.day === undefined) {
    throw new Error('Failed to get Madrid time components for working day start');
  }
  
  return getMadridTimeAsUTC(madrid.year, madrid.month, madrid.day, WORKING_HOURS.START_HOUR);
}

/**
 * Calculates remaining working hours in the current day
 */
function getRemainingWorkingHoursToday(utcDate: Date): number {
  if (!isWorkingDayInMadrid(utcDate) || !isWorkingHourInMadrid(utcDate)) {
    return 0;
  }
  
  const endOfDay = getWorkingDayEndUTC(utcDate);
  const remainingMs = endOfDay.getTime() - utcDate.getTime();
  return Math.max(0, remainingMs / (1000 * 60 * 60)); // Convert to hours
}

/**
 * Adds working hours to a start date, accounting for business hours and weekends
 * 
 * @param startDate - The starting date and time
 * @param hoursToAdd - Number of working hours to add
 * @returns The calculated end date considering only working hours
 * 
 * @example
 * // Auction created Friday 23:00 with 48h duration
 * const start = new Date('2024-08-23T23:00:00');
 * const end = addWorkingHours(start, 48);
 * // Result: Tuesday 23:00 (skipping weekend)
 * 
 * @example
 * // Auction created Wednesday 14:00 with 10h duration
 * const start = new Date('2024-08-21T14:00:00');
 * const end = addWorkingHours(start, 10);
 * // Result: Thursday 14:00 (5h remaining Wed + 5h Thursday)
 */
export function addWorkingHours(startDate: Date, hoursToAdd: number): Date {
  if (hoursToAdd <= 0) {
    return new Date(startDate);
  }
  
  let currentDate = new Date(startDate);
  let remainingHours = hoursToAdd;
  
  // Move to next working time if starting outside working hours
  currentDate = moveToNextWorkingTimeUTC(currentDate);
  
  while (remainingHours > 0) {
    // Ensure we're on a working day in Madrid timezone
    while (!isWorkingDayInMadrid(currentDate)) {
      currentDate = getNextWorkingDayStartUTC(currentDate);
    }
    
    // Ensure we're in working hours in Madrid timezone
    const madrid = getMadridTimeInfo(currentDate);
    if (madrid.hour === undefined) {
      throw new Error('Failed to get Madrid hour for current date');
    }
    
    if (madrid.hour < WORKING_HOURS.START_HOUR) {
      currentDate = getWorkingDayStartUTC(currentDate);
    } else if (madrid.hour >= WORKING_HOURS.END_HOUR) {
      // Move to next working day
      currentDate = getNextWorkingDayStartUTC(currentDate);
      continue;
    }
    
    // Calculate how many hours we can add today
    const currentMadridTime = getMadridTimeInfo(currentDate);
    if (currentMadridTime.hour === undefined || currentMadridTime.minute === undefined) {
      throw new Error('Failed to get Madrid time components for current date');
    }
    const currentHour = currentMadridTime.hour + (currentMadridTime.minute / 60);
    const hoursLeftToday = WORKING_HOURS.END_HOUR - currentHour;
    
    if (remainingHours <= hoursLeftToday) {
      // We can finish today - add the remaining hours
      const millisecondsToAdd = remainingHours * 60 * 60 * 1000;
      currentDate = new Date(currentDate.getTime() + millisecondsToAdd);
      remainingHours = 0;
    } else {
      // Use all remaining hours today and continue tomorrow
      remainingHours -= hoursLeftToday;
      currentDate = getNextWorkingDayStartUTC(currentDate);
    }
  }
  
  return currentDate;
}

/**
 * Moves a UTC date to the next working time (working day + working hours in Madrid timezone)
 */
function moveToNextWorkingTimeUTC(utcDate: Date): Date {
  let result = new Date(utcDate);
  
  // If it's a weekend in Madrid, move to Monday
  while (!isWorkingDayInMadrid(result)) {
    result = getNextWorkingDayStartUTC(result);
  }
  
  // Check Madrid time for working hours
  const madrid = getMadridTimeInfo(result);
  
  if (madrid.hour === undefined) {
    throw new Error('Failed to get Madrid hour for result date');
  }
  
  // If it's before working hours, move to start of working day
  if (madrid.hour < WORKING_HOURS.START_HOUR) {
    result = getWorkingDayStartUTC(result);
  }
  // If it's after working hours, move to next working day
  else if (madrid.hour >= WORKING_HOURS.END_HOUR) {
    result = getNextWorkingDayStartUTC(result);
  }
  
  return result;
}

/**
 * Calculates the working hours between two dates
 * 
 * @param startDate - The start date (UTC)
 * @param endDate - The end date (UTC)
 * @returns Number of working hours between the dates
 */
export function calculateWorkingHoursBetween(startDate: Date, endDate: Date): number {
  if (startDate >= endDate) {
    return 0;
  }
  
  let totalHours = 0;
  let currentDate = new Date(startDate);
  
  while (currentDate < endDate) {
    if (isWorkingDayInMadrid(currentDate)) {
      const dayStart = Math.max(currentDate.getTime(), getWorkingDayStartUTC(currentDate).getTime());
      const dayEnd = Math.min(endDate.getTime(), getWorkingDayEndUTC(currentDate).getTime());
      
      if (dayEnd > dayStart) {
        totalHours += (dayEnd - dayStart) / (1000 * 60 * 60);
      }
    }
    
    // Move to next day (add 24 hours)
    currentDate = new Date(currentDate.getTime() + 24 * 60 * 60 * 1000);
  }
  
  return totalHours;
}

/**
 * Calculates when an auction will be closed based on working hours
 * 
 * @param startDate - The auction start date (UTC)
 * @param workingHours - Number of working hours to add
 * @returns The date when the auction will be closed (UTC)
 */
export function calculateWorkingHoursClosedAt(startDate: Date, workingHours: number): Date {
  return addWorkingHours(startDate, workingHours);
}

/**
 * Default auction duration in working hours (48 working hours = ~1 week)
 */
export const DEFAULT_AUCTION_DURATION_HOURS = 48;

/**
 * Export working hours configuration for reference
 */
export const AUCTION_WORKING_HOURS = WORKING_HOURS;