-- Migration: Secure Environment Variables Configuration
-- This migration removes hardcoded credentials and sets up proper environment variable access
-- for database functions to securely retrieve sensitive configuration values.

-- Remove any existing hardcoded service role key entries from app_config
DELETE FROM public.app_config 
WHERE key = 'service_role_key';

-- Update the app_config table structure to prevent storing sensitive data
-- Add a constraint to prevent storing sensitive keys
ALTER TABLE public.app_config 
ADD CONSTRAINT check_no_sensitive_keys 
CHECK (key NOT IN ('service_role_key', 'supabase_service_role_key', 'api_key', 'secret_key', 'password'));

-- Add comment to document the security improvement
COMMENT ON TABLE public.app_config IS 
'Application configuration table. Sensitive credentials should NOT be stored here. Use environment variables instead.';

-- Add comment to the constraint
COMMENT ON CONSTRAINT check_no_sensitive_keys ON public.app_config IS 
'Prevents storing sensitive credentials in the database. Use environment variables for sensitive data.';

-- Update function comments to reflect the new environment variable approach
COMMENT ON FUNCTION public.get_service_role_key() IS 
'Securely retrieves the Supabase service role key from environment variables. Configured via config.toml [db.settings] section.';

-- Log the migration completion
DO $$
BEGIN
    RAISE NOTICE 'Migration 005: Secure environment variables configuration completed successfully';
    RAISE NOTICE 'Hardcoded credentials removed, environment variable access configured';
    RAISE NOTICE 'Database functions now use secure environment variable retrieval';
END $$;