# Zeeguros – Account Holder Policies PRD

## Feature Domain
**Account Holder**

## Feature Name
**Policies**

## Platform Context
Zeeguros (Next.js 15+ monorepo with Supabase & Prisma; screaming architecture, role‑based routing)

---

# 1. Intro Project Analysis and Context

## 1.1 Existing Project Overview

### 1.1.1 Analysis Source
IDE‑based fresh analysis of current Zeeguros project files and user‑provided details.

### 1.1.2 Current Project State
Zeeguros is an existing role-based Next.js 15+ monorepo application using Supabase (PostgreSQL) for data and Prisma as the ORM. The system has distinct areas for different user roles (Account Holder, <PERSON>roker, <PERSON>min) implemented via role-based routing and a “screaming architecture” feature organization.

Currently, the Account Holder portal includes a **“Mis Pólizas”** (My Policies) page where the user can see a list of their insurance policies, with filter and search functionalities. **However, this feature is incomplete:** it displays static mock data instead of real policy data from the database.

The primary purpose of this enhancement is to integrate real backend data into the Account Holder’s policies page and extend the policy data model to support upcoming needs, ensuring other parts of the system (like Broker views) remain unaffected.

## 1.2 Available Documentation Analysis

### 1.2.1 Available Documentation ✅/⚠️
- [x] Tech Stack Documentation — Present (architecture docs describe Next.js, Supabase, Prisma setup)  
- [x] Source Tree / Architecture — Documented (feature‑based structure and role separation outlined)  
- [x] Coding Standards — Partially documented (ESLint / Prettier configs)  
- [x] API Documentation — Basic coverage (server‑side patterns)  
- [x] External API Documentation — Not applicable  
- [ ] UX/UI Guidelines — Limited documentation (relies on design system)  
- [ ] Technical Debt Documentation — Minimal (code comments & backlog)

## 1.3 Enhancement Scope Definition

### 1.3.1 Enhancement Type
- [x] New Feature Addition  
- [x] Major Feature Modification  

### 1.3.2 Enhancement Description
Complete the Account Holder’s policy management features by:
* Replacing placeholder list data with live backend data.
* Introducing a detailed policy view via a slide‑out drawer (reusing the Broker component).
* Extending the data model to support multiple insured parties and generic asset types.
* Linking **RENEW_SOON** policies with the auction system.

### 1.3.3 Impact Assessment
- [x] **Significant Impact** (substantial code & schema changes)

## 1.4 Goals and Background Context

### 1.4.1 Goals
- **Complete “Mis Pólizas” with real data.**  
- Provide a full policy detail drawer view.  
- Support all policy status states (DRAFT, ACTIVE, RENEW_SOON, EXPIRED).  
- **Expand Policy Data Model** (multi‑insured, generic assets, policy‑auction link).  
- Enforce secure, server‑side data operations (Supabase Auth).  
- Maintain compatibility & consistency with existing architecture.

### 1.4.2 Background
The current hard‑coded list misleads users and limits expansion beyond vehicle insurance. Real data, richer models, and renewal‑auction linkage are required to unlock full marketplace value.

## 1.5 Assumptions
- Each Account Holder is linked to policies via user ID.  
- Broker drawer component is modular enough for read‑only reuse.  
- Multi‑insured stored via a new `InsuredParty` table.  
- Generic assets displayed conditionally.  
- No changes to broker/admin workflows beyond passive compatibility.  
- Core stack (Next.js, Supabase, Prisma) remains.

## 1.6 Non‑Goals
- Policy creation/edit by Account Holders.  
- Account Holder renewal or bidding flows.  
- Major UI redesigns.  
- New external services.

## 1.7 Change Log

| Change | Date | Version | Description | Author |
|--------|------|---------|-------------|--------|
| Initial draft created | 2025‑07‑29 | 0.1 | First version of Account Holder Policies PRD | Analyst |

---

# 2. Requirements

## 2.1 Functional Requirements

| ID | Requirement |
|----|-------------|
| **FR1** | Policies list page must fetch and display live data via secure server‑side call. |
| **FR2** | Clicking a policy opens a read‑only detail drawer (reused component). |
| **FR3** | Drawer shows asset, coverage, insurer, premium, insured parties, etc. |
| **FR4** | Support all status values (DRAFT, ACTIVE, RENEW_SOON, EXPIRED). |
| **FR5** | Prisma schema updated: many‑to‑many join table `PolicyInsuredParty`; generic `Asset` plus auxiliary `VehicleDetails`; policy‑auction link; new `AdminProfile` model. |
| **FR6** | Only RENEW_SOON policies link to auctions workflow. |
| **FR7** | All data ops occur server‑side with Supabase Auth. |

## 2.2 Non‑Functional Requirements
- **NFR1**: No performance degradation; use pagination if needed.  
- **NFR2**: Strict security; no client data leaks.  
- **NFR3**: Conform to existing architecture & coding standards.  
- **NFR4**: Re‑use existing UI components.

## 2.3 Compatibility Requirements
- **CR1**: Existing API consumers (broker/admin) remain functional.  
- **CR2**: Migrations preserve existing data.  
- **CR3**: UI/UX consistency (Spanish, Shadcn, Tailwind).  
- **CR4**: Fits current infra & manual deployment.

---

# 3. User Interface Enhancement Goals

## 3.1 Integration with Existing UI
Reuse `PolicyCard` for list entries and `PolicyDetailsDrawer` for details.

## 3.2 Modified / New Screens
- `src/app/account-holder/policies/page.tsx` – list with live data  
- Drawer component in read‑only mode for Account Holders

## 3.3 UI Consistency Checklist
- Language: Spanish  
- Responsive design  
- Component patterns (status badges, buttons)  
- Accessibility (keyboard nav, ARIA)

### 3.4 Policy Card Display Rules by Status
| Policy Status | Primary Label / Data Fields | Action Button |
|--------------|----------------------------|---------------|
| **DRAFT** | • **Primary label:** *Pending verification*<br>• **Description:** “Your policy is going through our verification process. As soon as validation is complete you will have full access to its data.”<br>• **Status badge:** DRAFT | **View details** (disabled) |
| **RENEW_SOON** | • Vehicle: _Make, Model, Trim, Year_ (e.g. **Seat Ibiza 2018**)<br>• Insurer: **Seguros Bilbao**<br>• Product type: **Third‑party + extras**<br>• Policyholder name: **Pepe Gómez Morales**<br>• Policy number: **00002111ESP039003**<br>• Premium: **€845.20 / year**<br>• Coverage count: **12**<br>• Expires: **13 Aug 2025**<br>• Status badge: RENEW_SOON | Dynamic:<br>• **View active auction** – _if policy already linked to an auction_<br>• **Renew** – _if no auction exists; launches a new auction_ |
| **ACTIVE** | Same fields as RENEW_SOON but with current status badge **ACTIVE** and expiry date in the future | **View details** |
| **EXPIRED** | Same fields as ACTIVE but with status badge **EXPIRED** and expiry date in the past | **View details** |
| **REJECTED** | • **Primary label:** *Rejected*<br>• **Description:** “The uploaded document is not a valid policy or cannot be read clearly. Please upload a correct and legible copy to continue.”<br>• **Status badge:** REJECTED | **View details** (disabled) |


### 3.5 Policy Detail Drawer – Data Sections

| Section | Required Fields |
|---------|-----------------|
| **1. Policy Information** | Policy number • Insurer • Product name • Start date • End date • Policy type • Annual premium |
| **2. Insured Parties** | For **each** party: `fullName`, `dni/nie/cif`, `role`, `gender`, `email`, `phone`, `dateOfBirth`.<br>If role = **Policyholder** → also show: `address`, `postalCode`, `regionName`, `country`.<br>If role = **Principal / Secondary Driver** → also show: `drivingLicenceNumber`, `licenceIssuedOn` |
| **3. Asset Information** | For vehicles: `plate`, `firstRegistrationDate`, `make`, `model`, `version`, `manufacturingYear`, `vehicleType`, `fuelType`, `vin`, `power`, `seats`, `usageType`, `garageType`, `annualKm`, `leasing`. For non‑vehicle assets show the generic fields available (`assetType`, `description`, etc.). |
| **4. Coverages** | Coverage title • Limit (numeric) • Description |

---

# 4. Technical Constraints & Integration

## 4.1 Existing Tech Stack
TypeScript, Next.js 15+, Supabase (PostgreSQL), Prisma, Tailwind + Shadcn UI.

## 4.2 Integration Approach
### Database
Add `InsuredParty`, `Asset`, and policy‑auction relations via Prisma migrations.

### API
Secure Next.js API routes (server components or route handlers) using Supabase JWT.

### Frontend
Refactor `PolicyList` to fetch server data. Use state/context to manage drawer.


## 4.3 Code Organization & Standards
Follow screaming architecture; new code under `features/account-holder/`. Strict TypeScript, ESLint, Prettier, updated docs.

## 4.4 Deployment & Operations
Standard manual deployment with Prisma migrations. Monitoring via existing logging tools; rollback plan with DB backups.

---

# 5. Risk Assessment & Mitigation

| Risk | Mitigation |
|------|------------|
| Schema changes break existing queries | Thorough staging tests, rollback scripts |
| Performance issues with large policy sets | Pagination, index tuning |
| Broker drawer reuse causes UI leaks | Remove/disable broker‑only actions in new context |
| Deployment mismatch (DB vs. app) | Coordinated rollout & feature flag |

---

# 6. Epic & Story Structure

## Epic 1 – Account Holder Policies Enhancement
**Epic Goal:** Enable Account Holders to see real, detailed, secure policy data.

### Story 1 – Real Policy Data Retrieval
_User Story, Acceptance Criteria, Integration Verification detailed above._

### Story 2 – Policy Detail Drawer View
_User Story, Acceptance Criteria, Integration Verification detailed above._

### Story 3 – Extend Policy Schema & Integrations
_User Story, Acceptance Criteria, Integration Verification detailed above._

---

_End of Document_