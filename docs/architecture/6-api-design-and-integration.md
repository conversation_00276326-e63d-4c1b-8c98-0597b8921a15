# 6. API Design and Integration

## 6.1 Current API Architecture

**✅ ESTABLISHED PATTERNS:**

**Server-Side Security (MANDATORY):**
- All database operations use server-side API routes (`/api/`)
- Client → Next.js API Route → Server-side Supabase → Database
- No client-side database operations permitted

**API Route Structure:**
```
src/app/api/
├── policies/
│   └── extract/           # AI-powered policy extraction
├── auctions/
│   ├── create/           # Auction creation
│   └── bid/              # Bid placement
└── auth/
    └── session/          # Session management
```

## 6.2 Security Requirements (CRITICAL)

**Database Access Security (HIGHEST PRIORITY):**
- ❌ **NEVER** use `createClient()` from `@/lib/supabase/client` for data operations
- ✅ **ALWAYS** use server-side API routes for all CRUD operations
- ✅ **MANDATORY** pattern: Client → API Route → Server-side Supabase → Database

**Approved Client-Side Usage (LIMITED):**
- Authentication operations only (`supabase.auth.signIn()`, `supabase.auth.signOut()`)
- Reading user session data (`supabase.auth.getUser()`)
- **NEVER** for direct database operations

**API Route Requirements:**
- Server-side authentication validation before database operations
- Zod schema validation for all incoming data
- Proper error handling without exposing internal details
- File operations handled server-side only

## 6.3 Integration Patterns

**External Service Integration:**
- **Google Gemini API:** Policy document processing and data extraction
- **Brevo SMTP:** Transactional email communications
- **Supabase:** Authentication, database, and file storage
- **Cloudflare R2:** File storage and CDN

**For New Integrations:**
- Follow established server-side patterns
- Implement proper error handling and validation
- Maintain security boundaries
- Document integration patterns

---
