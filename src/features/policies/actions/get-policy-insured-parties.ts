"use server";

import { PolicyInsuredPartiesService, type PolicyInsuredPartyData } from "../services/policy-insured-parties.service";

/**
 * Server action to get all insured parties associated with a specific policy
 * This follows the correct relationship: Policy -> PolicyInsuredParty (join table) -> InsuredParty
 */
export async function getPolicyInsuredParties(policyId: string): Promise<PolicyInsuredPartyData[]> {
  try {
    if (!policyId) {
      console.error('Policy ID is required');
      return [];
    }

    const insuredParties = await PolicyInsuredPartiesService.getInsuredPartiesForPolicy(policyId);
    return insuredParties;
  } catch (error) {
    console.error('Error in getPolicyInsuredParties:', error);
    return [];
  }
}

/**
 * Server action to get all insured parties that belong to an account holder
 * This is useful for showing available insured parties when creating/editing policies
 */
export async function getAccountHolderInsuredParties(accountHolderId: string): Promise<PolicyInsuredPartyData[]> {
  try {
    if (!accountHolderId) {
      console.error('Account Holder ID is required');
      return [];
    }

    const insuredParties = await PolicyInsuredPartiesService.getInsuredPartiesForAccountHolder(accountHolderId);
    return insuredParties;
  } catch (error) {
    console.error('Error in getAccountHolderInsuredParties:', error);
    return [];
  }
}

/**
 * Server action to associate an existing insured party with a policy
 */
export async function associateInsuredPartyWithPolicy(policyId: string, insuredPartyId: string): Promise<{ success: boolean; error?: string }> {
  try {
    if (!policyId || !insuredPartyId) {
      return { success: false, error: 'Policy ID and Insured Party ID are required' };
    }

    const success = await PolicyInsuredPartiesService.associateInsuredPartyWithPolicy(policyId, insuredPartyId);
    
    if (success) {
      return { success: true };
    } else {
      return { success: false, error: 'Failed to associate insured party with policy' };
    }
  } catch (error) {
    console.error('Error in associateInsuredPartyWithPolicy:', error);
    return { success: false, error: 'An error occurred while associating insured party with policy' };
  }
}

/**
 * Server action to remove association between an insured party and a policy
 */
export async function removeInsuredPartyFromPolicy(policyId: string, insuredPartyId: string): Promise<{ success: boolean; error?: string }> {
  try {
    if (!policyId || !insuredPartyId) {
      return { success: false, error: 'Policy ID and Insured Party ID are required' };
    }

    const success = await PolicyInsuredPartiesService.removeInsuredPartyFromPolicy(policyId, insuredPartyId);
    
    if (success) {
      return { success: true };
    } else {
      return { success: false, error: 'Failed to remove insured party from policy' };
    }
  } catch (error) {
    console.error('Error in removeInsuredPartyFromPolicy:', error);
    return { success: false, error: 'An error occurred while removing insured party from policy' };
  }
}
