# Story 1.3: ExtendPolicySchemaAndIntegrations

## Status
Done

## Story
**As a** Platform Developer,
**I want** extend the policy schema to support multiple insured parties, generic assets, and link renewal auctions,
**so that** the platform can handle complex policy structures and enable renewal bidding for expiring policies

## Acceptance Criteria
1. A join table `PolicyInsuredParty` enables a **many‑to‑many** relationship between `Policy` and `InsuredParty`.
2. `Asset` model is made generic (fields: id, policyId, assetType enum, description, value, etc.) while **vehicle‑specific fields are migrated into a one‑to‑one auxiliary model `VehicleDetails` linked to `Asset`.**
3. `AdminProfile` model exists with core fields (id, userId, fullName, email, roleLevel) and is referenced where admin user data is required.
4. Policies in RENEW_SOON status link to an `Auction` entity via foreign key (policyId) or join table; existing auctions continue to work.
5. API routes and TypeScript types expose insured parties, assets (with optional vehicleDetails), auction metadata, and admin profile where needed.
6. Row‑Level Security rules restrict access so a user sees only their own policies, related insured parties, assets, and auctions.
7. Migration preserves existing (empty) vehicle fields and succeeds without manual intervention; broker/admin flows remain functional.

## Tasks / Subtasks

- [x] Update Prisma schema (AC:1‑3)
  - [x] Create `PolicyInsuredParty` join table
  - [x] Add generic fields to `Asset` and create `VehicleDetails` auxiliary model
  - [x] Add `AdminProfile` model with fields fullName, email, roleLevel
  - [x] Link Policy <-> Auction via foreign key
- [x] Generate migration & run on staging (AC:6,7)
  - [x] Backup staging database
  - [x] Execute `prisma migrate dev`
  - [x] Seed sample data covering multi‑insured, generic asset, vehicle asset, admin profile
- [x] Regenerate Prisma Client & refactor services (AC:4,5)
  - [x] Adjust queries to include insuredParties (through join), assets & vehicleDetails
  - [x] Update PolicyService mapper and DTOs
- [x] Adjust RLS policies (AC:6)
  - [x] Ensure join tables and new models follow owner chain
- [x] Update TypeScript types & front‑end hooks (AC:5)
  - [x] `PolicyWithRelations` now includes assets.vehicleDetails?
  - [x] Verify admin, broker flows post‑migration

## Dev Notes
- New join model: `PolicyInsuredParty` with composite PK (policyId, insuredPartyId).
- Generic `Asset` fields: id, policyId, assetType (enum: VEHICLE, PROPERTY, PERSON, OTHER), description, value, etc.
- Auxiliary model `VehicleDetails` linked one‑to‑one to `Asset` via assetId.
- New `AdminProfile` mirrors structure of `BrokerProfile`/`AccountHolderProfile`.
- Remember to generate proper @@index(...) for join table and vehicleDetails.assetId.



## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-07-29 | 0.1 | Initial draft | Scrum Master |
