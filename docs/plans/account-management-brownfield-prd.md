# Zeeguros – Authentication & Account Management PRD

## Feature Domain
**Authentication & Account Management**

## Feature Name
**Account Registration, Login & Password Recovery**

## Platform Context
Zeeguros (Next.js 15+ monorepo with Supabase & Prisma; screaming architecture, role‑based routing)

---

# 1. Intro Project Analysis and Context

## 1.1 Existing Project Overview

### 1.1.1 Analysis Source
IDE‑based analysis of current Zeeguros project files, Supabase Auth configurations, and user‑provided enhancement list.

### 1.1.2 Current Project State
Zeeguros is an existing role‑based Next.js 15+ monorepo using Supabase (PostgreSQL) for authentication and data storage, with Prisma as the ORM. It implements role‑based routing and a “screaming architecture” for feature separation.

The account management flows (registration, login, password recovery) are functional but have gaps:
- Duplicate account prevention is not enforced.
- Supabase `auth.users` and `public.user` table fields (phone, display name) can desynchronize.
- Password recovery allows non‑existent emails and lacks post‑change notifications.
- Email templates for sign‑up, login, and recovery lack consistent branding.
- UI feedback and timers for re‑sending confirmation/recovery emails are inconsistent.

The purpose of this enhancement is to address these functional, security, and UX shortcomings while preserving compatibility with existing architecture.

## 1.2 Available Documentation Analysis

### 1.2.1 Available Documentation ✅/⚠️
- [x] Tech Stack Documentation — Present (architecture docs describe Next.js, Supabase, Prisma setup)  
- [x] Source Tree / Architecture — Documented (feature‑based structure and role separation outlined)  
- [x] Coding Standards — Partially documented (ESLint / Prettier configs)  
- [x] API Documentation — Basic coverage (server‑side patterns for Supabase Auth)  
- [ ] UX/UI Guidelines — Limited documentation (relies on design system)  
- [ ] Technical Debt Documentation — Minimal (code comments & backlog)

## 1.3 Enhancement Scope Definition

### 1.3.1 Enhancement Type
- [x] Major Feature Modification  

### 1.3.2 Enhancement Description
Enhance the authentication and account management flows by:
* Validating email and phone uniqueness before registration, with generic error messaging to prevent account enumeration.
* Storing Terms & Conditions acceptance as a boolean in the database.
* Fixing phone and display name sync between `auth.users` and `public.user`.
* Restricting password recovery to existing accounts only and sending a notification after password change.
* Implementing UI feedback (timer & button lock) for re‑sending confirmation/recovery emails.
* Providing branded, consistent email templates for sign‑up, login, and recovery flows.

### 1.3.3 Impact Assessment
- [x] **Significant Impact** (authentication logic, UI feedback, Supabase integration)

## 1.4 Goals and Background Context

### 1.4.1 Goals
- Prevent duplicate accounts by email or phone.
- Record Terms & Conditions acceptance before account creation.
- Synchronize phone and display name across `auth.users` and `public.user`.
- Restrict password recovery to existing emails only.
- Send password change notifications after recovery.
- Improve UX with proper email re‑send timers.
- Use consistent branded email templates.

### 1.4.2 Background
Weak duplicate prevention, inconsistent data sync, and poor UX around recovery flows create both security risks and user frustration. This enhancement closes those gaps while aligning with Zeeguros’ architecture.

## 1.5 Assumptions
- Supabase remains the sole authentication provider.
- Prisma models are the source of truth for profile data.
- UI updates follow existing Shadcn + Tailwind patterns.
- Existing DB schema supports new boolean for Terms acceptance.
- Email templates use existing transactional email provider.

## 1.6 Non‑Goals
- Introducing social logins.
- Major redesign of authentication UI.
- External authentication providers.
- New roles or permissions.

## 1.7 Change Log

| Change | Date | Version | Description | Author |
|--------|------|---------|-------------|--------|
| Initial draft created | 2025‑08‑11 | 0.1 | First version of Account Management PRD | Analyst |

---

# 2. Requirements

## 2.1 Functional Requirements

| ID | Requirement |
|----|-------------|
| **FR1** | On sign‑up, validate email and phone number uniqueness before account creation, with generic error messaging. |
| **FR2** | Store Terms & Conditions acceptance as a boolean in `public.user` table. |
| **FR3** | Fix phone number syncing from sign‑up form to `auth.users` and `public.user`. |
| **FR4** | Fix display name syncing between `auth.users.displayname` and `public.user.display_name`. |
| **FR5** | Restrict password recovery to existing emails only; reject requests for non‑existent emails with generic message. |
| **FR6** | Send email notification after password change via recovery flow. |
| **FR7** | Implement UI timer & button lock (60s) for re‑sending confirmation/recovery emails. |
| **FR8** | Create branded, consistent email templates for sign‑up, login, and recovery. |

## 2.2 Non‑Functional Requirements
- **NFR1**: Maintain Supabase Auth performance; no noticeable latency added.  
- **NFR2**: Avoid user enumeration through error messages.  
- **NFR3**: Preserve coding standards & architecture.  
- **NFR4**: Ensure accessibility (WCAG AA).  

## 2.3 Compatibility Requirements
- **CR1**: Existing user data remains valid.  
- **CR2**: All current roles and routing remain functional.  
- **CR3**: No breaking changes to other user flows.  

---

# 3. User Interface Enhancement Goals

## 3.1 Integration with Existing UI
Use existing Shadcn form components and layouts.

## 3.2 Modified / New Screens
- Sign‑up form (phone, Terms acceptance checkbox)
- Login form (minor adjustments)
- Password recovery form (validation + feedback)

## 3.3 UI Consistency Checklist
- Language: Spanish  
- Responsive design  
- Form field alignment and button styling  
- Accessibility compliance

### 3.4 Sign‑Up Display Rules
- Show Terms acceptance checkbox (required)
- Show phone field with proper formatting & country code handling

### 3.5 Password Recovery Display Rules
- Show generic success message regardless of email existence
- Lock “Resend” button for 60s after sending email

---

# 4. Technical Constraints & Integration

## 4.1 Existing Tech Stack
TypeScript, Next.js 15+, Supabase (PostgreSQL), Prisma, Tailwind + Shadcn UI.

## 4.2 Integration Approach
### Database
Add `termsAccepted` boolean to `public.user` via Prisma migration.

### API
Extend registration and recovery API routes to perform validations and notifications.

### Frontend
Update form components to include Terms acceptance and improved validation logic.

## 4.3 Code Organization & Standards
Follow screaming architecture; new code under `features/auth-management/`. Strict TypeScript, ESLint, Prettier.

## 4.4 Deployment & Operations
Manual deployment with DB migration. Test changes in staging before production.

---

# 5. Risk Assessment & Mitigation

| Risk | Mitigation |
|------|------------|
| Supabase API limits on auth operations | Batch or throttle requests |
| Sync failures between `auth.users` and `public.user` | Implement transaction‑like sync logic |
| Incorrect recovery email restriction logic | Add automated and manual test cases |
| UI timer implementation inconsistencies | Centralize timer logic in a shared hook |

---

# 6. Epic & Story Structure

## Epic 1 – Authentication & Account Management Enhancement
**Epic Goal:** Improve the security, reliability, and user experience of Zeeguros’ account flows.

### Story 1 – Duplicate Account Prevention
_As a new user, I want sign‑up to block existing emails/phones so that I can’t create duplicates._

### Story 2 – Terms Acceptance Recording
_As a new user, I want my Terms acceptance recorded so that legal requirements are met._

### Story 3 – Profile Sync Fix
_As a developer, I want phone & display name synced between tables so that data remains consistent._

### Story 4 – Recovery Flow Restriction & Notification
_As a user, I want password recovery only for existing accounts and a change notification so that my account is secure._

### Story 5 – Branded Email Templates
_As a user, I want consistent branded emails so that communications feel trustworthy._

---

_End of Document_
