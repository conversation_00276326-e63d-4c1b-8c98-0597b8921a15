import React from "react";
import { cn } from "../../lib/utils";

interface PriceIndicatorProps {
  price: number;
  currency?: string;
  period?: string;
  label?: string;
  size?: "sm" | "md" | "lg";
  highlighted?: boolean;
  className?: string;
}

export function PriceIndicator({
  price,
  currency = "€",
  period,
  label,
  size = "md",
  highlighted = false,
  className,
}: PriceIndicatorProps) {
  // Format number with thousands separator
  const formattedPrice = new Intl.NumberFormat("es-ES").format(price);

  return (
    <div
      className={cn(
        "flex flex-col",
        highlighted && "px-3 py-2 bg-primary/10 rounded-md",
        className
      )}
    >
      {label && (
        <div
          className={cn(
            "text-muted-foreground",
            size === "sm" && "text-xs",
            size === "md" && "text-sm",
            size === "lg" && "text-base"
          )}
        >
          {label}
        </div>
      )}
      <div
        className={cn(
          "font-bold flex items-baseline",
          size === "sm" && "text-base",
          size === "md" && "text-xl",
          size === "lg" && "text-3xl"
        )}
      >
        {currency === "€" ? "" : currency}
        {formattedPrice}
        {currency === "€" ? "€" : ""}
        {period && (
          <span
            className={cn(
              "text-muted-foreground font-normal ml-1",
              size === "sm" && "text-xs",
              size === "md" && "text-sm",
              size === "lg" && "text-base"
            )}
          >
            {period}
          </span>
        )}
      </div>
    </div>
  );
}

export default PriceIndicator;