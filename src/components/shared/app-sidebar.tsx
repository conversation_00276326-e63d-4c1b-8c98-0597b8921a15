"use client"

import * as React from "react"
import { useState, useEffect } from "react"
import { usePathname } from "next/navigation"
import {
  FileText,
  Gavel,
  <PERSON>tings,
  HelpCircle,
  BarChart3,
  Briefcase,
} from "lucide-react"
import Image from "next/image"
import Link from "next/link"

import { NavUser } from "@/features/auth/components/nav-user"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar"
import { useUserAuthentication } from "@/features/auth/hooks/useUserAuthentication"
import { extractUserName } from "@/features/auth/utils/user-metadata"
import { cn, getRoleBasedUrl, getRoleDisplayName, type UserRole } from "@/lib/utils"
import { useMediaQuery } from "@/lib/use-media-query"

interface AppSidebarProps extends React.ComponentProps<typeof Sidebar> {
  userRole?: string;
}

// Consolidated user state interface
interface UserState {
  name: string;
  email: string;
  roleDisplay: string;
}

// Navigation item configuration
interface NavigationItem {
  title: string;
  url: string;
  icon: React.ElementType;
}

// Role-specific navigation configurations
const NAVIGATION_CONFIG: Record<UserRole, NavigationItem[]> = {
  ACCOUNT_HOLDER: [
    { title: "Mis Pólizas", url: "policies", icon: FileText },
    { title: "Mis Subastas", url: "auctions", icon: Gavel },
    { title: "Configuración", url: "settings", icon: Settings },
    { title: "Soporte", url: "support", icon: HelpCircle },
  ],
  BROKER: [
    { title: "Subastas", url: "auctions", icon: Gavel },
    { title: "Cartera", url: "portfolio", icon: Briefcase },
    { title: "Configuración", url: "settings", icon: Settings },
    { title: "Soporte", url: "support", icon: HelpCircle },
  ],
  ADMIN: [
    { title: "Mis Pólizas", url: "policies", icon: FileText },
    { title: "Configuración", url: "settings", icon: Settings },
    { title: "Soporte", url: "support", icon: HelpCircle },
  ],
};

// Default user state
const DEFAULT_USER_STATE: UserState = {
  name: "Usuario",
  email: "<EMAIL>",
  roleDisplay: "Plataforma de Seguros",
};

// Utility function to resolve current role
const resolveCurrentRole = (role: string | null): UserRole => {
  return (role as UserRole) || 'ACCOUNT_HOLDER';
};

// Utility function to build navigation items
const buildNavigationItems = (currentRole: UserRole): NavigationItem[] => {
  const config = NAVIGATION_CONFIG[currentRole] || NAVIGATION_CONFIG.ACCOUNT_HOLDER;
  
  return config.map(item => ({
    ...item,
    url: getRoleBasedUrl(currentRole, item.url),
  }));
};

// Custom hook for user state management
const useUserState = (user: any, role: string | null): UserState => {
  const [userState, setUserState] = useState<UserState>(DEFAULT_USER_STATE);

  useEffect(() => {
    if (user) {
      const { fullName } = extractUserName(user);
      setUserState({
        name: fullName,
        email: user.email || DEFAULT_USER_STATE.email,
        roleDisplay: role ? getRoleDisplayName(role as UserRole) : DEFAULT_USER_STATE.roleDisplay,
      });
    }
  }, [user, role]);

  return userState;
};

export function AppSidebar({ userRole = "ACCOUNT_HOLDER", ...props }: AppSidebarProps) {
  const { user, loading } = useUserAuthentication();
  const pathname = usePathname();
  const { setOpenMobile } = useSidebar();
  const isMobile = useMediaQuery("(max-width: 768px)");

  const currentRole = resolveCurrentRole(userRole);
  const navigationItems = buildNavigationItems(currentRole);
  const userState = useUserState(user, userRole);

  const isActiveRoute = (url: string) => {
    return pathname.startsWith(url);
  };

  const handleNavigationClick = () => {
    if (isMobile) {
      setOpenMobile(false);
    }
  };

  if (loading) {
    return (
      <Sidebar variant="inset" {...props}>
        <SidebarHeader>
          <div className="flex items-center justify-center p-4">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
          </div>
        </SidebarHeader>
      </Sidebar>
    );
  }

  const headerUrl = getRoleBasedUrl(currentRole, currentRole === 'BROKER' ? 'auctions' : 'policies');

  return (
    <Sidebar variant="inset" collapsible="icon" {...props}>
      <SidebarHeader className="border-b border-sidebar-border">
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              size="lg"
              asChild
              className="transition-colors duration-200"
            >
              <Link href={headerUrl} onClick={handleNavigationClick}>
                <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-black text-white shadow-sm">
                  <Image
                    src="/logo-short-light.svg"
                    alt="Zeeguros"
                    width={20}
                    height={20}
                    style={{ width: 'auto', height: 'auto' }}
                    className="transition-transform duration-200 group-hover:scale-110"
                  />
                </div>
                <div className="grid flex-1 text-left text-sm leading-tight group-data-[collapsible=icon]:sr-only">
                  <span className="truncate font-semibold text-sidebar-foreground">Zeeguros</span>
                  <span className="truncate text-xs text-sidebar-foreground/60">{userState.roleDisplay}</span>
                </div>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>

      <SidebarContent className="px-2 py-4">
        <SidebarMenu className="gap-1">
          {navigationItems.map((item) => {
            const isActive = isActiveRoute(item.url);
            return (
              <SidebarMenuItem key={item.title}>
                <SidebarMenuButton
                  asChild
                  tooltip={item.title}
                  isActive={isActive}
                  className={cn(
                    "group relative font-medium transition-all duration-200 ease-in-out",
                    "focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2",
                    "active:scale-95"
                  )}
                >
                  <Link href={item.url} onClick={handleNavigationClick}>
                    <item.icon className="size-4 shrink-0 transition-all duration-200 group-hover:scale-110" />
                    <span className="group-data-[collapsible=icon]:sr-only transition-colors duration-200">
                      {item.title}
                    </span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            );
          })}
        </SidebarMenu>
      </SidebarContent>

      <SidebarFooter className="border-t border-sidebar-border p-2">
        <NavUser user={{
          name: userState.name,
          email: userState.email
        }} />
      </SidebarFooter>
    </Sidebar>
  )
}