import {
  PaymentPeriod,
  GuaranteeType,
  AssetsType,
  PolicyType,
  PolicyStatus,
  GarageType,
  PartyRole,
  UsageType,
  FuelType,
  KmRange,
} from "@prisma/client";

export type Coverage = {
  id?: string;
  type: GuaranteeType;
  customName?: string;
  limit?: number;
  deductible?: number;
  description?: string;
};

export type PolicyData = {
  // Policy details
  policyNumber?: string;
  type: AssetsType | null;
  status: string;
  pdfUrl: string;
  insurerName?: string;
  policyType?: PolicyType | null;
  productName?: string | null;
  startDate?: Date | null;
  endDate?: Date | null;
  paymentPeriod?: PaymentPeriod | null;
  premium?: number | null;

  // Insured details
  insuredParties?: {
    personId?: string;
    fullName: string;
    dni: string;
    roles: PartyRole[];
  }[];

  // Asset details
  brand?: string;
  model?: string;
  year?: number | null;
  version?: string | null;
  chassisNumber?: string;
  licensePlate?: string;
  firstRegistrationDate?: Date | null;

  fuelType?: FuelType | null;
  usageType?: UsageType | null;
  kmPerYear?: KmRange | null;
  garageType?: GarageType | null;
  seats?: number | null;
  powerCv?: number | null;
  isLeased?: boolean;

  // Coverage details
  coverages?: Coverage[];
};