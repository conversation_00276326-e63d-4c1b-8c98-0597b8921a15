# Insured Parties Relationship Documentation

## Database Schema Relationship

The correct relationship structure in the Zeeguros application is:

```
User -> AccountHolderProfile -> InsuredParties (reusable by account holder)
User -> AccountHolderProfile -> Policies -> PolicyInsuredParty (join table) -> InsuredParties
```

### Key Models

1. **InsuredParty** - Belongs to AccountHolderProfile, reusable across policies
2. **PolicyInsuredParty** - Join table linking policies to insured parties (many-to-many)
3. **Policy** - Can have multiple insured parties through the join table

### Schema Details

```prisma
model AccountHolderProfile {
  id             String          @id
  userId         String          @unique
  policies       Policy[]        @relation("PolicyUploader")
  insuredParties InsuredParty[]  // Direct relationship - reusable parties
  // ... other fields
}

model InsuredParty {
  id                    String                 @id
  accountHolderId       String                 // Belongs to account holder
  role                  PartyRole
  firstName             String?
  lastName              String?
  displayName           String?
  identification        String                 @unique
  gender                Gender?
  birthDate             DateTime?
  driverLicenseNumber   String?
  // ... other fields
  
  accountHolder         AccountHolderProfile   @relation(fields: [accountHolderId], references: [id])
  policies              PolicyInsuredParty[]   // Many-to-many through join table
}

model Policy {
  id                    String                 @id
  accountHolderId       String?
  insuredParties        PolicyInsuredParty[]   // Many-to-many through join table
  // ... other fields
}

model PolicyInsuredParty {
  policyId              String
  insuredPartyId        String
  
  policy                Policy                 @relation(fields: [policyId], references: [id])
  insuredParty          InsuredParty          @relation(fields: [insuredPartyId], references: [id])
  
  @@id([policyId, insuredPartyId])
}
```

## Benefits of This Structure

1. **Reusability**: InsuredParties are created once per AccountHolder and can be reused across multiple policies
2. **Flexibility**: A policy can have multiple insured parties with different roles
3. **Data Integrity**: InsuredParties maintain their information even if removed from specific policies
4. **Efficiency**: No data duplication - same person can be insured in multiple policies

## Usage Examples

### 1. Fetching Insured Parties for a Policy

```typescript
import { usePolicyInsuredParties } from "@/features/policies/hooks/usePolicyInsuredParties";

function PolicyDetailsComponent({ policyId }: { policyId: string }) {
  const { insuredParties, isLoading, error } = usePolicyInsuredParties(policyId);
  
  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;
  
  return (
    <div>
      <h3>Partes Aseguradas ({insuredParties.length})</h3>
      {insuredParties.map(party => (
        <div key={party.id}>
          <p>{party.fullName} - {party.role}</p>
          <p>ID: {party.identification}</p>
        </div>
      ))}
    </div>
  );
}
```

### 2. Fetching All InsuredParties for an AccountHolder

```typescript
import { useAccountHolderInsuredParties } from "@/features/policies/hooks/usePolicyInsuredParties";

function ManageInsuredPartiesComponent({ accountHolderId }: { accountHolderId: string }) {
  const { insuredParties, isLoading, error } = useAccountHolderInsuredParties(accountHolderId);
  
  return (
    <div>
      <h3>Mis Partes Aseguradas</h3>
      {insuredParties.map(party => (
        <div key={party.id}>
          <p>{party.fullName}</p>
          <p>Puede ser usado en múltiples pólizas</p>
        </div>
      ))}
    </div>
  );
}
```

### 3. Server Actions

```typescript
// Get insured parties for a specific policy
const policyInsuredParties = await getPolicyInsuredParties(policyId);

// Get all insured parties for an account holder (reusable across policies)
const accountHolderParties = await getAccountHolderInsuredParties(accountHolderId);

// Associate existing insured party with a policy
await associateInsuredPartyWithPolicy(policyId, insuredPartyId);

// Remove association (doesn't delete the insured party)
await removeInsuredPartyFromPolicy(policyId, insuredPartyId);
```

## Database Queries

### Correct Query for Policy with InsuredParties

```typescript
const policy = await db.policy.findUnique({
  where: { id: policyId },
  include: {
    insuredParties: {
      include: {
        insuredParty: true  // This gets the actual InsuredParty data
      }
    }
  }
});

// Transform the data
const insuredParties = policy.insuredParties.map(pip => ({
  id: pip.insuredParty.id,
  fullName: `${pip.insuredParty.firstName} ${pip.insuredParty.lastName}`.trim(),
  identification: pip.insuredParty.identification,
  role: pip.insuredParty.role,
  // ... other fields
}));
```

## UI Implementation

The PolicyDetailsDrawer now correctly shows:

1. **Section Title**: "👥 Partes Aseguradas" instead of "👤 Tomador"
2. **Count Display**: "Total: X partes aseguradas"
3. **Individual Cards**: Each insured party in a separate card showing:
   - Name (with masking in broker mode)
   - Identification (with masking in broker mode)
   - Role (translated to Spanish)
   - Additional fields like gender, birth date, driver license (when available)

## Migration Notes

- The old "Tomador" concept is replaced with the more flexible "Partes Aseguradas"
- InsuredParties are now reusable across policies
- The join table `PolicyInsuredParty` manages the many-to-many relationship
- Data masking continues to work in broker mode for privacy protection

## Files Updated

- `src/types/policy.ts` - Updated PolicyInsuredParty interface
- `src/components/shared/PolicyDetailsDrawer.tsx` - Updated UI to show all insured parties
- `src/features/policies/services/policy-insured-parties.service.ts` - New service for managing relationships
- `src/features/policies/actions/get-policy-insured-parties.ts` - Server actions
- `src/features/policies/hooks/usePolicyInsuredParties.ts` - React hooks
- `src/app/api/account-holder/policies/list/route.ts` - Updated API response
