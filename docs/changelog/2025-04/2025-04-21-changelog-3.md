# Changelog - 2025-04-21

> **Quick Summary**
>
> **What was done?**
> - Removed Data Access Layer (DAL) from `src/lib/dal/` and updated all references
> - Removed Supabase types from `src/supabase/types.ts` and updated all references
> - Simplified server utility to use non-typed Supabase client
> - Updated all API routes to use the Supabase client directly
> - Updated all server components to use the Supabase client directly
> - Fixed Next.js 15 compatibility issues with async cookies API
> - Removed unused `auth-client.ts` file
> - Optimized user data fetching to prevent redundant API calls
> - Fixed hydration warnings by adding `suppressHydrationWarning` to the body element
> - Fixed image aspect ratio warning in the sidebar logo
> - Fixed password recovery functionality by using consistent redirect URLs
> - Added detailed error logging for authentication operations
>
> **Key benefits:**
> - Simplified codebase structure
> - Reduced complexity by removing unnecessary abstraction layers
> - Improved maintainability with direct Supabase client usage
> - Better compatibility with Next.js 15 and its async cookies API
> - Reduced code duplication
> - More straightforward authentication flow
> - Improved developer experience
> - Enhanced performance by eliminating redundant API calls
> - Cleaner console output with fewer debug messages
> - Eliminated React hydration warnings
> - Fixed UI warnings for better component rendering
> - Improved error handling with detailed error messages

## Table of Contents

- [Changelog - 2025-04-21](#changelog---2025-04-21)
  - [Table of Contents](#table-of-contents)
  - [Highlighted Changes](#highlighted-changes)
  - [Executive Summary](#executive-summary)
  - [Changes Made](#changes-made)
    - [1. Removal of Data Access Layer (DAL)](#1-removal-of-data-access-layer-dal)
    - [2. Removal of Supabase Types](#2-removal-of-supabase-types)
    - [3. Simplified Server Utility](#3-simplified-server-utility)
    - [4. Updated API Routes](#4-updated-api-routes)
    - [5. Updated Server Components](#5-updated-server-components)
    - [6. Next.js 15 Compatibility Fixes](#6-nextjs-15-compatibility-fixes)
    - [7. Removed Unused Auth Client](#7-removed-unused-auth-client)
    - [8. Optimized User Data Fetching](#8-optimized-user-data-fetching)
    - [9. Fixed Hydration Warnings](#9-fixed-hydration-warnings)
    - [10. Fixed Image Aspect Ratio Warning](#10-fixed-image-aspect-ratio-warning)
    - [11. Fixed Password Recovery Functionality](#11-fixed-password-recovery-functionality)
    - [12. Improved Error Handling](#12-improved-error-handling)
  - [Benefits of the Changes](#benefits-of-the-changes)
  - [Recommended Next Steps](#recommended-next-steps)
  - [Conclusion](#conclusion)

## Highlighted Changes

<table>
  <tr>
    <td width="33.33%" align="center">
      <h3>🧹</h3>
      <b>Code Cleanup</b>
      <p>Removed unnecessary abstraction layers</p>
    </td>
    <td width="33.33%" align="center">
      <h3>⚡</h3>
      <b>Simplified Architecture</b>
      <p>Direct Supabase client usage</p>
    </td>
    <td width="33.33%" align="center">
      <h3>🔄</h3>
      <b>Next.js 15 Compatibility</b>
      <p>Fixed async cookies API issues</p>
    </td>
  </tr>
</table>

## Executive Summary

This document details the code cleanup and simplification efforts made to the Zeeguros application:

1. Removal of the Data Access Layer (DAL) from `src/lib/dal/` and updating all references to use the Supabase client directly, simplifying the authentication flow and reducing unnecessary abstraction.

2. Removal of Supabase types from `src/supabase/types.ts` and updating all references, reducing complexity and maintenance overhead.

3. Simplification of the server utility to use a non-typed Supabase client, making the code more straightforward and easier to maintain.

4. Updates to all API routes to use the Supabase client directly, providing a more consistent approach to data access.

5. Updates to all server components to use the Supabase client directly, improving code clarity and reducing indirection.

6. Fixes for Next.js 15 compatibility issues with the async cookies API, ensuring the application works correctly with the latest version of Next.js.

7. Removal of the unused `auth-client.ts` file, reducing redundancy in the codebase.

8. Optimization of user data fetching to prevent redundant API calls, improving performance and reducing console logs.

9. Fixes for hydration warnings caused by browser extensions by adding `suppressHydrationWarning` to the body element.

10. Fixes for image aspect ratio warnings in the sidebar logo by properly setting style properties.

11. Fixes for password recovery functionality by using consistent redirect URLs and adding proper environment variables.

12. Improvements to error handling throughout the authentication flows with detailed error logging and better user-facing error messages.

## Changes Made

### 1. Removal of Data Access Layer (DAL)

The Data Access Layer (DAL) was removed from `src/lib/dal/` to simplify the codebase and reduce unnecessary abstraction:

- Removed the `src/lib/dal/auth.ts` file that contained authentication-related functions
- Updated all references to use the Supabase client directly
- Simplified the authentication flow by removing the extra layer of abstraction

Before:
```typescript
// Using DAL
import { getUser, requireAuth } from '@/lib/dal/auth'

export default async function DashboardPage() {
  await requireAuth()
  const user = await getUser()
  // ...
}
```

After:
```typescript
// Direct Supabase client usage
import { createClient } from '@/utils/supabase/server'

export default async function DashboardPage() {
  const supabase = await createClient()
  const { data: { user } } = await supabase.auth.getUser()

  if (!user) {
    redirect('/login')
  }
  // ...
}
```

### 2. Removal of Supabase Types

The Supabase types file `src/supabase/types.ts` was removed to simplify the codebase:

- Removed the `src/supabase/types.ts` file that contained database type definitions
- Updated all references to use the Supabase client without explicit typing
- Simplified the code by removing the need to maintain separate type definitions

Before:
```typescript
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import type { Database } from '@/supabase/types'

export async function createClient() {
  const cookieStore = cookies()

  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    // ...
  )
}
```

After:
```typescript
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

export async function createClient() {
  const cookieStore = await cookies()

  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    // ...
  )
}
```

### 3. Simplified Server Utility

The server utility was simplified to use a non-typed Supabase client:

- Updated `src/utils/supabase/server.ts` to use the Supabase client without explicit typing
- Made the function async to properly handle the async cookies API in Next.js 15
- Simplified the cookie handling logic

```typescript
// src/utils/supabase/server.ts
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

export async function createClient() {
  const cookieStore = await cookies()

  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name) {
          return cookieStore.get(name)?.value
        },
        set(name, value, options) {
          cookieStore.set(name, value, options)
        },
        remove(name, options) {
          cookieStore.set(name, '', { ...options, maxAge: 0 })
        },
      },
    }
  )
}
```

### 4. Updated API Routes

All API routes were updated to use the Supabase client directly:

- Updated `src/app/api/policies/route.ts`
- Updated `src/app/api/policies/count/route.ts`
- Updated `src/app/api/policies/[id]/route.ts`
- Updated `src/app/api/profile/route.ts`

Example of updated API route:
```typescript
// src/app/api/policies/route.ts
import { NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";

export const dynamic = "force-dynamic"; // No cache this route

export async function GET() {
  try {
    console.log('API Route - Fetching policies');

    const supabase = await createClient();
    const { data: session } = await supabase.auth.getSession();

    if (!session.session) {
      return NextResponse.json([], { status: 401 });
    }

    const { data, error } = await supabase
      .from('policies')
      .select('*')
      .eq('user_id', session.session.user.id)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('API Route - Error fetching policies:', error.message);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    console.log(`API Route - Fetched ${data?.length || 0} policies`);
    return NextResponse.json(data || []);
  } catch (error) {
    console.error('API Route - Unexpected error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 }
    );
  }
}
```

### 5. Updated Server Components

All server components were updated to use the Supabase client directly:

- Updated `src/app/(dashboard)/layout.tsx`
- Updated `src/app/(dashboard)/dashboard/page.tsx`
- Updated `src/app/(dashboard)/policies/page.tsx`
- Updated server actions in `src/lib/actions/policies.ts`
- Updated server actions in `src/app/login/actions.ts`
- Updated server actions in `src/app/signup/actions.ts`

Example of updated server component:
```typescript
// src/app/(dashboard)/layout.tsx
import { DashboardLayoutClient } from "./_components/dashboard-layout-client";
import { createClient } from "@/utils/supabase/server";
import { extractUserName } from "@/lib/user-metadata";

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser();
  const { firstName } = user ? extractUserName(user) : { firstName: "" };

  // Get user initials for avatar
  const userInitials = firstName && firstName.charAt(0) || "Z";

  return <DashboardLayoutClient userInitials={userInitials}>{children}</DashboardLayoutClient>;
}
```

### 6. Next.js 15 Compatibility Fixes

Fixed compatibility issues with Next.js 15's async cookies API:

- Updated `src/utils/supabase/server.ts` to properly handle the async cookies API
- Updated all server components and server actions to await the `createClient()` function call
- Updated the middleware to await the `createServerClient()` function call

```typescript
// src/utils/supabase/middleware.ts
import { createServerClient } from '@supabase/ssr'
import { NextResponse, type NextRequest } from 'next/server'

export async function updateSession(request: NextRequest) {
  let response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  })

  const supabase = await createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name) {
          return request.cookies.get(name)?.value
        },
        set(name, value, options) {
          // This is needed because we're setting cookies both in the current
          // response, and in the original request that will go to the server.
          // This allows for the request to have the updated cookies when it
          // reaches the server.
          request.cookies.set({
            name,
            value,
            ...options,
          })
          response = NextResponse.next({
            request: {
              headers: request.headers,
            },
          })
          response.cookies.set({
            name,
            value,
            ...options,
          })
        },
        remove(name, options) {
          // Same as above, we need to delete cookies from both the current
          // response, and the original request that will go to the server.
          request.cookies.delete(name)
          response = NextResponse.next({
            request: {
              headers: request.headers,
            },
          })
          response.cookies.delete(name)
        },
      },
    }
  )

  // This will refresh session if expired - required for Server Components
  // https://supabase.com/docs/guides/auth/server-side/nextjs
  const { data } = await supabase.auth.getSession()

  return response
}
```

### 7. Removed Unused Auth Client

The unused `auth-client.ts` file was removed to reduce redundancy in the codebase:

- Identified that `src/lib/auth-client.ts` was not being imported or used in any components
- Safely removed the file as its functionality was either not used or handled directly in components
- Kept the necessary client-side authentication utilities in other files

### 8. Optimized User Data Fetching

Optimized user data fetching to prevent redundant API calls:

- Created a shared context in the dashboard layout to store user data
- Modified child components to use the user data from the context instead of fetching it again
- Removed redundant calls to `supabase.auth.getUser()` and `extractUserName()`
- Removed debug console logs that were showing up multiple times

Before:
```typescript
// Dashboard Layout
export default async function DashboardLayout({ children }) {
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser();
  const { firstName } = user ? extractUserName(user) : { firstName: "" };
  return <DashboardLayoutClient userInitials={userInitials}>{children}</DashboardLayoutClient>;
}

// Dashboard Page (child component)
export default async function DashboardPage() {
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser(); // Redundant API call
  const userName = user ? extractUserName(user).fullName : "Usuario"; // Redundant extraction
  // ...
}
```

After:
```typescript
// Dashboard Layout with context
export const userDataContext = {
  user: null,
  userName: "",
  userInitials: ""
};

export default async function DashboardLayout({ children }) {
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser();
  const { firstName, fullName } = user ? extractUserName(user) : { firstName: "", fullName: "Usuario" };

  // Store user data in the context
  userDataContext.user = user;
  userDataContext.userName = fullName;
  userDataContext.userInitials = firstName.charAt(0) || "Z";

  return <DashboardLayoutClient userInitials={userDataContext.userInitials}>{children}</DashboardLayoutClient>;
}

// Dashboard Page (child component)
export default async function DashboardPage() {
  const supabase = await createClient();

  // Use the user from the context instead of fetching it again
  const userId = userDataContext.user?.id || '';
  const userName = userDataContext.userName;

  // ...
}
```

### 9. Fixed Hydration Warnings

Fixed hydration warnings caused by browser extensions:

- Added `suppressHydrationWarning` prop to the body element in the RootLayout component
- This prevents warnings caused by browser extensions that add attributes to the body element
- Specifically addressed the `cz-shortcut-listen="true"` attribute added by browser extensions

```typescript
// src/app/layout.tsx
export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // This suppresses the hydration warning caused by browser extensions
  // that add attributes to the body element
  const suppressHydrationWarning = true;

  return (
    <html lang="es">
      <body className={`font-sans ${inter.variable}`} suppressHydrationWarning={suppressHydrationWarning}>
        {children}
        <Toaster />
      </body>
    </html>
  );
}
```

### 10. Fixed Image Aspect Ratio Warning

Fixed image aspect ratio warning in the sidebar logo:

- Added `style={{ width: 'auto', height: 'auto' }}` to the Image component in the sidebar
- This ensures the SVG logo maintains its proper aspect ratio
- Addressed the warning: "Image with src has either width or height modified, but not the other"

```typescript
// src/components/app-sidebar.tsx
<Image
  src="/logo-zeeguros-b.svg"
  alt="Zeeguros"
  width={20}
  height={20}
  style={{ width: 'auto', height: 'auto' }}
/>
```

### 11. Fixed Password Recovery Functionality

Fixed issues with the password recovery functionality:

- Updated the redirect URL handling in the forgot password form to use a consistent approach
- Added environment variable `NEXT_PUBLIC_SITE_URL` for authentication redirects
- Ensured consistent URL format between signup and password reset flows
- Fixed the password reset email sending functionality
- Fixed the reset password page to properly handle both hash fragments and code query parameters
- Prevented automatic redirection to login page when a valid reset token is present

```typescript
// src/app/_components/auth/forgot-password-form.tsx
// Before
const { error } = await supabase.auth.resetPasswordForEmail(data.email, {
  redirectTo: `${window.location.origin}/reset-password`,
});

// After
const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || window.location.origin;
console.log('Using redirect URL:', `${siteUrl}/reset-password`);

const { error } = await supabase.auth.resetPasswordForEmail(data.email, {
  redirectTo: `${siteUrl}/reset-password`,
});

// src/app/_components/auth/reset-password-form.tsx
// Before
const checkHash = async () => {
  try {
    // Check for hash in URL
    const hash = window.location.hash.substring(1);
    if (!hash) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Enlace de restablecimiento inválido o expirado.",
      });
      setTimeout(() => {
        window.location.href = "/login";
      }, 3000);
    }
  } catch (error) {
    // Error handling...
  }
};

// After
const checkResetToken = async () => {
  try {
    // Check for hash in URL or code parameter
    const hash = window.location.hash.substring(1);
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');

    console.log('Reset password page loaded with:', { hash, code });

    if (!hash && !code) {
      console.error('No hash or code found in URL');
      toast({
        variant: "destructive",
        title: "Error",
        description: "Enlace de restablecimiento inválido o expirado.",
      });
      setTimeout(() => {
        window.location.href = "/login";
      }, 3000);
    }
  } catch (error) {
    // Error handling...
  }
};
```

### 12. Improved Error Handling

Improved error handling throughout the authentication flows:

- Added detailed error logging for authentication operations
- Enhanced error messages to include specific error details from Supabase
- Added console logging for easier debugging of authentication issues
- Improved user-facing error messages with more specific information

```typescript
// Before
if (error) {
  toast({
    variant: "destructive",
    title: "Error",
    description: "No se pudo enviar el correo. Por favor, inténtalo de nuevo.",
  });
  return;
}

// After
if (error) {
  console.error('Password reset error:', error);
  toast({
    variant: "destructive",
    title: "Error",
    description: `No se pudo enviar el correo: ${error.message || 'Error desconocido'}`,
  });
  return;
}
```

## Benefits of the Changes

1. **Simplified Codebase Structure**:
   - Removed unnecessary abstraction layers
   - Reduced complexity by eliminating indirection
   - Made the code more straightforward and easier to understand

2. **Improved Maintainability**:
   - Direct Supabase client usage makes the code more explicit
   - Reduced the number of files to maintain
   - Simplified the authentication flow

3. **Better Next.js 15 Compatibility**:
   - Fixed issues with the async cookies API
   - Ensured the application works correctly with the latest version of Next.js
   - Properly handled asynchronous operations in server components and middleware

4. **Reduced Code Duplication**:
   - Eliminated redundant code by using the Supabase client directly
   - Simplified the authentication flow by removing unnecessary abstractions
   - Consolidated similar functionality

5. **More Straightforward Authentication Flow**:
   - Direct use of Supabase authentication methods
   - Clearer code paths for authentication-related operations
   - Easier to understand and debug

6. **Improved Developer Experience**:
   - Less context switching between different abstraction layers
   - More intuitive code structure
   - Easier onboarding for new developers

## Recommended Next Steps

1. **Review and Test Authentication Flow**:
   - Ensure all authentication-related functionality works correctly
   - Test edge cases such as session expiration and token refresh
   - Verify that protected routes are properly secured

2. **Update Documentation**:
   - Update any documentation that references the removed DAL or types
   - Document the new approach to authentication and data access
   - Provide examples of how to use the Supabase client directly

3. **Consider Type Safety Improvements**:
   - Evaluate the need for type safety in database operations
   - Consider using Prisma or other tools for type-safe database access
   - Implement runtime validation for critical data structures

4. **Performance Optimization**:
   - Analyze the performance impact of the changes
   - Identify any bottlenecks in the authentication flow
   - Implement caching or other optimizations as needed

5. **Security Review**:
   - Conduct a security review of the authentication flow
   - Ensure proper error handling and validation
   - Implement additional security measures as needed

## Conclusion

The code cleanup and simplification efforts have resulted in a more maintainable and straightforward codebase. By removing unnecessary abstraction layers and simplifying the authentication flow, we have made the code easier to understand and maintain. The updates to handle Next.js 15's async cookies API ensure that the application works correctly with the latest version of Next.js.

These changes represent a significant improvement to the codebase structure and will make future development more efficient. The direct use of the Supabase client provides a more consistent approach to data access and authentication, reducing complexity and improving developer experience.

Overall, these changes have made the codebase more robust, maintainable, and easier to work with, setting a solid foundation for future development efforts.
