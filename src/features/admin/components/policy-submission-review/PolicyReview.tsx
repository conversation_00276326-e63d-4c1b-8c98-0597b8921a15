"use client";

import { Button } from "@/components/ui/button";
import Link from "next/link";

export function PolicyReview() {

  return (
    <div className="space-y-6">
      <div className="space-y-6">
        <h2 className="text-2xl font-semibold tracking-tight">¡Hecho! 💪</h2>
        <p className="text-muted-foreground">
          Nuestro equipo ya está revisando la información que nos has
          proporcionado para asegurarnos de que todo esté correcto. Te
          enviaremos un correo en cuanto hayamos validado los datos.
        </p>
        <p className="font-bold">¿Qué sucede después?</p>
        <ul>
          <li>
            <b>Revisión y validación</b>: Estamos verificando que toda la información
            esté completa y sea precisa.
          </li>
          <li>
            <b>Inicio de la subasta</b>: Una vez validada tu póliza, lanzaremos la
            subasta para encontrar la mejor oferta o condiciones en menos de 48
            horas.
          </li>
          <li>
            <b>Notificación de resultados</b>: Te avisaremos tan pronto tengamos la
            mejor propuesta para ti.
          </li>
        </ul>
        <p className="font-bold">🔔 No necesitas hacer nada más por ahora.</p>
        <div>
          <p className="text-muted-foreground">
            ¡Nos aseguraremos de que encuentres la mejor oferta posible!
          </p>

          <p className="text-muted-foreground">
            Si tienes alguna duda o necesitas asistencia, puedes contactarnos a
            través de nuestro correo de contacto: <EMAIL>.
          </p>
          <p className="text-muted-foreground">Estamos aquí para ayudarte.</p>

          <p className="text-muted-foreground">El equipo de Zeeguros 🚀</p>
        </div>
      </div>

      <div className="space-y-6">
        <div className="flex justify-between">
          <Button type="button" variant="default">
            <Link href="/policies">Volver a Mis Póliza</Link>
          </Button>
        </div>
      </div>
    </div>
  );
}
